
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Wallet, ArrowUpDown, TrendingUp, TrendingDown, Plus, RefreshCw } from 'lucide-react';
import { makeRequest } from '@/utils/api';

// Interfaces
interface CashOperation {
  id: string;
  transaction_code: string;
  type: 'deposit' | 'withdrawal';
  amount: number;
  description: string;
  reference_number?: string;
  processed_at: string;
  status: string;
  account_number: string;
  client_name: string;
}

interface SessionTotals {
  deposits: { amount: number; count: number };
  withdrawals: { amount: number; count: number };
  net_amount: number;
}

// Apenas denominações de 200 Kz para cima conforme requisitos do sistema
interface CashDenominations {
  notes_10000: number;
  notes_5000: number;
  notes_2000: number;
  notes_1000: number;
  notes_500: number;
  notes_200: number;
}

const Caixa = () => {
  const [operations, setOperations] = useState<CashOperation[]>([]);
  const [totals, setTotals] = useState<SessionTotals>({
    deposits: { amount: 0, count: 0 },
    withdrawals: { amount: 0, count: 0 },
    net_amount: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isOperationModalOpen, setIsOperationModalOpen] = useState(false);
  const [isSelectionModalOpen, setIsSelectionModalOpen] = useState(false);
  const [operationType, setOperationType] = useState<'deposit' | 'withdrawal'>('deposit');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState({
    account_number: '',
    amount: '',
    description: '',
    reference_number: ''
  });

  const [denominations, setDenominations] = useState<CashDenominations>({
    notes_10000: 0, notes_5000: 0, notes_2000: 0, notes_1000: 0,
    notes_500: 0, notes_200: 0
  });

  const { toast } = useToast();

  // Carregar operações ao montar componente
  useEffect(() => {
    loadOperations();
  }, []);

  const loadOperations = async () => {
    try {
      setIsLoading(true);
      const response = await makeRequest<{
        session: any;
        totals: SessionTotals;
        operations: CashOperation[];
      }>('/api/cash-registers/operations');

      setOperations(response.data.operations);
      setTotals(response.data.totals);
    } catch (error: any) {
      console.error('Erro ao carregar operações:', error);
      if (error.message.includes('sessão de caixa ativa')) {
        toast({
          title: "Sessão Inativa",
          description: "Você precisa abrir uma sessão de caixa primeiro",
          variant: "destructive"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value).replace('AOA', 'Kz');
  };

  const openOperationModal = (type: 'deposit' | 'withdrawal') => {
    setOperationType(type);
    setFormData({
      account_number: '',
      amount: '',
      description: type === 'deposit' ? 'Depósito em dinheiro' : 'Levantamento em dinheiro',
      reference_number: ''
    });
    setDenominations({
      notes_10000: 0, notes_5000: 0, notes_2000: 0, notes_1000: 0,
      notes_500: 0, notes_200: 0, notes_100: 0, notes_50: 0,
      coins_10: 0, coins_5: 0, coins_1: 0
    });
    setIsSelectionModalOpen(false); // Fechar modal de seleção
    setIsOperationModalOpen(true);
  };

  const openSelectionModal = () => {
    setIsSelectionModalOpen(true);
  };

  const calculateDenominationsTotal = () => {
    return Object.entries(denominations).reduce((total, [key, quantity]) => {
      const value = key.includes('notes_') ?
        parseInt(key.replace('notes_', '')) :
        parseInt(key.replace('coins_', ''));
      return total + (quantity * value);
    }, 0);
  };

  const handleSubmitOperation = async () => {
    try {
      // Validações básicas
      if (!formData.account_number.trim()) {
        toast({
          title: "Erro",
          description: "Número da conta é obrigatório",
          variant: "destructive"
        });
        return;
      }

      const amount = parseFloat(formData.amount);
      if (!amount || amount <= 0) {
        toast({
          title: "Erro",
          description: "Valor deve ser maior que zero",
          variant: "destructive"
        });
        return;
      }

      const denominationsTotal = calculateDenominationsTotal();
      if (Math.abs(denominationsTotal - amount) > 0.01) {
        toast({
          title: "Erro",
          description: `Total das denominações (${formatCurrency(denominationsTotal)}) não confere com o valor informado (${formatCurrency(amount)})`,
          variant: "destructive"
        });
        return;
      }

      setIsSubmitting(true);

      const endpoint = operationType === 'deposit'
        ? '/api/cash-registers/operations/deposit'
        : '/api/cash-registers/operations/withdrawal';

      const response = await makeRequest<{
        transaction: any;
      }>(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          account_number: formData.account_number,
          amount: amount,
          denominations: denominations,
          description: formData.description,
          reference_number: formData.reference_number || undefined
        })
      });

      toast({
        title: "Sucesso",
        description: `${operationType === 'deposit' ? 'Depósito' : 'Levantamento'} de ${formatCurrency(amount)} realizado com sucesso`,
      });

      // Limpar formulário e fechar modal
      setIsOperationModalOpen(false);
      setFormData({
        account_number: '',
        amount: '',
        description: operationType === 'deposit' ? 'Depósito em dinheiro' : 'Levantamento em dinheiro',
        reference_number: ''
      });
      setDenominations({
        notes_10000: 0, notes_5000: 0, notes_2000: 0, notes_1000: 0,
        notes_500: 0, notes_200: 0, notes_100: 0, notes_50: 0,
        coins_10: 0, coins_5: 0, coins_1: 0
      });

      // Recarregar operações
      await loadOperations();

    } catch (error: any) {
      console.error('Erro ao processar operação:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao processar operação. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Operações de Caixa</h1>
          <p className="text-gray-600 dark:text-gray-400">Depósitos, levantametos e movimentações diárias</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadOperations} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button className="flex items-center gap-2" onClick={openSelectionModal}>
            <Plus className="h-4 w-4" />
            Nova Operação
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Depósitos Hoje</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(totals.deposits.amount)}</div>
            <p className="text-xs text-muted-foreground">{totals.deposits.count} operações</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Levantamentos Hoje</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{formatCurrency(totals.withdrawals.amount)}</div>
            <p className="text-xs text-muted-foreground">{totals.withdrawals.count} operações</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Líquido</CardTitle>
            <ArrowUpDown className={`h-4 w-4 ${totals.net_amount >= 0 ? 'text-green-600' : 'text-red-600'}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${totals.net_amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totals.net_amount >= 0 ? '+' : ''}{formatCurrency(totals.net_amount)}
            </div>
            <p className="text-xs text-muted-foreground">Diferença da sessão</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Operações</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.deposits.count + totals.withdrawals.count}</div>
            <p className="text-xs text-muted-foreground">Nesta sessão</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Últimas Operações</CardTitle>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={() => openOperationModal('deposit')}>
                <TrendingUp className="h-4 w-4 mr-1" />
                Depósito
              </Button>
              <Button size="sm" variant="outline" onClick={() => openOperationModal('withdrawal')}>
                <TrendingDown className="h-4 w-4 mr-1" />
                Levantamento
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : operations.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Wallet className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Nenhuma operação realizada nesta sessão</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {operations.slice(0, 10).map((operation) => (
                  <div key={operation.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium dark:text-gray-100">
                        {operation.type === 'deposit' ? 'Depósito' : 'Levamntamento'} - {operation.client_name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {operation.account_number} • {new Date(operation.processed_at).toLocaleTimeString('pt-AO', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                      {operation.reference_number && (
                        <div className="text-xs text-gray-400">
                          Ref: {operation.reference_number}
                        </div>
                      )}
                    </div>
                    <div className={`font-bold ${operation.type === 'deposit' ? 'text-green-600' : 'text-red-600'}`}>
                      {operation.type === 'deposit' ? '+' : '-'}{formatCurrency(operation.amount)}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Resumo da Sessão</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{totals.deposits.count}</div>
                  <div className="text-sm text-green-600">Depósitos</div>
                </div>
                <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{totals.withdrawals.count}</div>
                  <div className="text-sm text-red-600">Levantamentos</div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-muted-foreground">Total Depósitos:</span>
                  <span className="font-medium text-green-600">{formatCurrency(totals.deposits.amount)}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-muted-foreground">Total Levantamento:</span>
                  <span className="font-medium text-red-600">{formatCurrency(totals.withdrawals.amount)}</span>
                </div>
                <div className="flex justify-between items-center pt-2 border-t">
                  <span className="font-medium">Saldo Líquido:</span>
                  <span className={`font-bold ${totals.net_amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {totals.net_amount >= 0 ? '+' : ''}{formatCurrency(totals.net_amount)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modal de Seleção de Operação */}
      <Dialog open={isSelectionModalOpen} onOpenChange={setIsSelectionModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Nova Operação</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <p className="text-center text-muted-foreground">
              Selecione o tipo de operação que deseja realizar:
            </p>

            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={() => openOperationModal('deposit')}
                className="h-20 flex-col gap-2 bg-green-600 hover:bg-green-700"
              >
                <TrendingUp className="h-6 w-6" />
                <span>Novo Depósito</span>
              </Button>

              <Button
                onClick={() => openOperationModal('withdrawal')}
                className="h-20 flex-col gap-2 bg-red-600 hover:bg-red-700"
              >
                <TrendingDown className="h-6 w-6" />
                <span>Novo Levantamento</span>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal de Operações */}
      <Dialog open={isOperationModalOpen} onOpenChange={setIsOperationModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {operationType === 'deposit' ? 'Nova Operação - Depósito' : 'Nova Operação - Levantamento'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Informações Básicas */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="account_number">Número da Conta *</Label>
                <Input
                  id="account_number"
                  value={formData.account_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, account_number: e.target.value }))}
                  placeholder="Ex: **********"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="amount">Valor (Kz) *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                  placeholder="0,00"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Descrição da operação"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reference_number">Número de Referência (Opcional)</Label>
              <Input
                id="reference_number"
                value={formData.reference_number}
                onChange={(e) => setFormData(prev => ({ ...prev, reference_number: e.target.value }))}
                placeholder="Referência externa"
              />
            </div>

            {/* Denominações */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Contagem de Denominações</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Notas Grandes</h4>
                  {[
                    { key: 'notes_10000', label: 'Notas de 10.000 Kz', value: 10000 },
                    { key: 'notes_5000', label: 'Notas de 5.000 Kz', value: 5000 },
                    { key: 'notes_2000', label: 'Notas de 2.000 Kz', value: 2000 },
                    { key: 'notes_1000', label: 'Notas de 1.000 Kz', value: 1000 }
                  ].map(({ key, label, value }) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-sm">{label}</span>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min="0"
                          className="w-20 text-center"
                          value={denominations[key as keyof CashDenominations]}
                          onChange={(e) => setDenominations(prev => ({
                            ...prev,
                            [key]: parseInt(e.target.value) || 0
                          }))}
                        />
                        <span className="text-sm text-muted-foreground w-24">
                          {formatCurrency((denominations[key as keyof CashDenominations] || 0) * value)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Notas Pequenas</h4>
                  {[
                    { key: 'notes_500', label: 'Notas de 500 Kz', value: 500 },
                    { key: 'notes_200', label: 'Notas de 200 Kz', value: 200 },
                    { key: 'notes_100', label: 'Notas de 100 Kz', value: 100 },
                    { key: 'notes_50', label: 'Notas de 50 Kz', value: 50 }
                  ].filter(({ key }) => {
                    // Para depósitos, remover moedas de 1, 5 e 10 Kz
                    if (operationType === 'deposit') {
                      return !['coins_1', 'coins_5', 'coins_10'].includes(key);
                    }
                    return true;
                  }).map(({ key, label, value }) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-sm">{label}</span>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min="0"
                          className="w-20 text-center"
                          value={denominations[key as keyof CashDenominations]}
                          onChange={(e) => setDenominations(prev => ({
                            ...prev,
                            [key]: parseInt(e.target.value) || 0
                          }))}
                        />
                        <span className="text-sm text-muted-foreground w-24">
                          {formatCurrency((denominations[key as keyof CashDenominations] || 0) * value)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Total Calculado */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Calculado:</span>
                  <span className="text-lg font-bold">
                    {formatCurrency(calculateDenominationsTotal())}
                  </span>
                </div>
              </div>
            </div>

            {/* Botões */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button variant="outline" onClick={() => setIsOperationModalOpen(false)}>
                Cancelar
              </Button>
              <Button
                onClick={handleSubmitOperation}
                disabled={isSubmitting}
                className={operationType === 'deposit' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
              >
                {isSubmitting ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : operationType === 'deposit' ? (
                  <TrendingUp className="h-4 w-4 mr-2" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-2" />
                )}
                {operationType === 'deposit' ? 'Confirmar Depósito' : 'Confirmar Levantamento'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Caixa;
