const express = require('express');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');
const { catchAsync, AppError } = require('../core/errorHandler');
const logger = require('../core/logger');
const cascadeDeleteService = require('../services/cascadeDeleteService');

const router = express.Router();

// =============================================
// SCHEMAS DE VALIDAÇÃO
// =============================================

// Schema para cliente individual
const individualClientSchema = Joi.object({
  full_name: Joi.string().min(2).max(255).required(),
  document_type: Joi.string().valid('BI', 'Passaporte', 'Cedula').required(),
  document_number: Joi.string().min(5).max(50).required(),
  nif: Joi.string().min(9).max(20).optional(),
  birth_date: Joi.date().max('now').required(),
  nationality: Joi.string().max(100).required(),
  gender: Joi.string().valid('M', 'F').required(),
  marital_status: Joi.string().valid('Solteiro', 'Casado', 'Divorciado', 'Viúvo', 'União de Facto').optional(),
  profession: Joi.string().max(100).optional(),
  monthly_income: Joi.number().positive().optional(),
  branch_id: Joi.number().integer().positive().required(),
  // Endereço
  address: Joi.object({
    province: Joi.string().max(100).required(),
    municipality: Joi.string().max(100).required(),
    neighborhood: Joi.string().max(100).optional(),
    street: Joi.string().max(255).required(),
    house_number: Joi.string().max(20).optional(),
    postal_code: Joi.string().max(20).optional()
  }).required(),
  // Contactos
  contacts: Joi.object({
    phone_personal: Joi.string().max(20).optional(),
    phone_work: Joi.string().max(20).optional(),
    email_personal: Joi.string().email().max(255).optional(),
    email_work: Joi.string().email().max(255).optional()
  }).required()
});

// Schema para cliente empresa
const companyClientSchema = Joi.object({
  company_name: Joi.string().min(2).max(255).required(),
  document_type: Joi.string().valid('NIF', 'Alvará').required(),
  document_number: Joi.string().min(5).max(50).required(),
  nif: Joi.string().min(9).max(20).required(),
  incorporation_date: Joi.date().max('now').required(),
  nationality: Joi.string().max(100).required(),
  profession: Joi.string().max(100).optional(),
  monthly_income: Joi.number().positive().optional(),
  branch_id: Joi.number().integer().positive().required(),
  // Endereço
  address: Joi.object({
    province: Joi.string().max(100).required(),
    municipality: Joi.string().max(100).required(),
    neighborhood: Joi.string().max(100).optional(),
    street: Joi.string().max(255).required(),
    house_number: Joi.string().max(20).optional(),
    postal_code: Joi.string().max(20).optional()
  }).required(),
  // Contactos
  contacts: Joi.object({
    phone_personal: Joi.string().max(20).optional(),
    phone_work: Joi.string().max(20).optional(),
    email_personal: Joi.string().email().max(255).optional(),
    email_work: Joi.string().email().max(255).optional()
  }).required()
});

// Schema para filtros de listagem
const listClientsSchema = Joi.object({
  search: Joi.string().max(255).optional(),
  client_type: Joi.string().valid('individual', 'company').optional(),
  status: Joi.string().valid('active', 'inactive', 'blocked').optional(),
  branch_id: Joi.number().integer().positive().optional(),
  start_date: Joi.date().optional(),
  end_date: Joi.date().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50000).default(20) // Aumentado para permitir exportações
});

// Schema para atualização de cliente
const updateClientSchema = Joi.object({
  full_name: Joi.string().min(2).max(255).optional(),
  company_name: Joi.string().min(2).max(255).optional(),
  nif: Joi.string().min(9).max(20).optional(),
  nationality: Joi.string().max(100).optional(),
  gender: Joi.string().valid('M', 'F').optional(),
  marital_status: Joi.string().valid('Solteiro', 'Casado', 'Divorciado', 'Viúvo', 'União de Facto').optional(),
  profession: Joi.string().max(100).optional(),
  monthly_income: Joi.number().positive().optional(),
  status: Joi.string().valid('active', 'inactive', 'blocked').optional()
});

// =============================================
// FUNÇÕES AUXILIARES
// =============================================

// Função para inserir endereço do cliente
const insertClientAddress = async (clientId, addressData) => {
  const query = `
    INSERT INTO client_addresses (client_id, address_type, province, municipality, neighborhood, street, house_number, postal_code, is_primary)
    VALUES (?, 'residential', ?, ?, ?, ?, ?, ?, TRUE)
  `;

  return await executeQuery(query, [
    clientId,
    addressData.province,
    addressData.municipality,
    addressData.neighborhood || null,
    addressData.street,
    addressData.house_number || null,
    addressData.postal_code || null
  ]);
};

// Função para inserir contactos do cliente
const insertClientContacts = async (clientId, contactsData) => {
  const contacts = [];

  if (contactsData.phone_personal) {
    contacts.push(['phone_personal', contactsData.phone_personal, true]);
  }
  if (contactsData.phone_work) {
    contacts.push(['phone_work', contactsData.phone_work, false]);
  }
  if (contactsData.email_personal) {
    contacts.push(['email_personal', contactsData.email_personal, !contactsData.email_work]);
  }
  if (contactsData.email_work) {
    contacts.push(['email_work', contactsData.email_work, true]);
  }

  for (const [type, value, isPrimary] of contacts) {
    const query = `
      INSERT INTO client_contacts (client_id, contact_type, contact_value, is_primary)
      VALUES (?, ?, ?, ?)
    `;
    await executeQuery(query, [clientId, type, value, isPrimary]);
  }
};

// =============================================
// ENDPOINTS DA API
// =============================================

/**
 * GET /api/clients
 * Listar clientes com filtros e paginação
 */
router.get('/', authorize('admin', 'gerente', 'caixa'), catchAsync(async (req, res, next) => {
  // 1. Validar parâmetros de consulta
  const { error, value } = listClientsSchema.validate(req.query);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { search, client_type, status, branch_id, start_date, end_date, page, limit } = value;
  const offset = (page - 1) * limit;

  // 2. Construir query base
  let baseQuery = `
    FROM clients c
    LEFT JOIN branches b ON c.branch_id = b.id
    LEFT JOIN users u ON c.created_by = u.id
    WHERE 1=1
  `;

  let countQuery = `SELECT COUNT(*) as total ${baseQuery}`;
  let dataQuery = `
    SELECT
      c.id,
      c.client_type,
      c.full_name,
      c.company_name,
      c.document_type,
      c.document_number,
      c.nif,
      c.birth_date,
      c.incorporation_date,
      c.nationality,
      c.gender,
      c.marital_status,
      c.profession,
      c.monthly_income,
      c.status,
      c.created_at,
      c.updated_at,
      b.name as branch_name,
      u.full_name as created_by_name
    ${baseQuery}
  `;

  const queryParams = [];

  // 3. Aplicar filtros
  if (search) {
    baseQuery += ` AND (
      c.full_name LIKE ? OR
      c.company_name LIKE ? OR
      c.document_number LIKE ? OR
      c.nif LIKE ?
    )`;
    const searchParam = `%${search}%`;
    queryParams.push(searchParam, searchParam, searchParam, searchParam);
  }

  if (client_type) {
    baseQuery += ` AND c.client_type = ?`;
    queryParams.push(client_type);
  }

  if (status) {
    baseQuery += ` AND c.status = ?`;
    queryParams.push(status);
  }

  if (branch_id) {
    baseQuery += ` AND c.branch_id = ?`;
    queryParams.push(branch_id);
  }

  if (start_date) {
    baseQuery += ` AND c.created_at >= ?`;
    queryParams.push(start_date);
  }

  if (end_date) {
    baseQuery += ` AND c.created_at <= ?`;
    queryParams.push(end_date);
  }

  // 4. Atualizar queries com filtros
  countQuery = `SELECT COUNT(*) as total ${baseQuery}`;
  dataQuery = `
    SELECT
      c.id,
      c.client_type,
      c.full_name,
      c.company_name,
      c.document_type,
      c.document_number,
      c.nif,
      c.birth_date,
      c.incorporation_date,
      c.nationality,
      c.gender,
      c.marital_status,
      c.profession,
      c.monthly_income,
      c.status,
      c.created_at,
      c.updated_at,
      b.name as branch_name,
      u.full_name as created_by_name
    ${baseQuery}
    ORDER BY c.created_at DESC
    LIMIT ? OFFSET ?
  `;

  // 5. Executar queries
  const [countResult, clients] = await Promise.all([
    executeQuery(countQuery, queryParams),
    executeQuery(dataQuery, [...queryParams, limit, offset])
  ]);

  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);

  // 6. Buscar contactos e endereços para cada cliente
  for (const client of clients) {
    // Buscar contacto principal
    const contacts = await executeQuery(
      'SELECT contact_type, contact_value FROM client_contacts WHERE client_id = ? AND is_primary = TRUE',
      [client.id]
    );

    client.primary_contact = contacts.reduce((acc, contact) => {
      if (contact.contact_type.includes('phone')) {
        acc.phone = contact.contact_value;
      } else if (contact.contact_type.includes('email')) {
        acc.email = contact.contact_value;
      }
      return acc;
    }, {});

    // Buscar endereço principal
    const addresses = await executeQuery(
      'SELECT province, municipality, street FROM client_addresses WHERE client_id = ? AND is_primary = TRUE LIMIT 1',
      [client.id]
    );

    client.primary_address = addresses[0] || null;
  }

  res.status(200).json({
    status: 'success',
    data: {
      clients,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_records: total,
        records_per_page: limit,
        has_next: page < totalPages,
        has_previous: page > 1
      }
    }
  });
}));

/**
 * POST /api/clients/individual
 * Criar cliente individual (pessoa física)
 */
router.post('/individual', authorize('admin', 'gerente', 'caixa'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = individualClientSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const {
    full_name, document_type, document_number, nif, birth_date,
    nationality, gender, marital_status, profession, monthly_income,
    branch_id, address, contacts
  } = value;

  // 2. Verificar se documento já existe
  const existingClient = await executeQuery(
    'SELECT id FROM clients WHERE document_number = ?',
    [document_number]
  );

  if (existingClient && existingClient.length > 0) {
    return next(new AppError('Número de documento já está em uso', 409, 'DOCUMENT_EXISTS'));
  }

  // 3. Verificar se NIF já existe (se fornecido)
  if (nif) {
    const existingNif = await executeQuery(
      'SELECT id FROM clients WHERE nif = ?',
      [nif]
    );

    if (existingNif && existingNif.length > 0) {
      return next(new AppError('NIF já está em uso', 409, 'NIF_EXISTS'));
    }
  }

  // 4. Verificar se balcão existe
  const branch = await executeQuery(
    'SELECT id FROM branches WHERE id = ? AND is_active = TRUE',
    [branch_id]
  );

  if (!branch || branch.length === 0) {
    return next(new AppError('Balcão não encontrado ou inativo', 404, 'BRANCH_NOT_FOUND'));
  }

  // 5. Criar cliente
  const clientId = uuidv4();
  const clientQuery = `
    INSERT INTO clients (
      id, client_type, full_name, document_type, document_number, nif,
      birth_date, nationality, gender, marital_status, profession,
      monthly_income, branch_id, status, created_by, created_at, updated_at
    ) VALUES (?, 'individual', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())
  `;

  await executeQuery(clientQuery, [
    clientId, full_name, document_type, document_number, nif,
    birth_date, nationality, gender, marital_status, profession,
    monthly_income, branch_id, req.user.id
  ]);

  // 6. Inserir endereço
  await insertClientAddress(clientId, address);

  // 7. Inserir contactos
  await insertClientContacts(clientId, contacts);

  // 8. Buscar cliente criado com dados completos
  const newClient = await executeQuery(`
    SELECT
      c.id, c.client_type, c.full_name, c.document_type, c.document_number,
      c.nif, c.birth_date, c.nationality, c.gender, c.marital_status,
      c.profession, c.monthly_income, c.status, c.created_at,
      b.name as branch_name
    FROM clients c
    LEFT JOIN branches b ON c.branch_id = b.id
    WHERE c.id = ?
  `, [clientId]);

  // 9. Log de auditoria
  logger.info(`Cliente individual criado: ${full_name}`, {
    clientId,
    createdBy: req.user.id,
    document_number,
    branch_id
  });

  res.status(201).json({
    status: 'success',
    message: 'Cliente individual criado com sucesso',
    data: {
      client: newClient[0]
    }
  });
}));

/**
 * POST /api/clients/company
 * Criar cliente empresa (pessoa jurídica)
 */
router.post('/company', authorize('admin', 'gerente', 'caixa'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = companyClientSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const {
    company_name, document_type, document_number, nif, incorporation_date,
    nationality, profession, monthly_income, branch_id, address, contacts
  } = value;

  // Debug: Log dos parâmetros recebidos
  console.log('DEBUG - Parâmetros recebidos:', {
    company_name, document_type, document_number, nif, incorporation_date,
    nationality, profession, monthly_income, branch_id, address, contacts
  });

  // 2. Verificar se documento já existe
  const existingClient = await executeQuery(
    'SELECT id FROM clients WHERE document_number = ?',
    [document_number]
  );

  if (existingClient && existingClient.length > 0) {
    return next(new AppError('Número de documento já está em uso', 409, 'DOCUMENT_EXISTS'));
  }

  // 3. Verificar se NIF já existe
  const existingNif = await executeQuery(
    'SELECT id FROM clients WHERE nif = ?',
    [nif]
  );

  if (existingNif && existingNif.length > 0) {
    return next(new AppError('NIF já está em uso', 409, 'NIF_EXISTS'));
  }

  // 4. Verificar se balcão existe
  const branch = await executeQuery(
    'SELECT id FROM branches WHERE id = ? AND is_active = TRUE',
    [branch_id]
  );

  if (!branch || branch.length === 0) {
    return next(new AppError('Balcão não encontrado ou inativo', 404, 'BRANCH_NOT_FOUND'));
  }

  // 5. Criar cliente empresa
  const clientId = uuidv4();
  const clientQuery = `
    INSERT INTO clients (
      id, client_type, company_name, document_type, document_number, nif,
      incorporation_date, nationality, profession, monthly_income,
      branch_id, status, created_by, created_at, updated_at
    ) VALUES (?, 'company', ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())
  `;

  // Debug: Log dos parâmetros da query
  const queryParams = [
    clientId, company_name, document_type, document_number, nif,
    incorporation_date, nationality, profession || null, monthly_income || null,
    branch_id, req.user.id
  ];
  console.log('DEBUG - Parâmetros da query:', queryParams);
  console.log('DEBUG - Parâmetros undefined:', queryParams.map((param, index) => param === undefined ? index : null).filter(x => x !== null));

  await executeQuery(clientQuery, queryParams);

  // 6. Inserir endereço
  await insertClientAddress(clientId, address);

  // 7. Inserir contactos
  await insertClientContacts(clientId, contacts);

  // 8. Buscar cliente criado com dados completos
  const newClient = await executeQuery(`
    SELECT
      c.id, c.client_type, c.company_name, c.document_type, c.document_number,
      c.nif, c.incorporation_date, c.nationality, c.profession,
      c.monthly_income, c.status, c.created_at,
      b.name as branch_name
    FROM clients c
    LEFT JOIN branches b ON c.branch_id = b.id
    WHERE c.id = ?
  `, [clientId]);

  // 9. Log de auditoria
  logger.info(`Cliente empresa criado: ${company_name}`, {
    clientId,
    createdBy: req.user.id,
    document_number,
    branch_id
  });

  res.status(201).json({
    status: 'success',
    message: 'Cliente empresa criado com sucesso',
    data: {
      client: newClient[0]
    }
  });
}));

/**
 * GET /api/clients/:id
 * Obter detalhes completos de um cliente
 */
router.get('/:id', authorize('admin', 'gerente', 'caixa'), catchAsync(async (req, res, next) => {
  const clientId = req.params.id;

  // 1. Buscar cliente
  const clients = await executeQuery(`
    SELECT
      c.id, c.client_type, c.full_name, c.company_name, c.document_type,
      c.document_number, c.nif, c.birth_date, c.incorporation_date,
      c.nationality, c.gender, c.marital_status, c.profession,
      c.monthly_income, c.status, c.created_at, c.updated_at,
      b.id as branch_id, b.name as branch_name, b.code as branch_code,
      u.full_name as created_by_name
    FROM clients c
    LEFT JOIN branches b ON c.branch_id = b.id
    LEFT JOIN users u ON c.created_by = u.id
    WHERE c.id = ?
  `, [clientId]);

  if (!clients || clients.length === 0) {
    return next(new AppError('Cliente não encontrado', 404, 'CLIENT_NOT_FOUND'));
  }

  const client = clients[0];

  // 2. Buscar endereços
  const addresses = await executeQuery(`
    SELECT id, address_type, province, municipality, neighborhood,
           street, house_number, postal_code, is_primary
    FROM client_addresses
    WHERE client_id = ?
    ORDER BY is_primary DESC, address_type
  `, [clientId]);

  // 3. Buscar contactos
  const contacts = await executeQuery(`
    SELECT id, contact_type, contact_value, is_primary
    FROM client_contacts
    WHERE client_id = ?
    ORDER BY is_primary DESC, contact_type
  `, [clientId]);

  // 4. Buscar documentos
  const documents = await executeQuery(`
    SELECT id, document_type, file_name, upload_date,
           u.full_name as uploaded_by_name
    FROM client_documents cd
    LEFT JOIN users u ON cd.uploaded_by = u.id
    WHERE cd.client_id = ?
    ORDER BY cd.upload_date DESC
  `, [clientId]);

  // 5. Buscar contas associadas (se existirem)
  const accounts = await executeQuery(`
    SELECT id, account_number, account_type, balance, status, created_at
    FROM accounts
    WHERE client_id = ?
    ORDER BY created_at DESC
  `, [clientId]);

  client.addresses = addresses;
  client.contacts = contacts;
  client.documents = documents;
  client.accounts = accounts;

  res.status(200).json({
    status: 'success',
    data: {
      client
    }
  });
}));

/**
 * PUT /api/clients/:id
 * Atualizar dados de um cliente
 */
router.put('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const clientId = req.params.id;

  // 1. Validar dados de entrada
  const { error, value } = updateClientSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  // 2. Verificar se cliente existe
  const existingClients = await executeQuery(
    'SELECT * FROM clients WHERE id = ?',
    [clientId]
  );

  if (!existingClients || existingClients.length === 0) {
    return next(new AppError('Cliente não encontrado', 404, 'CLIENT_NOT_FOUND'));
  }

  const existingClient = existingClients[0];

  // 3. Verificar se NIF já está em uso por outro cliente (se fornecido)
  if (value.nif && value.nif !== existingClient.nif) {
    const existingNif = await executeQuery(
      'SELECT id FROM clients WHERE nif = ? AND id != ?',
      [value.nif, clientId]
    );

    if (existingNif && existingNif.length > 0) {
      return next(new AppError('NIF já está em uso por outro cliente', 409, 'NIF_EXISTS'));
    }
  }

  // 4. Construir query de atualização
  const updateFields = [];
  const updateValues = [];

  Object.entries(value).forEach(([key, val]) => {
    if (val !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(val);
    }
  });

  if (updateFields.length === 0) {
    return next(new AppError('Nenhum campo para atualizar', 400, 'NO_FIELDS_TO_UPDATE'));
  }

  updateFields.push('updated_at = NOW()');
  updateValues.push(clientId);

  const updateQuery = `
    UPDATE clients
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `;

  // 5. Executar atualização
  await executeQuery(updateQuery, updateValues);

  // 6. Buscar cliente atualizado
  const updatedClient = await executeQuery(`
    SELECT
      c.id, c.client_type, c.full_name, c.company_name, c.document_type,
      c.document_number, c.nif, c.birth_date, c.incorporation_date,
      c.nationality, c.gender, c.marital_status, c.profession,
      c.monthly_income, c.status, c.created_at, c.updated_at,
      b.name as branch_name
    FROM clients c
    LEFT JOIN branches b ON c.branch_id = b.id
    WHERE c.id = ?
  `, [clientId]);

  // 7. Log de auditoria
  logger.info(`Cliente atualizado: ${clientId}`, {
    updatedBy: req.user.id,
    fields: Object.keys(value)
  });

  res.status(200).json({
    status: 'success',
    message: 'Cliente atualizado com sucesso',
    data: {
      client: updatedClient[0]
    }
  });
}));

/**
 * DELETE /api/clients/:id
 * Remover cliente e todos os dados relacionados em cascata (apenas Admin e Gerente)
 */
router.delete('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const clientId = req.params.id;

  try {
    // Usar o serviço de eliminação em cascata
    const result = await cascadeDeleteService.deleteClientCascade(clientId, req.user);

    res.status(200).json({
      status: 'success',
      message: 'Cliente e todos os dados relacionados eliminados com sucesso',
      data: {
        clientId: result.data.clientId,
        clientName: result.data.clientName,
        clientType: result.data.clientType,
        deletionStats: result.data.deletionStats
      }
    });
  } catch (error) {
    logger.error(`Erro na eliminação em cascata do cliente ${clientId}:`, error);

    // Verificar se é erro de cliente não encontrado
    if (error.message.includes('Cliente não encontrado')) {
      return next(new AppError('Cliente não encontrado', 404, 'CLIENT_NOT_FOUND'));
    }

    // Outros erros
    return next(new AppError(
      `Erro ao eliminar cliente: ${error.message}`,
      500,
      'CASCADE_DELETE_ERROR'
    ));
  }
}));

module.exports = router;
