import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { HandCoins, Calculator, CheckCircle, AlertCircle, Wallet, ArrowRight, Loader2 } from 'lucide-react';
import { counterService, CounterBalance } from '@/services/counterService';
import { OperationSummary } from '@/components/common/OperationSummary';
import { useAuth } from '@/contexts/AuthContext';

const EntregaTesoureiro = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [balance, setBalance] = useState<CounterBalance | null>(null);
  const [formData, setFormData] = useState({
    amount: '',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingBalance, setIsLoadingBalance] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Carregar saldo atual ao montar componente
  useEffect(() => {
    loadBalance();
  }, []);

  const loadBalance = async () => {
    try {
      setIsLoadingBalance(true);
      const balanceData = await counterService.getBalance();
      setBalance(balanceData);
    } catch (error) {
      console.error('Erro ao carregar saldo:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar o saldo do balcão",
        variant: "destructive"
      });
    } finally {
      setIsLoadingBalance(false);
    }
  };

  // Validar formulário
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const amount = parseFloat(formData.amount.replace(/[^\d,.-]/g, '').replace(',', '.'));

    if (!formData.amount || amount <= 0) {
      newErrors.amount = 'Valor deve ser maior que zero';
    } else if (balance && amount > balance.balance) {
      newErrors.amount = `Valor não pode ser superior ao saldo disponível (${counterService.formatCurrency(balance.balance)})`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Processar entrega
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      const amount = parseFloat(formData.amount.replace(/[^\d,.-]/g, '').replace(',', '.'));

      const deliveryData = {
        amount,
        notes: formData.notes || undefined
      };

      await counterService.deliverToTreasurer(deliveryData);

      toast({
        title: "Sucesso",
        description: `Entrega de ${counterService.formatCurrency(amount)} ao tesoureiro realizada com sucesso`
      });

      // Limpar formulário e recarregar saldo
      setFormData({ amount: '', notes: '' });
      setErrors({});
      await loadBalance();

    } catch (error: any) {
      console.error('Erro ao processar entrega:', error);
      toast({
        title: "Erro",
        description: error.response?.data?.message || "Erro ao processar entrega ao tesoureiro",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Formatação de valor em tempo real
  const handleAmountChange = (value: string) => {
    // Remove caracteres não numéricos exceto vírgula e ponto
    const numericValue = value.replace(/[^\d,.-]/g, '');
    setFormData(prev => ({ ...prev, amount: numericValue }));
    
    // Limpar erro do campo quando usuário digita
    if (errors.amount) {
      setErrors(prev => ({ ...prev, amount: '' }));
    }
  };

  const currentBalance = balance?.balance || 0;
  const entryAmount = parseFloat(formData.amount.replace(/[^\d,.-]/g, '').replace(',', '.')) || 0;
  const remainingBalance = currentBalance - entryAmount;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 p-6 rounded-xl shadow-lg">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          <HandCoins className="h-8 w-8" />
          Entrega ao Tesoureiro
        </h1>
        <p className="text-white/90">Entregar valores do balcão para a tesouraria</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Resumo da Operação */}
        <OperationSummary
          currentBalance={currentBalance}
          deliveryAmount={parseFloat(formData.amount.replace(/[^\d,.-]/g, '').replace(',', '.')) || 0}
          title="Resumo da Operação"
          formatCurrency={counterService.formatCurrency}
          showValidation={true}
          className="lg:col-span-1"
        />

        {/* Formulário de Entrega */}
        <Card className="lg:col-span-2 border-t-4 border-t-blue-500">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent dark:from-blue-900/20">
            <CardTitle className="text-lg font-semibold text-blue-700 dark:text-blue-400 flex items-center gap-2">
              <ArrowRight className="h-5 w-5" />
              Dados da Entrega
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Valor a Entregar */}
              <div className="space-y-2">
                <Label htmlFor="amount" className="text-sm font-medium">
                  Valor a Entregar (AOA) *
                </Label>
                <div className="relative">
                  <Input
                    id="amount"
                    type="text"
                    placeholder="0,00"
                    value={formData.amount}
                    onChange={(e) => handleAmountChange(e.target.value)}
                    className={`text-lg font-semibold ${errors.amount ? 'border-red-500' : ''}`}
                    disabled={isSubmitting || isLoadingBalance}
                  />
                  <Calculator className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
                {errors.amount && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.amount}
                  </p>
                )}
              </div>

              {/* Resumo da Operação */}
              {entryAmount > 0 && (
                <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                  <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Resumo da Operação</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Saldo Atual:</span>
                      <span className="font-medium">{counterService.formatCurrency(currentBalance)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Valor a Entregar:</span>
                      <span className="font-medium text-red-600">-{counterService.formatCurrency(entryAmount)}</span>
                    </div>
                    <hr className="border-blue-300 dark:border-blue-700" />
                    <div className="flex justify-between font-semibold">
                      <span>Saldo Restante:</span>
                      <span className={remainingBalance >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {counterService.formatCurrency(remainingBalance)}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="notes" className="text-sm font-medium">
                  Observações
                </Label>
                <Textarea
                  id="notes"
                  placeholder="Observações sobre a entrega (opcional)"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                  disabled={isSubmitting}
                />
              </div>

              {/* Alertas */}
              {remainingBalance < 0 && entryAmount > 0 && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    O valor a entregar excede o saldo disponível. Ajuste o valor para continuar.
                  </AlertDescription>
                </Alert>
              )}

              {/* Botões */}
              <div className="flex gap-4 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setFormData({ amount: '', notes: '' });
                    setErrors({});
                  }}
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  Limpar
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || isLoadingBalance || remainingBalance < 0 || entryAmount <= 0}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processando...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Confirmar Entrega
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EntregaTesoureiro;
