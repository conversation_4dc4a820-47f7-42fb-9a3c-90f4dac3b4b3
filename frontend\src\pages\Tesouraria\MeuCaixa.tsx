import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { treasuryService } from '@/services/treasuryService';
import { Wallet, TrendingUp, TrendingDown, RefreshCw, Calendar, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Componente principal da página "Meu Caixa"
const MeuCaixa: React.FC = () => {
  const { toast } = useToast();
  const [balance, setBalance] = useState<any>(null);
  const [movements, setMovements] = useState<any[]>([]);
  const [pagination, setPagination] = useState<any>({ page: 1, limit: 20, total: 0, totalPages: 0 });
  const [isLoadingBalance, setIsLoadingBalance] = useState(true);
  const [isLoadingMovements, setIsLoadingMovements] = useState(true);
  
  // Filtros
  const [filters, setFilters] = useState({
    movement_type: 'all',
    start_date: '',
    end_date: ''
  });

  // Carregar saldo do tesoureiro
  const loadBalance = async () => {
    setIsLoadingBalance(true);
    try {
      const data = await treasuryService.getMyBalance();
      setBalance(data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar saldo',
        description: error.message || 'Não foi possível carregar o saldo do tesoureiro'
      });
    } finally {
      setIsLoadingBalance(false);
    }
  };

  // Carregar movimentações
  const loadMovements = async (page: number = 1) => {
    setIsLoadingMovements(true);
    try {
      const params: any = { page, limit: pagination.limit };

      if (filters.movement_type && filters.movement_type !== 'all') params.movement_type = filters.movement_type;
      if (filters.start_date) params.start_date = filters.start_date;
      if (filters.end_date) params.end_date = filters.end_date;

      const data = await treasuryService.getMyMovements(params);
      setMovements(data.movements);
      setPagination(data.pagination);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar movimentações',
        description: error.message || 'Não foi possível carregar o histórico de movimentações'
      });
    } finally {
      setIsLoadingMovements(false);
    }
  };

  // Aplicar filtros
  const handleApplyFilters = () => {
    loadMovements(1);
  };

  // Limpar filtros
  const handleClearFilters = () => {
    setFilters({ movement_type: 'all', start_date: '', end_date: '' });
    setTimeout(() => loadMovements(1), 100);
  };

  // Atualizar dados
  const handleRefresh = () => {
    loadBalance();
    loadMovements(pagination.page);
  };

  useEffect(() => {
    loadBalance();
    loadMovements();
  }, []);

  // Formatar valor monetário
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Formatar data
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Meu Caixa</h1>
          <p className="text-muted-foreground">Gerencie o seu saldo e visualize o histórico de movimentações</p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Atualizar
        </Button>
      </div>

      {/* Card de Saldo */}
      <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Wallet className="mr-2 h-5 w-5" />
            Saldo Disponível
          </CardTitle>
          <CardDescription className="text-blue-100">
            Valor total disponível para entregas
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingBalance ? (
            <div className="text-3xl font-bold">Carregando...</div>
          ) : (
            <>
              <div className="text-4xl font-bold mb-2">
                {balance ? formatCurrency(parseFloat(balance.current_balance)) : 'AOA 0,00'}
              </div>
              {balance && balance.branch_name && (
                <div className="text-sm text-blue-100">
                  Agência: {balance.branch_name} {balance.branch_code && `(${balance.branch_code})`}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="movement_type">Tipo de Movimento</Label>
              <Select
                value={filters.movement_type}
                onValueChange={(value) => setFilters({ ...filters, movement_type: value })}
              >
                <SelectTrigger id="movement_type">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="credit">Crédito (Entrada)</SelectItem>
                  <SelectItem value="debit">Débito (Saída)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="start_date">Data Inicial</Label>
              <Input
                id="start_date"
                type="date"
                value={filters.start_date}
                onChange={(e) => setFilters({ ...filters, start_date: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">Data Final</Label>
              <Input
                id="end_date"
                type="date"
                value={filters.end_date}
                onChange={(e) => setFilters({ ...filters, end_date: e.target.value })}
              />
            </div>

            <div className="space-y-2 flex items-end gap-2">
              <Button onClick={handleApplyFilters} className="flex-1">
                Aplicar
              </Button>
              <Button onClick={handleClearFilters} variant="outline">
                Limpar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Movimentações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Histórico de Movimentações
          </CardTitle>
          <CardDescription>
            Total de {pagination.total} movimentação(ões) encontrada(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingMovements ? (
            <div className="text-center py-8 text-muted-foreground">Carregando movimentações...</div>
          ) : movements.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">Nenhuma movimentação encontrada</div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data/Hora</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Descrição</TableHead>
                      <TableHead>Origem</TableHead>
                      <TableHead className="text-right">Saldo Anterior</TableHead>
                      <TableHead className="text-right">Valor</TableHead>
                      <TableHead className="text-right">Saldo Posterior</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {movements.map((movement) => (
                      <TableRow key={movement.id}>
                        <TableCell className="font-medium">
                          {formatDate(movement.created_at)}
                        </TableCell>
                        <TableCell>
                          {movement.movement_type === 'credit' ? (
                            <Badge className="bg-green-500 hover:bg-green-600">
                              <TrendingUp className="mr-1 h-3 w-3" />
                              Crédito
                            </Badge>
                          ) : (
                            <Badge className="bg-red-500 hover:bg-red-600">
                              <TrendingDown className="mr-1 h-3 w-3" />
                              Débito
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {movement.description}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{movement.source_type}</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(parseFloat(movement.balance_before))}
                        </TableCell>
                        <TableCell className={`text-right font-semibold ${
                          movement.movement_type === 'credit' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {movement.movement_type === 'credit' ? '+' : '-'}
                          {formatCurrency(parseFloat(movement.amount))}
                        </TableCell>
                        <TableCell className="text-right font-semibold">
                          {formatCurrency(parseFloat(movement.balance_after))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Paginação */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Página {pagination.page} de {pagination.totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadMovements(pagination.page - 1)}
                      disabled={pagination.page === 1}
                    >
                      Anterior
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadMovements(pagination.page + 1)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Próxima
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MeuCaixa;

