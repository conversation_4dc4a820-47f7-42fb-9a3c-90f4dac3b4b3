import { API_CONFIG, API_ENDPOINTS } from '@/config/api';

// Helper para fazer requisições HTTP com retry
const makeRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retries: number = 3,
  delay: number = 1000
): Promise<{ data?: T; status: string; message?: string }> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;

  const token = localStorage.getItem('twins-bank-token');
  const config: RequestInit = {
    ...options,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Erro HTTP ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (attempt === retries) {
        throw error;
      }

      // Aguardar antes de tentar novamente
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw new Error('Falha após múltiplas tentativas');
};

export interface AccountApplication {
  id?: string;
  client_id: string;
  account_type: 'corrente' | 'poupanca' | 'salario' | 'junior';
  currency_id: number;
  initial_deposit?: number;
  overdraft_limit?: number;
  branch_id: number;
  status?: 'pending' | 'approved' | 'rejected' | 'active';
  created_at?: string;
  updated_at?: string;
}

export interface Account {
  id: string;
  account_number: string;
  account_type: 'corrente' | 'poupanca' | 'salario' | 'junior';
  currency_id: number;
  balance: number;
  available_balance: number;
  overdraft_limit: number;
  status: 'active' | 'inactive' | 'blocked' | 'closed';
  opening_date: string;
  closing_date?: string;
  branch_id: number;
  branch_name?: string;
  primary_holder_name?: string;
  primary_holder_company?: string;
  primary_holder_type?: string;
  currency_code?: string;
  currency_symbol?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  holders?: AccountHolder[];
}

export interface AccountHolder {
  id: number;
  account_id: string;
  client_id: string;
  client_name?: string;
  full_name?: string;
  company_name?: string;
  client_type?: string;
  document_type?: string;
  document_number?: string;
  holder_type: 'primary' | 'secondary';
  signature_path?: string;
  created_at: string;
}

// Interface CreateAccountRequest removida
// Conforme Tarefa 7: Remoção da Funcionalidade 'Criar Conta Direta'

export interface AccountListResponse {
  accounts: Account[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    records_per_page: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

export interface AccountFilters {
  search?: string;
  account_type?: string;
  status?: string;
  branch_id?: number;
  client_id?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

class AccountService {
  private getAuthHeaders() {
    const token = localStorage.getItem('twins-bank-token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  // Método createAccount removido
  // Conforme Tarefa 7: Remoção da Funcionalidade 'Criar Conta Direta'

  /**
   * Listar contas com filtros
   */
  async getAccounts(filters: AccountFilters = {}): Promise<AccountListResponse> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const endpoint = `${API_ENDPOINTS.ACCOUNTS.LIST}?${params.toString()}`;
    const response = await makeRequest<AccountListResponse>(endpoint, { method: 'GET' });
    return response.data!;
  }

  /**
   * Obter conta específica por ID
   */
  async getAccount(id: string): Promise<Account> {
    const endpoint = API_ENDPOINTS.ACCOUNTS.UPDATE(id);
    const response = await makeRequest<{ account: Account }>(endpoint, { method: 'GET' });
    return response.data!.account;
  }

  /**
   * Obter contas de um cliente específico
   */
  async getClientAccounts(clientId: string): Promise<Account[]> {
    const response = await this.getAccounts({ client_id: clientId, limit: 100 });
    return response.accounts;
  }

  /**
   * Atualizar conta
   */
  async updateAccount(id: string, updates: Partial<Account>): Promise<Account> {
    const endpoint = API_ENDPOINTS.ACCOUNTS.UPDATE(id);
    const response = await makeRequest<{ account: Account }>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(updates)
    });
    return response.data!.account;
  }

  /**
   * Bloquear conta
   */
  async blockAccount(id: string, reason?: string): Promise<Account> {
    return this.updateAccount(id, { 
      status: 'blocked',
      // Note: In a real implementation, you'd also send the reason
    });
  }

  /**
   * Desbloquear conta
   */
  async unblockAccount(id: string): Promise<Account> {
    return this.updateAccount(id, { status: 'active' });
  }

  /**
   * Fechar conta
   */
  async closeAccount(id: string): Promise<Account> {
    return this.updateAccount(id, {
      status: 'closed',
      closing_date: new Date().toISOString().split('T')[0]
    });
  }

  /**
   * Adicionar segundo titular a uma conta
   */
  async addSecondHolder(accountId: string, clientId: string): Promise<{ status: string; message: string; data: any }> {
    const endpoint = `${API_ENDPOINTS.ACCOUNTS.BASE}/${accountId}/add-holder`;
    const response = await makeRequest<{ status: string; message: string; data: any }>(endpoint, {
      method: 'POST',
      body: JSON.stringify({ client_id: clientId })
    });

    if (response.status !== 'success') {
      throw new Error(response.message || 'Erro ao adicionar segundo titular');
    }

    return response;
  }

  /**
   * Obter tipos de conta disponíveis
   */
  getAccountTypes() {
    return [
      { value: 'corrente', label: 'Conta Corrente', description: 'Para movimentações diárias' },
      { value: 'poupanca', label: 'Conta Poupança', description: 'Para poupanças com rendimento' },
      { value: 'salario', label: 'Conta Salário', description: 'Para recebimento de salário' },
      { value: 'junior', label: 'Conta Jovem', description: 'Para clientes até 25 anos' }
    ];
  }

  // Método validateAccountData removido
  // Conforme Tarefa 7: Remoção da Funcionalidade 'Criar Conta Direta'

  /**
   * Gerar número de conta (simulação)
   */
  generateAccountNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${timestamp.slice(-7)}${random}`;
  }

  /**
   * Calcular taxas de manutenção por tipo de conta
   */
  getMaintenanceFees() {
    return {
      corrente: 2500, // 2.500 AOA
      poupanca: 0,    // Gratuita
      salario: 0,     // Gratuita
      junior: 0       // Gratuita
    };
  }

  /**
   * Obter limites padrão por tipo de conta
   */
  getDefaultLimits() {
    return {
      corrente: {
        daily_withdrawal: 500000,    // 500.000 AOA
        daily_transfer: 1000000,     // 1.000.000 AOA
        overdraft_limit: 100000      // 100.000 AOA
      },
      poupanca: {
        daily_withdrawal: 200000,    // 200.000 AOA
        daily_transfer: 500000,      // 500.000 AOA
        overdraft_limit: 0           // Sem descoberto
      },
      salario: {
        daily_withdrawal: 300000,    // 300.000 AOA
        daily_transfer: 500000,      // 500.000 AOA
        overdraft_limit: 0           // Sem descoberto
      },
      junior: {
        daily_withdrawal: 100000,    // 100.000 AOA
        daily_transfer: 200000,      // 200.000 AOA
        overdraft_limit: 0           // Sem descoberto
      }
    };
  }
}

export const accountService = new AccountService();
