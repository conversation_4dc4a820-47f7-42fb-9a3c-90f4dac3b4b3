import React from 'react';
import { DataTable, Column } from '@/components/ui/data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  User,
  Building2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Eye,
  Edit,
  Trash2,
  DollarSign
} from 'lucide-react';
import { Client } from '@/types/client';
import { formatDate } from '@/utils/dateUtils';

interface ClientDataTableProps {
  clients: Client[];
  loading?: boolean;
  pagination?: {
    current_page: number;
    total_pages: number;
    total_records: number;
    records_per_page: number;
    has_next: boolean;
    has_previous: boolean;
  };
  onPageChange?: (page: number) => void;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, string>) => void;
  onViewClient?: (client: Client) => void;
  onEditClient?: (client: Client) => void;
  onDeleteClient?: (client: Client) => void;
}

const ClientDataTable: React.FC<ClientDataTableProps> = ({
  clients,
  loading = false,
  pagination,
  onPageChange,
  onSort,
  onFilter,
  onViewClient,
  onEditClient,
  onDeleteClient,
}) => {
  // Usar utilitário centralizado para formatação de data
  // A função formatDate já está importada do dateUtils

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'default',
      inactive: 'secondary',
      blocked: 'destructive'
    } as const;

    const labels = {
      active: 'Ativo',
      inactive: 'Inativo',
      blocked: 'Bloqueado'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  const getClientTypeIcon = (clientType: string) => {
    return clientType === 'individual' ? (
      <User className="h-4 w-4 text-blue-600" />
    ) : (
      <Building2 className="h-4 w-4 text-purple-600" />
    );
  };

  const columns: Column<Client>[] = [
    {
      key: 'client_type',
      title: 'Tipo',
      sortable: true,
      width: '80px',
      render: (value: string) => (
        <div className="flex items-center justify-center">
          {getClientTypeIcon(value)}
        </div>
      )
    },
    {
      key: 'full_name',
      title: 'Nome/Empresa',
      sortable: true,
      render: (value: string, row: Client) => (
        <div className="space-y-1">
          <div className="font-medium">
            {row.full_name || row.company_name}
          </div>
          <div className="text-sm text-gray-500">
            {row.document_type} {row.document_number}
          </div>
        </div>
      )
    },
    {
      key: 'nif',
      title: 'NIF',
      sortable: true,
      width: '120px',
      render: (value: string) => value || '-'
    },
    {
      key: 'primary_contact',
      title: 'Contacto',
      width: '200px',
      render: (value: any, row: Client) => (
        <div className="space-y-1">
          {row.primary_contact?.phone && (
            <div className="flex items-center gap-1 text-sm">
              <Phone className="h-3 w-3 text-green-600" />
              <span>{row.primary_contact.phone}</span>
            </div>
          )}
          {row.primary_contact?.email && (
            <div className="flex items-center gap-1 text-sm">
              <Mail className="h-3 w-3 text-blue-600" />
              <span className="truncate">{row.primary_contact.email}</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'primary_address',
      title: 'Localização',
      width: '180px',
      render: (value: any, row: Client) => (
        <div className="space-y-1">
          {row.primary_address && (
            <div className="flex items-start gap-1 text-sm">
              <MapPin className="h-3 w-3 text-red-600 mt-0.5 flex-shrink-0" />
              <span className="truncate">
                {row.primary_address.municipality}, {row.primary_address.province}
              </span>
            </div>
          )}
          <div className="text-xs text-gray-500">
            {row.branch_name}
          </div>
        </div>
      )
    },
    {
      key: 'monthly_income',
      title: 'Rendimento',
      sortable: true,
      width: '120px',
      render: (value: number) => (
        <div className="text-sm">
          {value ? (
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3 text-green-600" />
              <span>{formatCurrency(value)}</span>
            </div>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      width: '100px',
      render: (value: string) => getStatusBadge(value)
    },
    {
      key: 'created_at',
      title: 'Criado em',
      sortable: true,
      width: '120px',
      render: (value: string) => (
        <div className="flex items-center gap-1 text-sm">
          <Calendar className="h-3 w-3 text-gray-400" />
          <span>{formatDate(value)}</span>
        </div>
      )
    }
  ];

  const actions = [
    {
      key: 'view',
      label: 'Ver Detalhes',
      icon: <Eye className="h-4 w-4" />
    },
    {
      key: 'edit',
      label: 'Editar',
      icon: <Edit className="h-4 w-4" />
    },
    {
      key: 'delete',
      label: 'Excluir',
      icon: <Trash2 className="h-4 w-4" />
    }
  ];

  const handleRowAction = (action: string, client: Client) => {
    switch (action) {
      case 'view':
        onViewClient?.(client);
        break;
      case 'edit':
        onEditClient?.(client);
        break;
      case 'delete':
        onDeleteClient?.(client);
        break;
    }
  };

  return (
    <DataTable
      data={clients}
      columns={columns}
      loading={loading}
      pagination={pagination}
      onPageChange={onPageChange}
      onSort={onSort}
      onFilter={onFilter}
      onRowAction={handleRowAction}
      actions={actions}
      selectable={true}
      searchPlaceholder="Pesquisar por nome, documento ou NIF..."
      emptyMessage="Nenhum cliente encontrado. Tente ajustar os filtros de pesquisa."
    />
  );
};

export default ClientDataTable;
