// Componente de diálogo de confirmação elegante
// Substitui window.confirm() por interface moderna

import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';
import { ConfirmDialogOptions } from '@/hooks/useConfirmDialog';

interface ConfirmDialogProps {
  isOpen: boolean;
  options: ConfirmDialogOptions | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const getDefaultIcon = (variant: string) => {
  switch (variant) {
    case 'destructive':
      return <AlertTriangle className="h-6 w-6 text-red-500" />;
    case 'success':
      return <CheckCircle className="h-6 w-6 text-green-500" />;
    case 'warning':
      return <XCircle className="h-6 w-6 text-yellow-500" />;
    default:
      return <Info className="h-6 w-6 text-blue-500" />;
  }
};

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  options,
  onConfirm,
  onCancel,
}) => {
  if (!options) return null;

  const icon = options.icon || getDefaultIcon(options.variant || 'default');

  return (
    <AlertDialog open={isOpen} onOpenChange={(open) => !open && onCancel()}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            {icon}
            <AlertDialogTitle className="text-lg font-semibold">
              {options.title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            {options.description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex gap-2 sm:gap-2">
          <AlertDialogCancel onClick={onCancel} className="flex-1">
            {options.cancelText || 'Cancelar'}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className={`flex-1 ${
              options.variant === 'destructive'
                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
            }`}
          >
            {options.confirmText || 'Confirmar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ConfirmDialog;
