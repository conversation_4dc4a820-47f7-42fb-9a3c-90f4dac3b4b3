import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Banknote, MapPin, Wifi, Battery, AlertTriangle, CheckCircle, RefreshCw, Settings, Power, Wrench } from 'lucide-react';
import { atmService, ATM as ATMType } from '@/services/atmService';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

const ATM = () => {
  const { hasRole } = useAuth();
  const [atms, setAtms] = useState<ATMType[]>([]);
  const [loading, setLoading] = useState(true);
  const [updatingStatus, setUpdatingStatus] = useState<number | null>(null);

  useEffect(() => {
    loadATMs();
  }, []);

  const loadATMs = async () => {
    try {
      setLoading(true);
      const response = await atmService.getATMs();
      setAtms(response.atms);
    } catch (error: any) {
      console.error('Erro ao carregar ATMs:', error);
      toast.error('Erro ao carregar ATMs');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (atmId: number, newStatus: 'online' | 'offline' | 'maintenance') => {
    try {
      setUpdatingStatus(atmId);
      await atmService.updateATMStatus(atmId, newStatus);
      toast.success('Estado do ATM atualizado com sucesso');
      loadATMs();
    } catch (error: any) {
      console.error('Erro ao atualizar estado:', error);
      toast.error(error.message || 'Erro ao atualizar estado do ATM');
    } finally {
      setUpdatingStatus(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'offline':
        return <Power className="h-4 w-4 text-red-600" />;
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-orange-600" />;
      case 'low_balance':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const config = {
      online: { label: 'Online', className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
      offline: { label: 'Offline', className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
      maintenance: { label: 'Manutenção', className: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' },
      low_balance: { label: 'Saldo Baixo', className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' }
    };
    const statusConfig = config[status as keyof typeof config] || config.offline;
    return <Badge className={statusConfig.className}>{statusConfig.label}</Badge>;
  };

  const stats = {
    online: atms.filter(a => a.status === 'online').length,
    offline: atms.filter(a => a.status === 'offline').length,
    maintenance: atms.filter(a => a.status === 'maintenance').length,
    totalCash: atms.reduce((sum, a) => sum + parseFloat(String(a.current_balance || 0)), 0)
  };

  const canControlStatus = hasRole(['admin', 'gerente', 'tecnico']);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Monitorização de ATMs</h1>
          <p className="text-gray-600 dark:text-gray-400">Controlo e gestão dos caixas automáticos</p>
        </div>
        <Button onClick={loadATMs} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Atualizar
        </Button>
      </div>

      {/* Cards de Estatísticas */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ATMs Online</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.online}</div>
            <p className="text-xs text-muted-foreground">de {atms.length} total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ATMs Offline</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.offline}</div>
            <p className="text-xs text-muted-foreground">Necessita atenção</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Em Manutenção</CardTitle>
            <Wrench className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.maintenance}</div>
            <p className="text-xs text-muted-foreground">Modo manutenção</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total em Caixas</CardTitle>
            <Banknote className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{atmService.formatCurrency(stats.totalCash)}</div>
            <p className="text-xs text-muted-foreground">Distribuído por {atms.length} ATMs</p>
          </CardContent>
        </Card>
      </div>

      {/* Cards de ATMs */}
      <div className="grid gap-6 lg:grid-cols-3">
        {atms.map((atm) => (
          <Card key={atm.id}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  {atm.atm_code}
                </div>
                {getStatusIcon(atm.status)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Localização */}
                <div className="flex justify-between items-start">
                  <span className="text-sm text-muted-foreground">Localização:</span>
                  <span className="text-sm font-medium text-right">{atm.location}</span>
                </div>

                {/* Agência */}
                {atm.branch_name && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Agência:</span>
                    <span className="text-sm font-medium">{atm.branch_name}</span>
                  </div>
                )}

                {/* Estado */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Estado:</span>
                  {getStatusBadge(atm.status)}
                </div>

                {/* Saldo */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Saldo Atual:</span>
                  <span className="font-bold">{atmService.formatCurrency(atm.current_balance)}</span>
                </div>

                {/* Capacidade */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Capacidade:</span>
                  <span className="text-sm">{atmService.formatCurrency(atm.cash_capacity)}</span>
                </div>

                {/* Percentagem de Ocupação */}
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Ocupação:</span>
                    <span className="font-medium">{atm.capacity_percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        atm.capacity_percentage > 70
                          ? 'bg-green-600'
                          : atm.capacity_percentage > 30
                          ? 'bg-yellow-600'
                          : 'bg-red-600'
                      }`}
                      style={{ width: `${atm.capacity_percentage}%` }}
                    />
                  </div>
                </div>

                {/* Controle de Estado - Apenas para Admin, Gerente e Técnico */}
                {canControlStatus && (
                  <div className="pt-3 border-t">
                    <label className="text-sm text-muted-foreground mb-2 block">
                      <Settings className="h-3 w-3 inline mr-1" />
                      Alterar Estado:
                    </label>
                    <Select
                      value={atm.status}
                      onValueChange={(value: any) => handleStatusChange(atm.id, value)}
                      disabled={updatingStatus === atm.id}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="online">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            Online
                          </div>
                        </SelectItem>
                        <SelectItem value="offline">
                          <div className="flex items-center gap-2">
                            <Power className="h-4 w-4 text-red-600" />
                            Offline
                          </div>
                        </SelectItem>
                        <SelectItem value="maintenance">
                          <div className="flex items-center gap-2">
                            <Wrench className="h-4 w-4 text-orange-600" />
                            Em Manutenção
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {atms.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <AlertTriangle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              Nenhum ATM registado no sistema
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ATM;
