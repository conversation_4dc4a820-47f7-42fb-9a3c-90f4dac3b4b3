import { API_CONFIG, API_ENDPOINTS, ApiResponse, ApiError, getAuthHeaders } from '@/config/api';

// Tipos para gestão de balcões
export interface Branch {
  id: number;
  code: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  manager_id?: string;
  manager_name?: string;
  manager_email?: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface CreateBranchRequest {
  code: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  manager_id?: string;
}

export interface UpdateBranchRequest {
  code?: string;
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  manager_id?: string;
  is_active?: boolean;
}

export interface BranchListResponse {
  branches: Branch[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface BranchFilters {
  page?: number;
  limit?: number;
  search?: string;
  active?: boolean;
}

// Classe de erro específica para balcões
export class BranchError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'BranchError';
  }
}

// Helper para fazer requisições HTTP
const makeRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retries: number = 3,
  delay: number = 1000
): Promise<T> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;

  const config: RequestInit = {
    ...options,
    headers: {
      ...getAuthHeaders(),
      ...options.headers,
    },
  };

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        throw new BranchError(
          errorData.message || `Erro HTTP ${response.status}`,
          errorData.code || 'HTTP_ERROR',
          response.status
        );
      }

      const data = await response.json();
      
      if (data.status === 'error') {
        throw new BranchError(data.message || 'Erro na resposta da API', data.code);
      }

      return data;
    } catch (error) {
      if (attempt === retries) {
        if (error instanceof BranchError) {
          throw error;
        }
        throw new BranchError(
          'Erro de conexão com o servidor',
          'CONNECTION_ERROR'
        );
      }
      
      // Aguardar antes de tentar novamente
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw new BranchError('Falha após múltiplas tentativas', 'MAX_RETRIES_EXCEEDED');
};

/**
 * Serviço para gestão de balcões
 */
export class BranchService {
  /**
   * Listar balcões com filtros e paginação
   */
  async listBranches(filters: BranchFilters = {}): Promise<BranchListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.active !== undefined) params.append('active', filters.active.toString());

    const queryString = params.toString();
    const endpoint = `${API_ENDPOINTS.BRANCHES.LIST}${queryString ? `?${queryString}` : ''}`;

    const response = await makeRequest<ApiResponse<BranchListResponse>>(endpoint);
    return response.data!;
  }

  /**
   * Obter balcão específico por ID
   */
  async getBranch(id: number): Promise<Branch> {
    const response = await makeRequest<ApiResponse<{ branch: Branch }>>(
      API_ENDPOINTS.BRANCHES.GET(id.toString())
    );
    return response.data!.branch;
  }

  /**
   * Criar novo balcão
   */
  async createBranch(branchData: CreateBranchRequest): Promise<Branch> {
    const response = await makeRequest<ApiResponse<{ branch: Branch }>>(
      API_ENDPOINTS.BRANCHES.CREATE,
      {
        method: 'POST',
        body: JSON.stringify(branchData),
      }
    );
    return response.data!.branch;
  }

  /**
   * Atualizar balcão existente
   */
  async updateBranch(id: number, branchData: UpdateBranchRequest): Promise<Branch> {
    const response = await makeRequest<ApiResponse<{ branch: Branch }>>(
      API_ENDPOINTS.BRANCHES.UPDATE(id.toString()),
      {
        method: 'PUT',
        body: JSON.stringify(branchData),
      }
    );
    return response.data!.branch;
  }

  /**
   * Remover balcão
   */
  async deleteBranch(id: number): Promise<void> {
    await makeRequest<ApiResponse>(
      API_ENDPOINTS.BRANCHES.DELETE(id.toString()),
      {
        method: 'DELETE',
      }
    );
  }

  /**
   * Ativar/Desativar balcão
   */
  async toggleBranchStatus(id: number, isActive: boolean): Promise<Branch> {
    return this.updateBranch(id, { is_active: isActive });
  }

  /**
   * Buscar balcões ativos (para dropdowns)
   */
  async getActiveBranches(): Promise<Branch[]> {
    const response = await this.listBranches({ 
      active: true, 
      limit: 100 // Buscar todos os balcões ativos
    });
    return response.branches;
  }
}

// Instância singleton do serviço
export const branchService = new BranchService();

// Hook personalizado para React Query (se estiver usando)
export const useBranchQueries = () => {
  return {
    // Query keys para cache
    keys: {
      all: ['branches'] as const,
      lists: () => [...useBranchQueries().keys.all, 'list'] as const,
      list: (filters: BranchFilters) => [...useBranchQueries().keys.lists(), filters] as const,
      details: () => [...useBranchQueries().keys.all, 'detail'] as const,
      detail: (id: number) => [...useBranchQueries().keys.details(), id] as const,
    }
  };
};

export default branchService;
