# Guia de Deploy na Vercel - Twins_Bank

## 📋 Problemas Resolvidos

### ✅ 1. Erro de Build do Frontend (Rollup)
- **Problema**: `Cannot find module @rollup/rollup-linux-x64-gnu`
- **Solução**: 
  - Configuração otimizada do Vite para produção
  - Ficheiro `.npmrc` para garantir instalação de dependências opcionais
  - Configuração de build específica para Vercel

### ✅ 2. Erro 500 do Backend (Winston Logs)
- **Problema**: <PERSON> tentava criar diretórios de logs em ambiente serverless
- **Solução**:
  - Logger configurado condicionalmente baseado em `NODE_ENV`
  - Logs apenas para console em produção
  - Configuração específica para ambientes serverless

## 🚀 Configuração na Vercel

### Frontend (twins-bank-frontend)

1. **Importar Projeto**:
   - Conectar repositório GitHub
   - Selecionar pasta `frontend/`
   - Framework: Vite

2. **Variáveis de Ambiente**:
   ```
   VITE_API_URL=https://twins-bank-backend.vercel.app
   VITE_NODE_ENV=production
   VITE_DEBUG=false
   ```

3. **Configurações de Build**:
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

### Backend (twins-bank-backend)

1. **Importar Projeto**:
   - Conectar repositório GitHub
   - Selecionar pasta `backend/`
   - Framework: Node.js

2. **Variáveis de Ambiente** (CRÍTICAS):
   ```
   # Base de Dados
   DB_HOST=mysql-doublec.alwaysdata.net
   DB_PORT=3306
   DB_NAME=doublec_twins_bank
   DB_USER=doublec
   DB_PASSWORD=CarlosCesar@2022
   
   # JWT (CHAVE SEGURA GERADA)
   JWT_SECRET=ad042f36a25c3488c6582a47622021deb039f04e8191a32c340010dd737412bf12688964f438c356b11c3a47c3a6a27d7c07551a8c72cc90df749555eb74f6e1
   JWT_EXPIRES_IN=24h
   JWT_REFRESH_EXPIRES_IN=7d
   
   # Servidor
   PORT=3000
   NODE_ENV=production
   
   # CORS (ATUALIZAR com URLs reais)
   CORS_ORIGIN=https://twins-bank-frontend.vercel.app
   
   # Rate Limiting
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=50
   
   # Logs
   LOG_LEVEL=warn
   
   # Upload
   UPLOAD_MAX_SIZE=5242880
   UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf
   
   # Segurança
   BCRYPT_ROUNDS=12
   ```

3. **Configurações de Build**:
   - Build Command: `npm install`
   - Output Directory: (deixar vazio)
   - Install Command: `npm install`

## 🔧 Passos Pós-Deploy

1. **Atualizar URLs**:
   - Após deploy, atualizar `VITE_API_URL` no frontend com URL real do backend
   - Atualizar `CORS_ORIGIN` no backend com URL real do frontend

2. **Testar Endpoints**:
   - Health check: `https://twins-bank-backend.vercel.app/api/health`
   - Frontend: `https://twins-bank-frontend.vercel.app`

3. **Monitorização**:
   - Verificar logs na Vercel Dashboard
   - Testar funcionalidades críticas (login, transações)

## 🚨 Notas Importantes

- **JWT_SECRET**: Chave gerada criptograficamente - NÃO alterar após produção
- **Base de Dados**: Mantém configuração AlwaysData existente
- **Logs**: Em produção, logs vão apenas para console (visíveis na Vercel)
- **CORS**: Deve ser atualizado com URLs reais após deploy
- **Rate Limiting**: Mais restritivo em produção (50 req/15min vs 100 em dev)

## 🔍 Resolução de Problemas

### Se o frontend falhar no build:
1. Verificar se `.npmrc` está presente
2. Limpar cache: Settings → General → Clear Build Cache
3. Re-deploy

### Se o backend retornar 500:
1. Verificar logs na Vercel Dashboard
2. Confirmar variáveis de ambiente
3. Testar conexão com base de dados

### Se houver erros de CORS:
1. Verificar `CORS_ORIGIN` no backend
2. Confirmar URLs exatas (sem trailing slash)
3. Re-deploy backend após alteração
