import { API_CONFIG, API_ENDPOINTS, ApiResponse, ApiError, getAuthHeaders } from '@/config/api';
import { makeRequest } from '@/utils/api';

// Tipos para gestão de usuários
export interface User {
  id: string;
  full_name: string;
  email: string;
  role_id: number;
  role_name: string;
  role_description?: string;
  branch_id?: number;
  branch_name?: string;
  branch_code?: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at?: string;
}

export interface CreateUserRequest {
  full_name: string;
  email: string;
  password: string;
  role_id: number;
  branch_id?: number;
}

export interface UpdateUserRequest {
  full_name?: string;
  email?: string;
  role_id?: number;
  branch_id?: number;
  is_active?: boolean;
}

export interface UserListResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface UserFilters {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  branch?: number;
  active?: boolean;
}

export interface Role {
  id: number;
  name: string;
  description: string;
}

// Classe de erro específica para usuários
export class UserError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'UserError';
  }
}

// Removida implementação duplicada do makeRequest - usando a implementação padrão do sistema

/**
 * Serviço para gestão de usuários
 */
export class UserService {
  /**
   * Listar usuários com filtros e paginação
   */
  async listUsers(filters: UserFilters = {}): Promise<UserListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.role) params.append('role', filters.role);
    if (filters.branch) params.append('branch', filters.branch.toString());
    if (filters.active !== undefined) params.append('active', filters.active.toString());

    const queryString = params.toString();
    const endpoint = `${API_ENDPOINTS.USERS.LIST}${queryString ? `?${queryString}` : ''}`;

    const response = await makeRequest<ApiResponse<UserListResponse>>(endpoint);
    return response.data!;
  }

  /**
   * Obter usuário específico por ID
   */
  async getUser(id: string): Promise<User> {
    const response = await makeRequest<ApiResponse<{ user: User }>>(
      API_ENDPOINTS.USERS.UPDATE(id)
    );
    return response.data!.user;
  }

  /**
   * Criar novo usuário
   */
  async createUser(userData: CreateUserRequest): Promise<User> {
    const response = await makeRequest<ApiResponse<{ user: User }>>(
      API_ENDPOINTS.USERS.CREATE,
      {
        method: 'POST',
        body: JSON.stringify(userData),
      }
    );
    return response.data!.user;
  }

  /**
   * Atualizar usuário existente
   */
  async updateUser(id: string, userData: UpdateUserRequest): Promise<User> {
    const response = await makeRequest<ApiResponse<{ user: User }>>(
      API_ENDPOINTS.USERS.UPDATE(id),
      {
        method: 'PUT',
        body: JSON.stringify(userData),
      }
    );
    return response.data!.user;
  }

  /**
   * Remover usuário
   */
  async deleteUser(id: string): Promise<void> {
    await makeRequest<ApiResponse>(
      API_ENDPOINTS.USERS.DELETE(id),
      {
        method: 'DELETE',
      }
    );
  }

  /**
   * Listar utilizadores por agência (com filtro opcional por role)
   */
  async getUsersByBranch(branchId: number, role?: string): Promise<User[]> {
    const params = new URLSearchParams();
    params.append('branch_id', branchId.toString());
    if (role) {
      params.append('role', role);
    }

    const endpoint = `/users/by-branch?${params.toString()}`;
    const response = await makeRequest<ApiResponse<{ users: User[] }>>(endpoint);
    return response.data!.users;
  }

  /**
   * Ativar/Desativar usuário
   */
  async toggleUserStatus(id: string, isActive: boolean): Promise<User> {
    return this.updateUser(id, { is_active: isActive });
  }

  /**
   * Buscar usuários ativos (para dropdowns)
   */
  async getActiveUsers(): Promise<User[]> {
    const response = await this.listUsers({ 
      active: true, 
      limit: 100 // Buscar todos os usuários ativos
    });
    return response.users;
  }

  /**
   * Obter roles disponíveis
   */
  async getRoles(): Promise<Role[]> {
    // Importar roleService dinamicamente para evitar dependência circular
    const { roleService } = await import('./roleService');
    const roles = await roleService.getAllRoles();

    // Converter para formato esperado pelo userService
    return roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description
    }));
  }

  // Listar operadores de caixa
  async getCashiers(): Promise<User[]> {
    const response = await makeRequest<{ data: { cashiers: User[] } }>('/users/cashiers');

    if (response.data && response.data.cashiers) {
      return response.data.cashiers;
    }
    throw new Error(response.message || 'Erro ao carregar operadores de caixa');
  }

  // Listar tesoureiros (filtrados por agência automaticamente no backend)
  async getTreasurers(): Promise<User[]> {
    // O backend já filtra automaticamente por agência se o utilizador não for admin
    // através do endpoint /users que verifica req.user.branch_id
    const response = await this.listUsers({ role: 'tesoureiro', active: true });
    return response.users;
  }
}

// Instância singleton do serviço

export const userService = new UserService();

// Hook personalizado para React Query (se estiver usando)
export const useUserQueries = () => {
  return {
    // Query keys para cache
    keys: {
      all: ['users'] as const,
      lists: () => [...useUserQueries().keys.all, 'list'] as const,
      list: (filters: UserFilters) => [...useUserQueries().keys.lists(), filters] as const,
      details: () => [...useUserQueries().keys.all, 'detail'] as const,
      detail: (id: string) => [...useUserQueries().keys.details(), id] as const,
      roles: () => [...useUserQueries().keys.all, 'roles'] as const,
    }
  };
};

export default userService;
