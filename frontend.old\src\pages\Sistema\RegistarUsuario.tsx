import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { UserPlus, Eye, EyeOff, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { userService, CreateUserRequest, Role, UserError } from '@/services/userService';
import { branchService, Branch, BranchError } from '@/services/branchService';
import { extractAndTranslateError } from '@/utils/errorTranslator';

interface FormErrors {
  full_name?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  role_id?: string;
  branch_id?: string;
}

const RegistarUsuario = () => {
  const { toast } = useToast();

  // Estados do formulário
  const [formData, setFormData] = useState<CreateUserRequest>({
    full_name: '',
    email: '',
    password: '',
    role_id: 0,
    branch_id: undefined
  });

  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Estados para dados do servidor
  const [roles, setRoles] = useState<Role[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);

  // Carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setIsLoadingData(true);
    try {
      const [rolesData, branchesData] = await Promise.all([
        userService.getRoles(),
        branchService.getActiveBranches()
      ]);

      setRoles(rolesData);
      setBranches(branchesData);
    } catch (error) {
      console.error('Erro ao carregar dados iniciais:', error);
      const translatedError = extractAndTranslateError(error);
      toast({
        title: "Erro ao carregar dados",
        description: translatedError,
        variant: "destructive"
      });
    } finally {
      setIsLoadingData(false);
    }
  };

  // Validação do formulário
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validar nome
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Nome é obrigatório';
    } else if (formData.full_name.trim().length < 2) {
      newErrors.full_name = 'Nome deve ter pelo menos 2 caracteres';
    }

    // Validar email
    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email deve ter um formato válido';
    }

    // Validar senha
    if (!formData.password) {
      newErrors.password = 'Senha é obrigatória';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Senha deve ter pelo menos 6 caracteres';
    }

    // Validar confirmação de senha
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Confirmação de senha é obrigatória';
    } else if (formData.password !== confirmPassword) {
      newErrors.confirmPassword = 'Senhas não coincidem';
    }

    // Validar role
    if (!formData.role_id || formData.role_id === 0) {
      newErrors.role_id = 'Perfil é obrigatório';
    }

    // Validar balcão (opcional para admin)
    const selectedRole = roles.find(r => r.id === formData.role_id);
    if (selectedRole?.name !== 'admin' && !formData.branch_id) {
      newErrors.branch_id = 'Balcão é obrigatório para este perfil';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Função para submeter o formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await userService.createUser(formData);

      toast({
        title: "Usuário criado",
        description: `Usuário ${formData.full_name} foi criado com sucesso`,
      });

      // Limpar formulário
      resetForm();
    } catch (error) {
      console.error('Erro ao criar usuário:', error);
      const translatedError = extractAndTranslateError(error);
      toast({
        title: "Erro ao criar usuário",
        description: translatedError,
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para resetar formulário
  const resetForm = () => {
    setFormData({
      full_name: '',
      email: '',
      password: '',
      role_id: 0,
      branch_id: undefined
    });
    setConfirmPassword('');
    setErrors({});
  };

  // Função para lidar com mudanças nos campos
  const handleInputChange = (field: keyof CreateUserRequest, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Verificar se o formulário é válido
  const isFormValid = formData.full_name && formData.email && formData.password &&
                     confirmPassword && formData.role_id &&
                     Object.keys(errors).length === 0;

  // Loading state para dados iniciais
  if (isLoadingData) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Carregando dados...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <UserPlus className="h-8 w-8" />
          Registar Usuário
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">Criar nova conta de usuário no sistema</p>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informações do Usuário</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Nome Completo */}
            <div className="space-y-2">
              <Label htmlFor="full_name">
                Nome Completo <span className="text-red-500">*</span>
              </Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => handleInputChange('full_name', e.target.value)}
                placeholder="Digite o nome completo"
                className={errors.full_name ? 'border-red-500' : ''}
              />
              {errors.full_name && (
                <Alert variant="destructive" className="py-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    {errors.full_name}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">
                Email <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <Alert variant="destructive" className="py-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    {errors.email}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Senha */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="password">
                  Senha <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder="Mínimo 6 caracteres"
                    className={`pr-10 ${errors.password ? 'border-red-500' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {errors.password && (
                  <Alert variant="destructive" className="py-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      {errors.password}
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">
                  Confirmar Senha <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Repita a senha"
                    className={`pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <Alert variant="destructive" className="py-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      {errors.confirmPassword}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>

            {/* Perfil e Balcão */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="role_id">
                  Perfil de Usuário <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.role_id ? formData.role_id.toString() : ''}
                  onValueChange={(value) => handleInputChange('role_id', parseInt(value))}
                >
                  <SelectTrigger className={errors.role_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Selecione o perfil" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id.toString()}>
                        {role.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.role_id && (
                  <Alert variant="destructive" className="py-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      {errors.role_id}
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="branch_id">
                  Balcão {roles.find(r => r.id === formData.role_id)?.name !== 'admin' && <span className="text-red-500">*</span>}
                </Label>
                <Select
                  value={formData.branch_id ? formData.branch_id.toString() : ''}
                  onValueChange={(value) => handleInputChange('branch_id', value ? parseInt(value) : undefined)}
                  disabled={roles.find(r => r.id === formData.role_id)?.name === 'admin'}
                >
                  <SelectTrigger className={errors.branch_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder={
                      roles.find(r => r.id === formData.role_id)?.name === 'admin'
                        ? "Admin não precisa de balcão"
                        : "Selecione o balcão"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id.toString()}>
                        {branch.name} ({branch.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.branch_id && (
                  <Alert variant="destructive" className="py-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      {errors.branch_id}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>

            {/* Resumo */}
            {isFormValid && (
              <Alert className="bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium text-blue-800 dark:text-blue-200">Resumo do Usuário:</p>
                    <p className="text-sm text-blue-700 dark:text-blue-300"><strong>Nome:</strong> {formData.full_name}</p>
                    <p className="text-sm text-blue-700 dark:text-blue-300"><strong>Email:</strong> {formData.email}</p>
                    <p className="text-sm text-blue-700 dark:text-blue-300"><strong>Perfil:</strong> {roles.find(r => r.id === formData.role_id)?.description}</p>
                    {formData.branch_id && (
                      <p className="text-sm text-blue-700 dark:text-blue-300"><strong>Balcão:</strong> {branches.find(b => b.id === formData.branch_id)?.name}</p>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Botões */}
            <div className="flex gap-4 pt-4">
              <Button
                type="submit"
                disabled={!isFormValid || isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Criando Usuário...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Registrar Usuário
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
                disabled={isSubmitting}
              >
                Limpar
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
};

export default RegistarUsuario;
