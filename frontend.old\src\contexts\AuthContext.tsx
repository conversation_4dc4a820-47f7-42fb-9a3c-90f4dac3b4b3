import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  User,
  AuthContextType,
  LoginCredentials,
  UserRole,
  Permission,
  ROLE_PERMISSIONS
} from '@/types/auth';
import authService, { AuthError } from '@/services/authService';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authCheckInProgress, setAuthCheckInProgress] = useState(false);

  // Verificar se há utilizador autenticado ao inicializar
  useEffect(() => {
    const checkAuthState = async () => {
      // Prevenir múltiplas verificações simultâneas
      if (authCheckInProgress) {
        return;
      }

      setAuthCheckInProgress(true);

      try {
        // Verificar se há token armazenado
        if (!authService.isAuthenticated()) {
          setIsLoading(false);
          return;
        }

        // Verificar se o backend está disponível
        const isBackendHealthy = await authService.checkBackendHealth();
        if (!isBackendHealthy) {
          console.warn('Backend indisponível durante verificação de autenticação');
          // Manter estado atual se o backend estiver indisponível
          setIsLoading(false);
          return;
        }

        // Verificar se o token ainda é válido
        const isTokenValid = await authService.checkToken();
        if (!isTokenValid) {
          // Token inválido, tentar renovar
          try {
            await authService.refreshToken();
          } catch (error) {
            console.warn('Não foi possível renovar token:', error);
            // Não foi possível renovar, fazer logout
            await authService.logout();
            setUser(null);
            setIsLoading(false);
            return;
          }
        }

        // Obter dados do utilizador
        const userProfile = await authService.getUserProfile();
        const userData = {
          ...userProfile.user,
          role: userProfile.user.role as UserRole
        };
        setUser(userData);

        // Armazenar dados do utilizador no localStorage para acesso offline
        localStorage.setItem('twins-bank-user', JSON.stringify(userData));

      } catch (error) {
        console.error('Erro ao verificar estado de autenticação:', error);

        // Tentar recuperar dados do localStorage como fallback
        try {
          const savedUser = localStorage.getItem('twins-bank-user');
          if (savedUser && authService.isAuthenticated()) {
            const userData = JSON.parse(savedUser);
            setUser(userData);
            console.info('Utilizando dados de utilizador do cache local');
          } else {
            // Em caso de erro, limpar dados e fazer logout
            await authService.logout();
            setUser(null);
          }
        } catch (fallbackError) {
          console.error('Erro ao recuperar dados do cache:', fallbackError);
          await authService.logout();
          setUser(null);
        }
      } finally {
        setIsLoading(false);
        setAuthCheckInProgress(false);
      }
    };

    checkAuthState();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    setIsLoading(true);

    try {
      // Converter credenciais para formato do backend
      const loginRequest = {
        email: credentials.email,
        password: credentials.senha
      };

      // Fazer login no backend
      const loginResponse = await authService.login(loginRequest);

      // Definir utilizador no estado com tipo correto
      const userData = {
        ...loginResponse.user,
        role: loginResponse.user.role as UserRole
      };
      setUser(userData);

      // Armazenar dados do utilizador no localStorage
      localStorage.setItem('twins-bank-user', JSON.stringify(userData));

    } catch (error) {
      // Re-lançar erro para ser tratado pelo componente
      if (error instanceof AuthError) {
        throw new Error(error.message);
      }
      throw new Error('Erro inesperado durante o login');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);

    try {
      // Fazer logout no backend
      await authService.logout();
    } catch (error) {
      console.warn('Erro ao fazer logout no servidor:', error);
    } finally {
      // Limpar estado local
      setUser(null);
      setIsLoading(false);
    }
  };

  const getPermissions = (role: UserRole): Permission[] => {
    return ROLE_PERMISSIONS[role] || [];
  };

  const hasPermission = (module: string, action: string): boolean => {
    if (!user) return false;

    const permissions = getPermissions(user.role);
    const modulePermission = permissions.find(p => p.module === module);

    return modulePermission?.actions.includes(action) || false;
  };

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!user) return false;

    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(user.role);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    permissions: user ? getPermissions(user.role) : [],
    login,
    logout,
    hasPermission,
    hasRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
