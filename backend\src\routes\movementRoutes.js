const express = require('express');
const { authorize } = require('../auth/middleware');
const { catchAsync } = require('../core/errorHandler');
const suspendedMovementService = require('../services/suspendedMovementService');

const router = express.Router();

/**
 * GET /api/movements/suspended
 * Listar movimentos suspensos com filtros
 */
router.get('/suspended', 
  authorize('admin', 'gerente', 'tesoureiro'), 
  catchAsync(async (req, res) => {
    const filters = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10,
      searchTerm: req.query.search,
      status: req.query.status,
      dateFrom: req.query.dateFrom,
      dateTo: req.query.dateTo
    };

    const result = await suspendedMovementService.getSuspendedMovements(filters);
    
    res.status(200).json({
      status: 'success',
      data: result
    });
  })
);

/**
 * GET /api/movements/suspended/statistics
 * Obter estatísticas de movimentos suspensos
 */
router.get('/suspended/statistics', 
  authorize('admin', 'gerente', 'tesoureiro'), 
  catchAsync(async (req, res) => {
    const stats = await suspendedMovementService.getStatistics();
    
    res.status(200).json({
      status: 'success',
      data: stats
    });
  })
);

/**
 * PATCH /api/movements/suspended/:id/approve
 * Aprovar movimento suspenso
 */
router.patch('/suspended/:id/approve', 
  authorize('admin', 'gerente', 'tesoureiro'), 
  catchAsync(async (req, res) => {
    const { id } = req.params;
    const { observacoes } = req.body;
    const userId = req.user.id;

    const result = await suspendedMovementService.approveMovement(id, observacoes, userId);
    
    res.status(200).json({
      status: 'success',
      message: result.message
    });
  })
);

/**
 * PATCH /api/movements/suspended/:id/reject
 * Rejeitar movimento suspenso
 */
router.patch('/suspended/:id/reject', 
  authorize('admin', 'gerente', 'tesoureiro'), 
  catchAsync(async (req, res) => {
    const { id } = req.params;
    const { motivo } = req.body;
    const userId = req.user.id;

    if (!motivo) {
      return res.status(400).json({
        status: 'error',
        message: 'Motivo da rejeição é obrigatório'
      });
    }

    const result = await suspendedMovementService.rejectMovement(id, motivo, userId);
    
    res.status(200).json({
      status: 'success',
      message: result.message
    });
  })
);

/**
 * POST /api/movements/suspended
 * Criar movimento suspenso (para testes)
 */
router.post('/suspended', 
  authorize('admin', 'gerente'), 
  catchAsync(async (req, res) => {
    const { type, amount, description, suspensionReason } = req.body;
    const userId = req.user.id;

    if (!type || !amount || !suspensionReason) {
      return res.status(400).json({
        status: 'error',
        message: 'Dados obrigatórios: tipo, valor e motivo da suspensão'
      });
    }

    const result = await suspendedMovementService.createSuspendedMovement({
      type,
      amount: parseFloat(amount),
      description: description || 'Movimento suspenso para teste'
    }, suspensionReason, userId);
    
    res.status(201).json({
      status: 'success',
      message: result.message,
      data: {
        id: result.id,
        transactionId: result.transactionId,
        transactionCode: result.transactionCode
      }
    });
  })
);

module.exports = router;
