import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Loader2, AlertCircle, Save, X } from 'lucide-react';
import { User as UserType, UpdateUserRequest, userService } from '@/services/userService';
import { Branch, branchService } from '@/services/branchService';

interface UserEditModalProps {
  user: UserType | null;
  isOpen: boolean;
  onClose: () => void;
  onUserUpdated: () => void;
}

interface FormErrors {
  full_name?: string;
  email?: string;
  role_id?: string;
  branch_id?: string;
}

const UserEditModal: React.FC<UserEditModalProps> = ({ 
  user, 
  isOpen, 
  onClose, 
  onUserUpdated 
}) => {
  const { toast } = useToast();
  
  // Estados do formulário
  const [formData, setFormData] = useState<UpdateUserRequest>({});
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Opções de roles
  const roleOptions = [
    { value: '1', label: 'Administrador', key: 'admin' },
    { value: '2', label: 'Gerente', key: 'gerente' },
    { value: '3', label: 'Tesoureiro', key: 'tesoureiro' },
    { value: '4', label: 'Operador de Caixa', key: 'caixa' },
    { value: '5', label: 'Técnico', key: 'tecnico' },
    { value: '7', label: 'Balcão', key: 'balcao' }
  ];

  // Carregar dados quando o modal abrir
  useEffect(() => {
    if (isOpen && user) {
      loadInitialData();
    }
  }, [isOpen, user]);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      // Carregar balcões
      const branchesData = await branchService.getActiveBranches();
      setBranches(branchesData);

      // Mapear role_name para role_id
      const getRoleIdFromName = (roleName?: string): number | undefined => {
        if (!roleName) return undefined;
        const roleMap: { [key: string]: number } = {
          'admin': 1,
          'gerente': 2,
          'tesoureiro': 3,
          'caixa': 4,
          'tecnico': 5,
          'balcao': 7
        };
        return roleMap[roleName.toLowerCase()];
      };

      // Mapear branch_name para branch_id
      const getBranchIdFromName = (branchName?: string): number | undefined => {
        if (!branchName || !branchesData.length) return undefined;
        const branch = branchesData.find(b => b.name === branchName);
        return branch?.id;
      };

      // Inicializar formulário com dados do usuário
      const initialFormData = {
        full_name: user?.full_name || '',
        email: user?.email || '',
        role_id: user?.role_id || getRoleIdFromName(user?.role_name),
        branch_id: user?.branch_id || getBranchIdFromName(user?.branch_name),
        // Garantir que is_active seja sempre boolean (converter 1/0 para true/false)
        is_active: Boolean(user?.is_active ?? true)
      };

      setFormData(initialFormData);

      setErrors({});
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: "Erro ao carregar dados",
        description: "Não foi possível carregar os dados necessários",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Validação do formulário
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.full_name?.trim()) {
      newErrors.full_name = 'Nome completo é obrigatório';
    } else if (formData.full_name.length < 2) {
      newErrors.full_name = 'Nome deve ter pelo menos 2 caracteres';
    }

    if (!formData.email?.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email deve ter um formato válido';
    }

    if (!formData.role_id) {
      newErrors.role_id = 'Perfil é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submeter formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Preparar dados para envio, garantindo tipos corretos
      const updateData: UpdateUserRequest = {
        ...formData,
        // Garantir que is_active seja sempre boolean
        is_active: Boolean(formData.is_active)
      };

      // Remover campos undefined para evitar envio desnecessário
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof UpdateUserRequest] === undefined) {
          delete updateData[key as keyof UpdateUserRequest];
        }
      });

      await userService.updateUser(user.id, updateData);

      toast({
        title: "Usuário atualizado",
        description: `${formData.full_name} foi atualizado com sucesso`,
      });

      onUserUpdated();
      onClose();
    } catch (error) {
      console.error('Erro ao atualizar usuário:', error);
      toast({
        title: "Erro ao atualizar usuário",
        description: "Não foi possível atualizar o usuário. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fechar modal
  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({});
      setErrors({});
      onClose();
    }
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            Editar Usuário
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Carregando dados...</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Nome Completo */}
            <div className="space-y-2">
              <Label htmlFor="full_name">
                Nome Completo <span className="text-red-500">*</span>
              </Label>
              <Input
                id="full_name"
                value={formData.full_name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                placeholder="Digite o nome completo"
                className={errors.full_name ? 'border-red-500' : ''}
                disabled={isSubmitting}
              />
              {errors.full_name && (
                <Alert variant="destructive" className="py-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    {errors.full_name}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">
                Email <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="Digite o email"
                className={errors.email ? 'border-red-500' : ''}
                disabled={isSubmitting}
              />
              {errors.email && (
                <Alert variant="destructive" className="py-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    {errors.email}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Perfil */}
            <div className="space-y-2">
              <Label htmlFor="role_id">
                Perfil <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.role_id?.toString() || ''}
                onValueChange={(value) => setFormData(prev => ({ ...prev, role_id: parseInt(value) }))}
                disabled={isSubmitting}
              >
                <SelectTrigger className={errors.role_id ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Selecione o perfil" />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.role_id && (
                <Alert variant="destructive" className="py-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    {errors.role_id}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Balcão */}
            <div className="space-y-2">
              <Label htmlFor="branch_id">Balcão</Label>
              <Select
                value={formData.branch_id?.toString() || 'none'}
                onValueChange={(value) => setFormData(prev => ({ 
                  ...prev, 
                  branch_id: value === 'none' ? undefined : parseInt(value) 
                }))}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o balcão" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Nenhum balcão</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <DialogFooter className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                <X className="h-4 w-4 mr-2" />
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default UserEditModal;
