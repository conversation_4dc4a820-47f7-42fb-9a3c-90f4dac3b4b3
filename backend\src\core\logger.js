const winston = require('winston');
const path = require('path');

// Configuração dos níveis de log
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Configuração das cores para cada nível
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

winston.addColors(logColors);

// Verificar se estamos em ambiente de produção/serverless
const isProduction = process.env.NODE_ENV === 'production';
const isServerless = process.env.VERCEL || process.env.AWS_LAMBDA_FUNCTION_NAME || process.env.FUNCTIONS_WORKER_RUNTIME;

// Formato personalizado para logs no console
const consoleLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Formato para ficheiros (sem cores) - apenas usado em desenvolvimento
const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss:ms' }),
  winston.format.json()
);

// Formato para produção/serverless (estruturado para melhor parsing)
const productionLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Configuração dos transportes baseada no ambiente
const createTransports = () => {
  const transports = [];

  // Console transport - sempre presente
  transports.push(
    new winston.transports.Console({
      format: isProduction || isServerless ? productionLogFormat : consoleLogFormat,
      level: process.env.LOG_LEVEL || (isProduction ? 'warn' : 'info'),
      handleExceptions: true,
      handleRejections: true
    })
  );

  // Transportes de ficheiro - apenas em desenvolvimento
  // Em produção/serverless, os logs vão apenas para console/stdout
  if (!isProduction && !isServerless) {
    // Criar directório de logs se não existir (apenas em desenvolvimento)
    const fs = require('fs');
    const logsDir = path.join(__dirname, '../../logs');
    if (!fs.existsSync(logsDir)) {
      try {
        fs.mkdirSync(logsDir, { recursive: true });
      } catch (error) {
        console.warn('Aviso: Não foi possível criar directório de logs:', error.message);
      }
    }

    // Ficheiro para todos os logs (apenas desenvolvimento)
    transports.push(
      new winston.transports.File({
        filename: path.join(__dirname, '../../logs/app.log'),
        format: fileLogFormat,
        level: 'info',
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        handleExceptions: true
      })
    );

    // Ficheiro apenas para erros (apenas desenvolvimento)
    transports.push(
      new winston.transports.File({
        filename: path.join(__dirname, '../../logs/error.log'),
        format: fileLogFormat,
        level: 'error',
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        handleExceptions: true
      })
    );
  }

  return transports;
};

// Criar logger com configuração dinâmica
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (isProduction ? 'warn' : 'info'),
  levels: logLevels,
  format: isProduction || isServerless ? productionLogFormat : fileLogFormat,
  transports: createTransports(),
  exitOnError: false,
  // Configurações adicionais para ambiente serverless
  silent: false,
  handleExceptions: true,
  handleRejections: true
});

// Log de inicialização com informações do ambiente
logger.info('Logger inicializado', {
  environment: process.env.NODE_ENV || 'development',
  isProduction,
  isServerless,
  logLevel: logger.level,
  transportsCount: logger.transports.length,
  platform: process.platform,
  nodeVersion: process.version
});

// Funções auxiliares de logging com contexto
logger.http = (message, meta = {}) => {
  logger.log('http', message, { ...meta, context: 'HTTP' });
};

logger.database = (message, meta = {}) => {
  logger.info(`[DATABASE] ${message}`, { ...meta, context: 'DATABASE' });
};

logger.auth = (message, meta = {}) => {
  logger.info(`[AUTH] ${message}`, { ...meta, context: 'AUTH' });
};

logger.transaction = (message, meta = {}) => {
  logger.info(`[TRANSACTION] ${message}`, { ...meta, context: 'TRANSACTION' });
};

logger.security = (message, meta = {}) => {
  logger.warn(`[SECURITY] ${message}`, { ...meta, context: 'SECURITY' });
};

// Função para log de erros com stack trace completo
logger.errorWithStack = (message, error, meta = {}) => {
  logger.error(message, {
    ...meta,
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name
    },
    context: 'ERROR'
  });
};

// Função para log de performance
logger.performance = (message, duration, meta = {}) => {
  logger.info(`[PERFORMANCE] ${message}`, {
    ...meta,
    duration: `${duration}ms`,
    context: 'PERFORMANCE'
  });
};

// Função para log de deploy/startup
logger.startup = (message, meta = {}) => {
  logger.info(`[STARTUP] ${message}`, { ...meta, context: 'STARTUP' });
};

module.exports = logger;
