const logger = require('./logger');

/**
 * Classe para erros personalizados da aplicação
 */
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Middleware de tratamento de erros
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log do erro
  logger.error(`Erro na rota ${req.method} ${req.path}:`, {
    error: error.message,
    stack: err.stack,
    user: req.user?.id,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Erro de validação do Joi
  if (err.isJoi) {
    const message = err.details.map(detail => detail.message).join(', ');
    error = new AppError(message, 400, 'VALIDATION_ERROR');
  }

  // Erro de duplicação de chave única (MySQL)
  if (err.code === 'ER_DUP_ENTRY') {
    const field = err.sqlMessage.match(/for key '(.+?)'/)?.[1] || 'campo';
    const message = `Já existe um registo com este ${field}`;
    error = new AppError(message, 409, 'DUPLICATE_ENTRY');
  }

  // Erro de chave estrangeira (MySQL)
  if (err.code === 'ER_NO_REFERENCED_ROW_2') {
    const message = 'Referência inválida. Verifique os dados fornecidos.';
    error = new AppError(message, 400, 'FOREIGN_KEY_ERROR');
  }

  // Erro de conexão com a base de dados
  if (err.code === 'ECONNREFUSED' || err.code === 'ER_ACCESS_DENIED_ERROR') {
    const message = 'Erro de conexão com a base de dados';
    error = new AppError(message, 500, 'DATABASE_CONNECTION_ERROR');
  }

  // Erro de token JWT
  if (err.name === 'JsonWebTokenError') {
    const message = 'Token inválido';
    error = new AppError(message, 401, 'INVALID_TOKEN');
  }

  // Erro de token JWT expirado
  if (err.name === 'TokenExpiredError') {
    const message = 'Token expirado';
    error = new AppError(message, 401, 'EXPIRED_TOKEN');
  }

  // Erro de limite de upload
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'Ficheiro muito grande. Tamanho máximo: 5MB';
    error = new AppError(message, 400, 'FILE_TOO_LARGE');
  }

  // Erro de tipo de ficheiro não permitido
  if (err.code === 'INVALID_FILE_TYPE') {
    const message = 'Tipo de ficheiro não permitido';
    error = new AppError(message, 400, 'INVALID_FILE_TYPE');
  }

  // Resposta de erro
  const statusCode = error.statusCode || 500;
  const status = error.status || 'error';

  // Em produção, não enviar detalhes do erro para o cliente
  if (process.env.NODE_ENV === 'production' && !error.isOperational) {
    return res.status(500).json({
      status: 'error',
      message: 'Algo correu mal. Tente novamente mais tarde.',
      timestamp: new Date().toISOString()
    });
  }

  res.status(statusCode).json({
    status,
    message: error.message,
    code: error.code,
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

/**
 * Middleware para capturar rotas não encontradas
 */
const notFoundHandler = (req, res, next) => {
  const err = new AppError(`Rota ${req.originalUrl} não encontrada`, 404, 'ROUTE_NOT_FOUND');
  next(err);
};

/**
 * Wrapper para funções assíncronas
 */
const catchAsync = (fn) => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

module.exports = {
  AppError,
  errorHandler,
  notFoundHandler,
  catchAsync
};
