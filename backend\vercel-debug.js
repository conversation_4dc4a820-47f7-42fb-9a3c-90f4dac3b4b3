/**
 * Script de debug específico para problemas na Vercel
 * Simula o ambiente serverless e identifica possíveis problemas
 */

// Carregar configurações de produção
require('dotenv').config({ path: '.env.production' });

// Simular ambiente Vercel
process.env.VERCEL = '1';
process.env.NODE_ENV = 'production';

console.log('🔍 Iniciando diagnóstico para ambiente Vercel...');
console.log('Ambiente simulado:', {
  VERCEL: process.env.VERCEL,
  NODE_ENV: process.env.NODE_ENV,
  isServerless: !!(process.env.VERCEL || process.env.AWS_LAMBDA_FUNCTION_NAME || process.env.FUNCTIONS_WORKER_RUNTIME)
});

async function testServerlessApp() {
  try {
    console.log('📦 Carregando dependências...');
    
    // Testar carregamento do logger
    console.log('🔧 Testando logger...');
    const logger = require('./src/core/logger');
    logger.info('Logger carregado com sucesso');

    // Testar carregamento da configuração da base de dados
    console.log('🔧 Testando configuração da base de dados...');
    const { initializeDatabase } = require('./src/config/database');
    
    // Testar inicialização da base de dados
    console.log('🔧 Testando inicialização da base de dados...');
    await initializeDatabase();
    console.log('✅ Base de dados inicializada com sucesso');

    // Testar carregamento da aplicação Express
    console.log('🔧 Testando carregamento da aplicação Express...');
    const app = require('./src/server');
    console.log('✅ Aplicação Express carregada com sucesso');

    // Testar se a aplicação responde
    console.log('🔧 Testando resposta da aplicação...');
    const request = require('supertest');
    const response = await request(app).get('/api/health');
    
    console.log('✅ Health check respondeu:', {
      status: response.status,
      body: response.body
    });

    console.log('🎉 Todos os testes passaram! A aplicação deve funcionar na Vercel.');
    return true;

  } catch (error) {
    console.error('❌ Erro encontrado:');
    console.error('Tipo:', error.constructor.name);
    console.error('Mensagem:', error.message);
    console.error('Stack:', error.stack);
    
    // Análise específica de erros comuns
    if (error.message.includes('Cannot find module')) {
      console.error('🔍 Possível problema: Dependência em falta');
      console.error('Solução: Verificar se todas as dependências estão no package.json');
    }
    
    if (error.message.includes('ENOENT') && error.message.includes('mkdir')) {
      console.error('🔍 Possível problema: Tentativa de criar diretórios em filesystem read-only');
      console.error('Solução: Verificar código de upload e logs');
    }
    
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      console.error('🔍 Possível problema: Conexão com base de dados');
      console.error('Solução: Verificar variáveis de ambiente na Vercel');
    }

    return false;
  }
}

// Executar diagnóstico
if (require.main === module) {
  testServerlessApp()
    .then(success => {
      console.log(success ? '✅ Diagnóstico concluído com sucesso' : '❌ Problemas encontrados');
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Erro inesperado no diagnóstico:', error);
      process.exit(1);
    });
}

module.exports = { testServerlessApp };
