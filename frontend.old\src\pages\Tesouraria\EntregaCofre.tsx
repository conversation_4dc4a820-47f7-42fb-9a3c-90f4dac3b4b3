import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Vault, Shield, CheckCircle, AlertCircle, Calculator, Loader2 } from 'lucide-react';
import {
  treasuryService,
  CashDenominations,
  DeliverToVaultRequest
} from '@/services/treasuryService';
import { OperationSummary } from '@/components/common/OperationSummary';

const EntregaCofre = () => {
  const [formData, setFormData] = useState({
    amount: '',
    source_type: 'treasury' as 'cash_register' | 'treasury' | 'manual' | 'system',
    source_id: '',
    notes: ''
  });

  const [denominations, setDenominations] = useState<CashDenominations>(
    treasuryService.getEmptyDenominations()
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Estado para saldo do tesoureiro
  const [treasurerBalance, setTreasurerBalance] = useState<number>(0);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);

  const { toast } = useToast();

  // Carregar saldo do tesoureiro ao montar componente
  useEffect(() => {
    loadTreasurerBalance();
  }, []);

  // Auto-calcular o valor total baseado nas denominações
  useEffect(() => {
    const total = treasuryService.calculateDenominationsTotal(denominations);
    setFormData(prev => ({ ...prev, amount: total.toString() }));
  }, [denominations]);

  const loadTreasurerBalance = async () => {
    try {
      setIsLoadingBalance(true);
      const balanceData = await treasuryService.getMyBalance();
      setTreasurerBalance(parseFloat(balanceData.current_balance) || 0);
    } catch (error: any) {
      console.error('Erro ao carregar saldo do tesoureiro:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar saldo do tesoureiro",
        variant: "destructive"
      });
      setTreasurerBalance(0); // Valor padrão em caso de erro
    } finally {
      setIsLoadingBalance(false);
    }
  };

  // Validar formulário
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Valor deve ser maior que zero';
    }

    // Validar saldo suficiente
    const amount = parseFloat(formData.amount) || 0;
    if (amount > treasurerBalance) {
      newErrors.amount = `Saldo insuficiente. Disponível: ${treasuryService.formatCurrency(treasurerBalance)}`;
    }

    if (!formData.source_type) {
      newErrors.source_type = 'Origem dos valores é obrigatória';
    }

    // Validar se total das denominações corresponde ao valor
    const denominationsTotal = treasuryService.calculateDenominationsTotal(denominations);

    if (amount > 0 && Math.abs(denominationsTotal - amount) > 0.01) {
      newErrors.denominations = 'Total das denominações deve corresponder ao valor informado';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submeter formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const deliveryData: DeliverToVaultRequest = {
        amount: parseFloat(formData.amount),
        source_type: formData.source_type,
        source_id: formData.source_id || undefined,
        denominations,
        notes: formData.notes || undefined
      };

      await treasuryService.deliverToVault(deliveryData);

      toast({
        title: "Sucesso",
        description: `Entrega de ${treasuryService.formatCurrency(parseFloat(formData.amount))} ao cofre realizada com sucesso`
      });

      // Limpar formulário e recarregar saldo
      setFormData({ amount: '', source_type: 'treasury', source_id: '', notes: '' });
      setDenominations(treasuryService.getEmptyDenominations());
      setErrors({});
      await loadTreasurerBalance(); // Recarregar saldo após entrega

    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || 'Erro ao processar entrega ao cofre',
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Entrega ao Cofre</h1>
          <p className="text-gray-600 dark:text-gray-400">Registar entrega de valores para o cofre principal</p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Vault className="h-5 w-5" />
                Informações da Entrega
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 mb-2">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Área de Segurança Máxima</span>
                </div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Esta operação registará a entrada de valores no cofre principal da agência.
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="amount">Valor Total a Depositar (AOA) *</Label>
                  <div className="relative">
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      placeholder="0,00"
                      value={formData.amount}
                      readOnly
                      className={`text-lg font-bold bg-gray-50 dark:bg-gray-800 cursor-not-allowed ${errors.amount ? 'border-red-500' : ''}`}
                    />
                    <Calculator className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Calculado automaticamente a partir das denominações
                  </p>
                  {errors.amount && (
                    <p className="text-sm text-red-500 mt-1">{errors.amount}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="source_type">Origem dos Valores *</Label>
                  <Select 
                    value={formData.source_type} 
                    onValueChange={(value: 'cash_register' | 'treasury' | 'manual' | 'system') => 
                      setFormData(prev => ({ ...prev, source_type: value }))
                    }
                  >
                    <SelectTrigger className={errors.source_type ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Selecionar origem..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="treasury">Tesouraria</SelectItem>
                      <SelectItem value="cash_register">Caixa</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                      <SelectItem value="system">Sistema</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.source_type && (
                    <p className="text-sm text-red-500 mt-1">{errors.source_type}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="source_id">Identificação da Origem</Label>
                  <Input 
                    id="source_id"
                    placeholder="Ex: CAIXA001, REF123..." 
                    value={formData.source_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, source_id: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Observações</Label>
                  <Textarea 
                    id="notes"
                    placeholder="Observações sobre a operação..."
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Detalhamento das Denominações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="notes_10000">Notas de 10.000 AOA</Label>
                  <Input 
                    id="notes_10000"
                    type="number" 
                    min="0" 
                    placeholder="0"
                    value={denominations.notes_10000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_10000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_5000">Notas de 5.000 AOA</Label>
                  <Input 
                    id="notes_5000"
                    type="number" 
                    min="0" 
                    placeholder="0"
                    value={denominations.notes_5000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_5000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_2000">Notas de 2.000 AOA</Label>
                  <Input 
                    id="notes_2000"
                    type="number" 
                    min="0" 
                    placeholder="0"
                    value={denominations.notes_2000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_2000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_1000">Notas de 1.000 AOA</Label>
                  <Input 
                    id="notes_1000"
                    type="number" 
                    min="0" 
                    placeholder="0"
                    value={denominations.notes_1000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_1000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_500">Notas de 500 AOA</Label>
                  <Input 
                    id="notes_500"
                    type="number" 
                    min="0" 
                    placeholder="0"
                    value={denominations.notes_500}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_500: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_200">Notas de 200 AOA</Label>
                  <Input
                    id="notes_200"
                    type="number"
                    min="0"
                    placeholder="0"
                    value={denominations.notes_200}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_200: parseInt(e.target.value) || 0 }))}
                  />
                </div>
              </div>

              <div className="bg-gray-50 border border-gray-200 p-3 rounded-lg dark:bg-gray-800 dark:border-gray-700">
                <div className="text-gray-700 dark:text-gray-300 text-sm">
                  <strong>Total Calculado:</strong> {treasuryService.formatCurrency(treasuryService.calculateDenominationsTotal(denominations))}
                </div>
              </div>

              {errors.denominations && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.denominations}</AlertDescription>
                </Alert>
              )}

              <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg dark:bg-yellow-900/20 dark:border-yellow-800">
                <div className="text-yellow-700 dark:text-yellow-300 text-sm">
                  <strong>Atenção:</strong> Esta operação será registada no sistema de auditoria.
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full" 
                size="lg"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processando...
                  </>
                ) : (
                  <>
                    <Vault className="h-4 w-4 mr-2" />
                    Depositar no Cofre
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Resumo da Operação */}
          <OperationSummary
            currentBalance={treasurerBalance}
            deliveryAmount={parseFloat(formData.amount) || 0}
            title="Resumo da Operação"
            formatCurrency={treasuryService.formatCurrency}
            showValidation={true}
            className="lg:col-span-1"
          />
        </div>
      </form>
    </div>
  );
};

export default EntregaCofre;
