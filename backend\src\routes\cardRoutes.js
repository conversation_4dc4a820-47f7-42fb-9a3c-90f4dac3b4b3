const express = require('express');
const { authorize } = require('../auth/middleware');

const router = express.Router();

// Placeholder routes for card management
// TODO: Implement full card management functionality

/**
 * GET /api/cards
 * Listar cartões
 */
router.get('/', authorize('admin', 'gerente', 'caixa'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Listagem de cartões - Em desenvolvimento',
    data: []
  });
});

/**
 * POST /api/cards
 * Emitir cartão
 */
router.post('/', authorize('admin', 'gerente', 'caixa'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Emissão de cartão - Em desenvolvimento'
  });
});

module.exports = router;
