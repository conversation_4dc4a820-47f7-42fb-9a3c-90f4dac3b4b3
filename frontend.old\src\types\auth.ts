// Tipos para o sistema de autenticação

// Interface do utilizador baseada na resposta do backend
export interface User {
  id: string;
  full_name: string;
  email: string;
  role: UserRole;
  branch: {
    id: number;
    name: string;
    code?: string;
  } | null;
  is_active: boolean;
  last_login: string | null;
  created_at: string;
}

// Tipos de roles suportados pelo sistema
export type UserRole = 'admin' | 'gerente' | 'caixa' | 'tesoureiro' | 'tecnico' | 'balcao';

export interface Permission {
  module: string;
  actions: string[];
}

export interface RolePermissions {
  [key: string]: Permission[];
}

// Credenciais de login (mantém compatibilidade com frontend)
export interface LoginCredentials {
  email: string;
  senha: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  permissions: Permission[];
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  hasPermission: (module: string, action: string) => boolean;
  hasRole: (role: UserRole | UserRole[]) => boolean;
}

// Definição de permissões por role (baseado no backend)
export const ROLE_PERMISSIONS: RolePermissions = {
  admin: [
    { module: 'sistema', actions: ['read', 'write', 'delete'] },
    { module: 'usuarios', actions: ['read', 'write', 'delete'] },
    { module: 'clientes', actions: ['read', 'write', 'delete'] },
    { module: 'accounts', actions: ['read', 'write', 'delete', 'block', 'unblock'] },
    { module: 'transferencias', actions: ['read', 'write', 'delete'] },
    { module: 'caixa', actions: ['read', 'write', 'delete'] },
    { module: 'tesouraria', actions: ['read', 'write', 'delete'] },
    { module: 'cartoes', actions: ['read', 'write', 'delete'] },
    { module: 'cambios', actions: ['read', 'write', 'delete'] },
    { module: 'seguros', actions: ['read', 'write', 'delete'] },
    { module: 'atm', actions: ['read', 'write', 'delete'] },
    { module: 'relatorios', actions: ['read', 'write'] }
  ],
  gerente: [
    { module: 'clientes', actions: ['read', 'write'] },
    { module: 'accounts', actions: ['read', 'write', 'block', 'unblock'] },
    { module: 'transferencias', actions: ['read', 'write'] },
    { module: 'caixa', actions: ['read', 'write'] },
    { module: 'tesouraria', actions: ['read', 'write'] },
    { module: 'cartoes', actions: ['read', 'write'] },
    { module: 'cambios', actions: ['read', 'write'] },
    { module: 'seguros', actions: ['read', 'write'] },
    { module: 'atm', actions: ['read'] },
    { module: 'relatorios', actions: ['read'] }
  ],
  caixa: [
    { module: 'clientes', actions: ['read'] },
    { module: 'accounts', actions: ['read'] },
    { module: 'transferencias', actions: ['read', 'write'] },
    { module: 'caixa', actions: ['read', 'write'] },
    { module: 'cambios', actions: ['read', 'write'] }
  ],
  tesoureiro: [
    { module: 'tesouraria', actions: ['read', 'write'] },
    { module: 'caixa', actions: ['read'] },
    { module: 'atm', actions: ['read', 'write'] }
  ],
  tecnico: [
    { module: 'sistema', actions: ['read'] },
    { module: 'atm', actions: ['read', 'write'] },
    { module: 'relatorios', actions: ['read'] }
  ],
  balcao: [
    { module: 'clientes', actions: ['read', 'write'] },
    { module: 'accounts', actions: ['read'] },
    { module: 'transferencias', actions: ['read', 'write'] },
    { module: 'cartoes', actions: ['read', 'write'] },
    { module: 'cambios', actions: ['read', 'write'] },
    { module: 'seguros', actions: ['read', 'write'] },
    { module: 'tesouraria', actions: ['read'] }
  ]
};
