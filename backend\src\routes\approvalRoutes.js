const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');

const router = express.Router();

/**
 * Listar solicitações de abertura de contas
 * GET /api/approvals/accounts
 */
router.get('/accounts', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const {
      search = '',
      status = '',
      client_type = '',
      account_type = '',
      branch_id = '',
      page = 1,
      limit = 20
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Construir query base
    let whereConditions = ['1=1'];
    let queryParams = [];

    // Filtros
    if (search) {
      whereConditions.push(`(c.full_name LIKE ? OR c.company_name LIKE ? OR c.document_number LIKE ?)`);
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    if (status) {
      whereConditions.push(`aa.status = ?`);
      queryParams.push(status);
    }

    if (client_type) {
      whereConditions.push(`c.client_type = ?`);
      queryParams.push(client_type);
    }

    if (account_type) {
      whereConditions.push(`aa.account_type = ?`);
      queryParams.push(account_type);
    }

    if (branch_id) {
      whereConditions.push(`aa.branch_id = ?`);
      queryParams.push(branch_id);
    }

    const whereClause = whereConditions.join(' AND ');

    // Query principal
    const query = `
      SELECT 
        aa.id,
        aa.client_id,
        COALESCE(c.full_name, c.company_name) as client_name,
        c.client_type,
        c.document_number,
        aa.account_type,
        aa.currency_id,
        aa.initial_deposit,
        aa.overdraft_limit,
        aa.status,
        aa.rejection_reason,
        aa.approved_by,
        aa.approved_at,
        aa.rejected_by,
        aa.rejected_at,
        aa.account_id,
        aa.created_by,
        aa.created_at,
        b.name as branch_name,
        c.monthly_income,
        u_created.full_name as requested_by,
        u_approved.full_name as approved_by_name,
        u_rejected.full_name as rejected_by_name
      FROM account_applications aa
      JOIN clients c ON aa.client_id = c.id
      JOIN branches b ON aa.branch_id = b.id
      JOIN users u_created ON aa.created_by = u_created.id
      LEFT JOIN users u_approved ON aa.approved_by = u_approved.id
      LEFT JOIN users u_rejected ON aa.rejected_by = u_rejected.id
      WHERE ${whereClause}
      ORDER BY aa.created_at DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(parseInt(limit), offset);

    const applications = await executeQuery(query, queryParams);

    // Query para contar total
    const countQuery = `
      SELECT COUNT(*) as total
      FROM account_applications aa
      JOIN clients c ON aa.client_id = c.id
      WHERE ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2); // Remove limit e offset
    const [{ total }] = await executeQuery(countQuery, countParams);

    res.status(200).json({
      status: 'success',
      data: {
        applications,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(total),
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('[ERROR] Erro ao listar solicitações de contas:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Obter detalhes de uma solicitação específica
 * GET /api/approvals/accounts/:id
 */
router.get('/accounts/:id', authorize('admin', 'gerente'), async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        aa.*,
        COALESCE(c.full_name, c.company_name) as client_name,
        c.client_type,
        c.document_number,
        c.nif,
        c.birth_date,
        c.incorporation_date,
        c.nationality,
        c.profession,
        c.monthly_income,
        b.name as branch_name,
        u_created.full_name as requested_by,
        u_approved.full_name as approved_by_name,
        u_rejected.full_name as rejected_by_name,
        -- Endereço do cliente
        ca.street,
        ca.municipality,
        ca.province,
        ca.postal_code
      FROM account_applications aa
      JOIN clients c ON aa.client_id = c.id
      JOIN branches b ON aa.branch_id = b.id
      JOIN users u_created ON aa.created_by = u_created.id
      LEFT JOIN users u_approved ON aa.approved_by = u_approved.id
      LEFT JOIN users u_rejected ON aa.rejected_by = u_rejected.id
      LEFT JOIN client_addresses ca ON c.id = ca.client_id
      WHERE aa.id = ?
    `;

    const [application] = await executeQuery(query, [id]);

    if (!application) {
      return res.status(404).json({
        status: 'error',
        message: 'Solicitação não encontrada'
      });
    }

    res.status(200).json({
      status: 'success',
      data: { application }
    });

  } catch (error) {
    console.error('[ERROR] Erro ao obter detalhes da solicitação:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Aprovar solicitação de abertura de conta
 * PUT /api/approvals/accounts/:id/approve
 */
router.put('/accounts/:id/approve', authorize('admin', 'gerente'), async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a solicitação existe e está pendente
    const checkQuery = `
      SELECT aa.*, c.client_type, COALESCE(c.full_name, c.company_name) as client_name
      FROM account_applications aa
      JOIN clients c ON aa.client_id = c.id
      WHERE aa.id = ? AND aa.status = 'pending'
    `;

    const [application] = await executeQuery(checkQuery, [id]);

    if (!application) {
      return res.status(404).json({
        status: 'error',
        message: 'Solicitação não encontrada ou já processada'
      });
    }

    // Gerar número da conta
    const accountNumber = await generateAccountNumber();

    // Criar a conta bancária
    const accountId = uuidv4();
    const createAccountQuery = `
      INSERT INTO accounts (
        id, account_number, account_type, currency_id, balance, available_balance,
        overdraft_limit, status, opening_date, branch_id, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', CURDATE(), ?, ?)
    `;

    await executeQuery(createAccountQuery, [
      accountId,
      accountNumber,
      application.account_type,
      application.currency_id,
      application.initial_deposit,
      application.initial_deposit,
      application.overdraft_limit,
      application.branch_id,
      userId
    ]);

    // Criar relação titular da conta (primeiro titular)
    const createHolderQuery = `
      INSERT INTO account_holders (account_id, client_id, holder_type)
      VALUES (?, ?, 'primary')
    `;

    await executeQuery(createHolderQuery, [accountId, application.client_id]);

    // Adicionar segundo titular se existir (para contas Junior)
    if (application.second_holder_id && (application.account_type === 'junior')) {
      const createSecondHolderQuery = `
        INSERT INTO account_holders (account_id, client_id, holder_type)
        VALUES (?, ?, 'secondary')
      `;

      await executeQuery(createSecondHolderQuery, [accountId, application.second_holder_id]);

      // Atualizar status do segundo titular para 'active'
      const updateSecondClientStatusQuery = `
        UPDATE clients
        SET status = 'active', updated_at = NOW()
        WHERE id = ?
      `;

      await executeQuery(updateSecondClientStatusQuery, [application.second_holder_id]);
    }

    // Atualizar status da solicitação
    const updateApplicationQuery = `
      UPDATE account_applications
      SET status = 'approved', approved_by = ?, approved_at = NOW(), account_id = ?
      WHERE id = ?
    `;

    await executeQuery(updateApplicationQuery, [userId, accountId, id]);

    // Atualizar status do cliente para 'active' quando a conta é aprovada
    const updateClientStatusQuery = `
      UPDATE clients
      SET status = 'active', updated_at = NOW()
      WHERE id = ?
    `;

    await executeQuery(updateClientStatusQuery, [application.client_id]);

    res.status(200).json({
      status: 'success',
      message: 'Solicitação aprovada com sucesso',
      data: {
        application_id: id,
        account_id: accountId,
        account_number: accountNumber
      }
    });

  } catch (error) {
    console.error('[ERROR] Erro ao aprovar solicitação:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Rejeitar solicitação de abertura de conta
 * PUT /api/approvals/accounts/:id/reject
 */
router.put('/accounts/:id/reject', authorize('admin', 'gerente'), async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user.id;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Motivo da rejeição é obrigatório'
      });
    }

    // Verificar se a solicitação existe e está pendente
    const checkQuery = `
      SELECT id FROM account_applications 
      WHERE id = ? AND status = 'pending'
    `;

    const [application] = await executeQuery(checkQuery, [id]);

    if (!application) {
      return res.status(404).json({
        status: 'error',
        message: 'Solicitação não encontrada ou já processada'
      });
    }

    // Atualizar status da solicitação
    const updateQuery = `
      UPDATE account_applications 
      SET status = 'rejected', rejected_by = ?, rejected_at = NOW(), rejection_reason = ?
      WHERE id = ?
    `;

    await executeQuery(updateQuery, [userId, reason.trim(), id]);

    res.status(200).json({
      status: 'success',
      message: 'Solicitação rejeitada com sucesso'
    });

  } catch (error) {
    console.error('[ERROR] Erro ao rejeitar solicitação:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Criar solicitação de abertura de conta para cliente individual
 * POST /api/approvals/accounts/individual
 */
router.post('/accounts/individual', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const {
      client_id,
      account_type = 'corrente',
      currency_id = 1,
      initial_deposit = 0,
      overdraft_limit = 0,
      branch_id,
      second_holder_id
    } = req.body;

    // Validações básicas
    if (!client_id || !branch_id) {
      return res.status(400).json({
        status: 'error',
        message: 'Dados obrigatórios não fornecidos',
        errors: {
          client_id: !client_id ? 'Cliente é obrigatório' : null,
          branch_id: !branch_id ? 'Balcão é obrigatório' : null
        }
      });
    }

    // Validar segundo titular para contas Junior
    if (account_type === 'junior' && !second_holder_id) {
      return res.status(400).json({
        status: 'error',
        message: 'Segundo titular é obrigatório para contas Junior',
        errors: {
          second_holder_id: 'Segundo titular é obrigatório para contas Junior'
        }
      });
    }

    // Verificar se cliente existe
    const clientQuery = `
      SELECT id, full_name, client_type
      FROM clients
      WHERE id = ? AND client_type = 'individual' AND status = 'pending'
    `;
    const [client] = await executeQuery(clientQuery, [client_id]);

    if (!client) {
      return res.status(404).json({
        status: 'error',
        message: 'Cliente individual não encontrado ou não está pendente de aprovação'
      });
    }

    // Verificar se segundo titular existe (se fornecido)
    let secondHolder = null;
    if (second_holder_id) {
      const secondHolderQuery = `
        SELECT id, full_name, client_type
        FROM clients
        WHERE id = ? AND client_type = 'individual' AND status = 'pending'
      `;
      const [secondHolderResult] = await executeQuery(secondHolderQuery, [second_holder_id]);

      if (!secondHolderResult) {
        return res.status(404).json({
          status: 'error',
          message: 'Segundo titular não encontrado ou não está pendente de aprovação'
        });
      }

      // Verificar se não é o mesmo cliente
      if (client_id === second_holder_id) {
        return res.status(400).json({
          status: 'error',
          message: 'O segundo titular deve ser diferente do primeiro titular'
        });
      }

      secondHolder = secondHolderResult;
    }

    // Verificar se já existe solicitação pendente para este cliente
    const existingQuery = `
      SELECT id FROM account_applications
      WHERE client_id = ? AND status = 'pending'
    `;
    const existing = await executeQuery(existingQuery, [client_id]);

    if (existing.length > 0) {
      return res.status(409).json({
        status: 'error',
        message: 'Já existe uma solicitação pendente para este cliente'
      });
    }

    // Criar solicitação
    const applicationId = uuidv4();
    const insertQuery = `
      INSERT INTO account_applications (
        id, client_id, account_type, currency_id, initial_deposit,
        overdraft_limit, branch_id, status, created_by, second_holder_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?)
    `;

    await executeQuery(insertQuery, [
      applicationId,
      client_id,
      account_type,
      currency_id,
      initial_deposit,
      overdraft_limit,
      branch_id,
      req.user.id,
      second_holder_id || null
    ]);

    res.status(201).json({
      status: 'success',
      message: 'Solicitação de abertura de conta criada com sucesso',
      data: {
        application_id: applicationId,
        client_name: client.full_name,
        account_type,
        status: 'pending'
      }
    });

  } catch (error) {
    console.error('[ERROR] Erro ao criar solicitação individual:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Criar solicitação de abertura de conta para cliente empresa
 * POST /api/approvals/accounts/company
 */
router.post('/accounts/company', authorize('admin', 'gerente', 'caixa'), async (req, res) => {
  try {
    const {
      client_id,
      account_type = 'corrente',
      currency_id = 1,
      initial_deposit = 0,
      overdraft_limit = 0,
      branch_id
    } = req.body;

    // Validações básicas
    if (!client_id || !branch_id) {
      return res.status(400).json({
        status: 'error',
        message: 'Dados obrigatórios não fornecidos',
        errors: {
          client_id: !client_id ? 'Cliente é obrigatório' : null,
          branch_id: !branch_id ? 'Balcão é obrigatório' : null
        }
      });
    }

    // Verificar se cliente existe
    const clientQuery = `
      SELECT id, company_name, client_type
      FROM clients
      WHERE id = ? AND client_type = 'company' AND status = 'pending'
    `;
    const [client] = await executeQuery(clientQuery, [client_id]);

    if (!client) {
      return res.status(404).json({
        status: 'error',
        message: 'Cliente empresa não encontrado ou não está pendente de aprovação'
      });
    }

    // Verificar se já existe solicitação pendente para este cliente
    const existingQuery = `
      SELECT id FROM account_applications
      WHERE client_id = ? AND status = 'pending'
    `;
    const existing = await executeQuery(existingQuery, [client_id]);

    if (existing.length > 0) {
      return res.status(409).json({
        status: 'error',
        message: 'Já existe uma solicitação pendente para este cliente'
      });
    }

    // Criar solicitação
    const applicationId = uuidv4();
    const insertQuery = `
      INSERT INTO account_applications (
        id, client_id, account_type, currency_id, initial_deposit,
        overdraft_limit, branch_id, status, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?)
    `;

    await executeQuery(insertQuery, [
      applicationId,
      client_id,
      account_type,
      currency_id,
      initial_deposit,
      overdraft_limit,
      branch_id,
      req.user.id
    ]);

    res.status(201).json({
      status: 'success',
      message: 'Solicitação de abertura de conta criada com sucesso',
      data: {
        application_id: applicationId,
        client_name: client.company_name,
        account_type,
        status: 'pending'
      }
    });

  } catch (error) {
    console.error('[ERROR] Erro ao criar solicitação empresa:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erro interno do servidor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Função auxiliar para gerar número de conta único
 */
async function generateAccountNumber() {
  const prefix = '0001'; // Prefixo do banco
  let accountNumber;
  let exists = true;

  while (exists) {
    // Gerar 8 dígitos aleatórios
    const randomDigits = Math.floor(******** + Math.random() * ********);
    accountNumber = `${prefix}${randomDigits}`;

    // Verificar se já existe
    const checkQuery = 'SELECT id FROM accounts WHERE account_number = ?';
    const result = await executeQuery(checkQuery, [accountNumber]);
    exists = result.length > 0;
  }

  return accountNumber;
}

module.exports = router;
