import { makeRequest } from '@/utils/api';

// Interfaces para sessões de caixa
// Apenas denominações de 200 Kz para cima conforme requisitos do sistema
export interface CashDenominations {
  notes_10000: number;
  notes_5000: number;
  notes_2000: number;
  notes_1000: number;
  notes_500: number;
  notes_200: number;
}

export interface OpenSessionRequest {
  cash_register_number: string;
  opening_balance: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface CloseSessionRequest {
  session_id: string;
  closing_balance: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface CloseAndDeliverRequest {
  closing_balance: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface CashRegisterBalance {
  cash_register_id: number;
  cash_register_number: string;
  available_balance: number;
  currency: string;
}

export interface CashRegisterSession {
  id: string;
  user_id: string;
  user_name: string;
  cash_register_id: number;
  cash_register_number: string;
  cash_register_description?: string;
  branch_name?: string;
  opening_balance: number;
  closing_balance?: number;
  current_balance: number;
  difference?: number;
  status: 'open' | 'closed';
  opened_at: string;
  closed_at?: string;
  notes?: string;
  opening_denominations?: CashDenominations;
  closing_denominations?: CashDenominations;
}

class CashRegisterSessionService {
  // Abrir sessão de caixa
  async openSession(data: OpenSessionRequest): Promise<CashRegisterSession> {
    const response = await makeRequest<{ session: CashRegisterSession }>('/cash-registers/sessions/open', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.session;
    }
    throw new Error(response.message || 'Erro ao abrir sessão de caixa');
  }

  // Fechar sessão de caixa
  async closeSession(data: CloseSessionRequest): Promise<CashRegisterSession> {
    const response = await makeRequest<{ session: CashRegisterSession }>('/cash-registers/sessions/close', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.session;
    }
    throw new Error(response.message || 'Erro ao fechar sessão de caixa');
  }

  // Fechar sessão de caixa e criar entrega automática para tesoureiro
  async closeAndDeliverSession(data: CloseAndDeliverRequest): Promise<{ session: CashRegisterSession; delivery: any }> {
    const response = await makeRequest<{ session: CashRegisterSession; delivery: any }>('/cash-registers/sessions/close-and-deliver', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao fechar sessão e criar entrega');
  }

  // Obter sessão ativa do usuário
  async getCurrentSession(): Promise<CashRegisterSession | null> {
    const response = await makeRequest<{ session: CashRegisterSession | null }>('/cash-registers/sessions/current');

    if (response.data) {
      return response.data.session;
    }
    throw new Error(response.message || 'Erro ao carregar sessão ativa');
  }

  // Obter saldo disponível de um caixa específico
  async getCashRegisterBalance(cashRegisterId: number): Promise<CashRegisterBalance> {
    const response = await makeRequest<CashRegisterBalance>(`/cash-registers/${cashRegisterId}/balance`);

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao carregar saldo do caixa');
  }

  // Obter informações do tesoureiro responsável pela agência do caixa
  async getCashRegisterTreasurer(cashRegisterId: number): Promise<{
    cash_register_id: number;
    treasurer: {
      id: string;
      full_name: string;
      email: string;
      branch_name: string;
    };
  }> {
    const response = await makeRequest<{
      cash_register_id: number;
      treasurer: {
        id: string;
        full_name: string;
        email: string;
        branch_name: string;
      };
    }>(`/cash-registers/${cashRegisterId}/treasurer`);

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao carregar informações do tesoureiro');
  }

  // Listar sessões (para administradores)
  async getSessions(filters: {
    user_id?: string;
    cash_register_id?: number;
    status?: 'open' | 'closed';
    start_date?: string;
    end_date?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{ sessions: CashRegisterSession[]; pagination: any }> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await makeRequest<{ sessions: CashRegisterSession[]; pagination: any }>(
      `/cash-registers/sessions?${queryParams.toString()}`
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao carregar sessões de caixa');
  }

  // Calcular total das denominações
  calculateDenominationsTotal(denominations: CashDenominations): number {
    return (
      denominations.notes_10000 * 10000 +
      denominations.notes_5000 * 5000 +
      denominations.notes_2000 * 2000 +
      denominations.notes_1000 * 1000 +
      denominations.notes_500 * 500 +
      denominations.notes_200 * 200
    );
  }

  // Formatar valor monetário
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  }

  // Validar denominações
  validateDenominations(denominations: CashDenominations, expectedTotal: number): { isValid: boolean; error?: string } {
    const calculatedTotal = this.calculateDenominationsTotal(denominations);
    
    if (Math.abs(calculatedTotal - expectedTotal) > 0.01) {
      return {
        isValid: false,
        error: `Total das denominações (${this.formatCurrency(calculatedTotal)}) não confere com o valor informado (${this.formatCurrency(expectedTotal)})`
      };
    }
    
    return { isValid: true };
  }

  // Obter denominações vazias
  getEmptyDenominations(): CashDenominations {
    return {
      notes_10000: 0,
      notes_5000: 0,
      notes_2000: 0,
      notes_1000: 0,
      notes_500: 0,
      notes_200: 0
    };
  }

  // Obter configuração das denominações para exibição
  getDenominationConfig(): Array<{
    key: keyof CashDenominations;
    label: string;
    value: number;
    type: 'note' | 'coin';
  }> {
    return [
      { key: 'notes_10000', label: '10.000 Kz', value: 10000, type: 'note' },
      { key: 'notes_5000', label: '5.000 Kz', value: 5000, type: 'note' },
      { key: 'notes_2000', label: '2.000 Kz', value: 2000, type: 'note' },
      { key: 'notes_1000', label: '1.000 Kz', value: 1000, type: 'note' },
      { key: 'notes_500', label: '500 Kz', value: 500, type: 'note' },
      { key: 'notes_200', label: '200 Kz', value: 200, type: 'note' }
    ];
  }

  // Formatar duração da sessão
  formatSessionDuration(openedAt: string, closedAt?: string): string {
    const start = new Date(openedAt);
    const end = closedAt ? new Date(closedAt) : new Date();
    
    const diffMs = end.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    }
    return `${diffMinutes}m`;
  }

  // Obter status badge para sessão
  getSessionStatusBadge(status: 'open' | 'closed'): { variant: string; label: string } {
    switch (status) {
      case 'open':
        return { variant: 'default', label: 'Aberta' };
      case 'closed':
        return { variant: 'secondary', label: 'Fechada' };
      default:
        return { variant: 'outline', label: 'Desconhecido' };
    }
  }
}

export const cashRegisterSessionService = new CashRegisterSessionService();
