// API Configuration for K-Bank Frontend
// Configuração da API para comunicação com o backend

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Configuração base do axios ou fetch
export const API_CONFIG = {
  baseURL: `${API_BASE_URL}/api`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Endpoints da API
export const API_ENDPOINTS = {
  // Autenticação
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    ME: '/auth/me',
    CHECK: '/auth/check',
  },
  // Utilizadores
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    UPDATE: (id: string) => `/users/${id}`,
    DELETE: (id: string) => `/users/${id}`,
    PROFILE: '/users/profile',
  },
  // <PERSON>l<PERSON>ões
  BRANCHES: {
    LIST: '/branches',
    CREATE: '/branches',
    UPDATE: (id: string) => `/branches/${id}`,
    DELETE: (id: string) => `/branches/${id}`,
    GET: (id: string) => `/branches/${id}`,
  },
  // Roles
  ROLES: {
    LIST: '/roles',
    CREATE: '/roles',
    UPDATE: (id: string) => `/roles/${id}`,
    DELETE: (id: string) => `/roles/${id}`,
    GET: (id: string) => `/roles/${id}`,
  },
  // Clientes
  CLIENTS: {
    LIST: '/clients',
    CREATE_INDIVIDUAL: '/clients/individual',
    CREATE_COMPANY: '/clients/company',
    GET: (id: string) => `/clients/${id}`,
    UPDATE: (id: string) => `/clients/${id}`,
    DELETE: (id: string) => `/clients/${id}`,
  },
  // Contas
  ACCOUNTS: {
    BASE: '/accounts',
    LIST: '/accounts',
    CREATE: '/accounts',
    UPDATE: (id: string) => `/accounts/${id}`,
    DELETE: (id: string) => `/accounts/${id}`,
  },
  // Transferências
  TRANSFERS: {
    LIST: '/transfers',
    CREATE: '/transfers',
    UPDATE: (id: string) => `/transfers/${id}`,
  },
  // Caixa
  CASH_REGISTER: {
    LIST: '/cash-register',
    OPEN: '/cash-register/open',
    CLOSE: '/cash-register/close',
    SESSIONS: '/cash-register/sessions',
  },
  // Tesouraria
  TREASURY: {
    LIST: '/treasury',
    OPERATIONS: '/treasury/operations',
  },
  // Cartões
  CARDS: {
    LIST: '/cards',
    CREATE: '/cards',
    UPDATE: (id: string) => `/cards/${id}`,
  },
  // Câmbios
  EXCHANGE: {
    RATES: '/exchange/rates',
    CONVERT: '/exchange/convert',
  },
  // Seguros
  INSURANCE: {
    POLICIES: '/insurance/policies',
    CREATE: '/insurance/policies',
  },
  // ATM
  ATM: {
    LIST: '/atm',
    OPERATIONS: '/atm/operations',
  },
  // Relatórios
  REPORTS: {
    DASHBOARD: '/reports/dashboard',
    TRANSACTIONS: '/reports/transactions',
    CLIENTS: '/reports/clients',
  },
  // Auditoria
  AUDIT: {
    USERS: '/audit/users',
    SYSTEM: '/audit/system',
    SECURITY: '/audit/security',
    SUMMARY: '/audit/summary',
  },
};

// Tipos de resposta da API
export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  error?: string;
}

export interface ApiError {
  status: 'error';
  message: string;
  code?: string;
  details?: any;
}

// Helper para construir URLs
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.baseURL}${endpoint}`;
};

// Helper para headers com autenticação
export const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem('twins-bank-token');
  return {
    ...API_CONFIG.headers,
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

export default API_CONFIG;
