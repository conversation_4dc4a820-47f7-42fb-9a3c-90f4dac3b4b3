-- Migration: Add second_holder_id column to account_applications table
-- Date: 2025-01-27
-- Description: Add support for second holder in account applications for Junior and Joint accounts

-- Add second_holder_id column to account_applications table
ALTER TABLE account_applications 
ADD COLUMN second_holder_id VARCHAR(36) NULL 
AFTER branch_id;

-- Add foreign key constraint to ensure second_holder_id references a valid client
ALTER TABLE account_applications 
ADD CONSTRAINT fk_account_applications_second_holder 
FOREIGN KEY (second_holder_id) REFERENCES clients(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add index for better performance on second_holder_id queries
CREATE INDEX idx_account_applications_second_holder_id 
ON account_applications(second_holder_id);

-- Add comment to document the column purpose
ALTER TABLE account_applications 
MODIFY COLUMN second_holder_id VARCHAR(36) NULL 
COMMENT 'ID do segundo titular para contas Junior e Conjunta';
