import { api } from '@/utils/api';

export interface CounterBalance {
  balance: number;
  user_id: string;
  user_name: string;
}

export interface CounterMovement {
  id: number;
  user_id: string;
  movement_type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference_id?: string;
  created_by: string;
  created_by_name?: string;
  created_at: string;
}

export interface CounterUser {
  id: string;
  full_name: string;
  email: string;
  is_active: boolean;
  balance: number;
  branch_name?: string;
}

export interface DeliverToCounterRequest {
  counter_user_id: string;
  amount: number;
  denominations?: Record<string, number>;
  notes?: string;
}

export interface DeliverToTreasurerRequest {
  amount: number;
  notes?: string;
}

export interface PaginatedMovements {
  movements: CounterMovement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class CounterService {
  /**
   * Obter saldo do balcão do utilizador atual
   */
  async getBalance(): Promise<CounterBalance> {
    const response = await api.get('/counter/balance');
    return response.data?.data || response.data;
  }

  /**
   * Obter histórico de movimentos do balcão
   */
  async getMovements(page: number = 1, limit: number = 20): Promise<PaginatedMovements> {
    const response = await api.get('/counter/movements', {
      params: { page, limit }
    });
    return response.data?.data || response.data;
  }

  /**
   * Entregar valores do tesoureiro para balcão
   */
  async deliverToCounter(data: DeliverToCounterRequest): Promise<any> {
    const response = await api.post('/counter/deliver-to-counter', data);
    return response.data;
  }

  /**
   * Entregar valores do balcão para tesoureiro
   */
  async deliverToTreasurer(data: DeliverToTreasurerRequest): Promise<any> {
    const response = await api.post('/counter/deliver-to-treasurer', data);
    return response.data;
  }

  /**
   * Listar utilizadores de balcão (para tesoureiros)
   */
  async getCounterUsers(): Promise<CounterUser[]> {
    const response = await api.get('/counter/users');

    // A resposta pode vir diretamente ou dentro de data
    const counterUsers = response.data?.counter_users || response.data?.data?.counter_users || [];
    return counterUsers;
  }

  /**
   * Formatar valor monetário
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Formatar data
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}

export const counterService = new CounterService();
export default counterService;
