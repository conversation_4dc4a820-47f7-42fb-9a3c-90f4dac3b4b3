const multer = require('multer');
const { uploadFile, generateUniqueFileName, deleteFile, deleteFiles, deleteFolder } = require('./supabaseClient');

// Configuração do multer para armazenar ficheiros em memória
const storage = multer.memoryStorage();

// Filtro de tipos de ficheiro permitidos
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'application/pdf',
    'image/webp'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Tipo de ficheiro não permitido: ${file.mimetype}. Tipos permitidos: ${allowedTypes.join(', ')}`), false);
  }
};

// Configuração do multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 10 // Máximo 10 ficheiros por upload
  },
  fileFilter: fileFilter
});

/**
 * Middleware para upload de documentos de cliente
 * Aceita múltiplos campos de ficheiro
 */
const uploadClientDocuments = upload.fields([
  { name: 'imagemBI', maxCount: 1 },
  { name: 'imagemPassaporte', maxCount: 1 },
  { name: 'imagemCartaoResidente', maxCount: 1 },
  { name: 'imagemAssinatura', maxCount: 1 },
  { name: 'imagemDeclaracaoServico', maxCount: 1 },
  { name: 'certidaoComercial', maxCount: 1 },
  { name: 'nifEmpresa', maxCount: 1 },
  { name: 'alvaraFuncionamento', maxCount: 1 },
  { name: 'estatutosSociais', maxCount: 1 },
  { name: 'actaConstituicao', maxCount: 1 }
]);

/**
 * Middleware para upload de avatar de utilizador
 */
const uploadAvatar = upload.single('avatar');

/**
 * Processar upload de documentos para Supabase
 * @param {Object} files - Ficheiros do multer
 * @param {string} clientId - ID do cliente
 * @param {string} clientType - Tipo de cliente ('individual' ou 'company')
 * @returns {Promise<Object>} URLs dos ficheiros carregados
 */
async function processDocumentUploads(files, clientId, clientType = 'individual') {
  const uploadedFiles = {};
  const bucket = 'bank_documents';

  try {
    // Processar cada tipo de documento
    for (const [fieldName, fileArray] of Object.entries(files)) {
      if (fileArray && fileArray.length > 0) {
        const file = fileArray[0];
        
        // Gerar nome único para o ficheiro
        const uniqueFileName = generateUniqueFileName(
          file.originalname, 
          `${clientType}_${clientId}_${fieldName}`
        );
        
        // Fazer upload para Supabase
        const uploadResult = await uploadFile(
          bucket,
          `clients/${clientId}/${uniqueFileName}`,
          file.buffer,
          {
            contentType: file.mimetype,
            upsert: true
          }
        );

        uploadedFiles[fieldName] = {
          originalName: file.originalname,
          fileName: uniqueFileName,
          path: uploadResult.path,
          publicUrl: uploadResult.publicUrl,
          size: file.size,
          mimetype: file.mimetype
        };
      }
    }

    return uploadedFiles;
  } catch (error) {
    console.error('Erro no processamento de uploads:', error);
    throw new Error(`Erro no upload de documentos: ${error.message}`);
  }
}

/**
 * Processar upload de avatar para Supabase
 * @param {Object} file - Ficheiro do multer
 * @param {string} userId - ID do utilizador
 * @returns {Promise<Object>} URL do avatar carregado
 */
async function processAvatarUpload(file, userId) {
  const bucket = 'bank_avatars';

  try {
    if (!file) {
      throw new Error('Nenhum ficheiro fornecido');
    }

    // Gerar nome único para o avatar
    const uniqueFileName = generateUniqueFileName(
      file.originalname,
      `user_${userId}`
    );

    // Fazer upload para Supabase
    const uploadResult = await uploadFile(
      bucket,
      `users/${userId}/${uniqueFileName}`,
      file.buffer,
      {
        contentType: file.mimetype,
        upsert: true
      }
    );

    return {
      originalName: file.originalname,
      fileName: uniqueFileName,
      path: uploadResult.path,
      publicUrl: uploadResult.publicUrl,
      size: file.size,
      mimetype: file.mimetype
    };
  } catch (error) {
    console.error('Erro no upload de avatar:', error);
    throw new Error(`Erro no upload de avatar: ${error.message}`);
  }
}

/**
 * Middleware de tratamento de erros de upload
 */
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        status: 'error',
        message: 'Ficheiro muito grande. Tamanho máximo: 5MB'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        status: 'error',
        message: 'Muitos ficheiros. Máximo permitido por campo: 1'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        status: 'error',
        message: 'Campo de ficheiro não esperado'
      });
    }
  }

  if (error.message.includes('Tipo de ficheiro não permitido')) {
    return res.status(400).json({
      status: 'error',
      message: error.message
    });
  }

  // Erro genérico
  return res.status(500).json({
    status: 'error',
    message: 'Erro interno no upload de ficheiros'
  });
};

/**
 * Eliminar documentos de cliente do Supabase
 * @param {string} clientId - ID do cliente
 * @param {string} clientType - Tipo de cliente ('individual' ou 'company')
 * @returns {Promise<boolean>} True se eliminados com sucesso
 */
async function deleteClientDocuments(clientId, clientType = 'individual') {
  const bucket = 'bank_documents';
  const folderPath = `clients/${clientId}`;

  try {
    console.log(`Eliminando documentos do cliente ${clientId} do Supabase...`);

    // Eliminar toda a pasta do cliente
    await deleteFolder(bucket, folderPath);

    console.log(`Documentos do cliente ${clientId} eliminados com sucesso do Supabase`);
    return true;
  } catch (error) {
    console.error(`Erro ao eliminar documentos do cliente ${clientId}:`, error);
    throw new Error(`Erro ao eliminar documentos do cliente: ${error.message}`);
  }
}

/**
 * Eliminar avatar de utilizador do Supabase
 * @param {string} userId - ID do utilizador
 * @returns {Promise<boolean>} True se eliminado com sucesso
 */
async function deleteUserAvatar(userId) {
  const bucket = 'bank_avatars';
  const folderPath = `users/${userId}`;

  try {
    console.log(`Eliminando avatar do utilizador ${userId} do Supabase...`);

    // Eliminar toda a pasta do utilizador
    await deleteFolder(bucket, folderPath);

    console.log(`Avatar do utilizador ${userId} eliminado com sucesso do Supabase`);
    return true;
  } catch (error) {
    console.error(`Erro ao eliminar avatar do utilizador ${userId}:`, error);
    throw new Error(`Erro ao eliminar avatar do utilizador: ${error.message}`);
  }
}

/**
 * Eliminar ficheiro específico do Supabase
 * @param {string} bucket - Nome do bucket ('bank_documents' ou 'bank_avatars')
 * @param {string} filePath - Caminho completo do ficheiro
 * @returns {Promise<boolean>} True se eliminado com sucesso
 */
async function deleteSpecificFile(bucket, filePath) {
  try {
    console.log(`Eliminando ficheiro específico: ${bucket}/${filePath}`);

    await deleteFile(bucket, filePath);

    console.log(`Ficheiro eliminado com sucesso: ${bucket}/${filePath}`);
    return true;
  } catch (error) {
    console.error(`Erro ao eliminar ficheiro ${filePath}:`, error);
    throw new Error(`Erro ao eliminar ficheiro: ${error.message}`);
  }
}

module.exports = {
  uploadClientDocuments,
  uploadAvatar,
  processDocumentUploads,
  processAvatarUpload,
  handleUploadError,
  deleteClientDocuments,
  deleteUserAvatar,
  deleteSpecificFile
};
