import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { approvalService, AccountApplication } from '@/services/approvalService';
import { extractAndTranslateError } from '@/utils/errorTranslator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import {
  CheckCircle,
  XCircle,
  Clock,
  Search,
  Filter,
  RefreshCw,
  User,
  Building2,
  Calendar,
  FileText,
  Eye
} from 'lucide-react';
import ApplicationViewModal from '@/components/modals/ApplicationViewModal';
import RejectApplicationModal from '@/components/modals/RejectApplicationModal';

// Interface já importada do approvalService

const AprovacaoContas: React.FC = () => {
  const [applications, setApplications] = useState<AccountApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isProcessing, setIsProcessing] = useState<string | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedApplicationId, setSelectedApplicationId] = useState<string>('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [applicationToReject, setApplicationToReject] = useState<AccountApplication | null>(null);
  const { toast } = useToast();

  // Carregar solicitações do backend
  const loadApplications = async () => {
    try {
      setLoading(true);
      const response = await approvalService.getAccountApplications({
        search: searchTerm,
        status: statusFilter === 'all' ? undefined : statusFilter,
        limit: 100
      });
      setApplications(response.applications);
    } catch (error: any) {
      console.error('Erro ao carregar solicitações:', error);
      const translatedError = extractAndTranslateError(error);
      toast({
        title: "Erro ao Carregar Solicitações",
        description: translatedError,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadApplications();
  }, [searchTerm, statusFilter]);

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'default',
      approved: 'default',
      rejected: 'destructive'
    } as const;

    const labels = {
      pending: 'Pendente',
      approved: 'Aprovado',
      rejected: 'Rejeitado'
    };

    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };

    return (
      <Badge className={colors[status as keyof typeof colors]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  const getAccountTypeLabel = (type: string) => {
    const labels = {
      corrente: 'Conta Corrente',
      poupanca: 'Conta Poupança',
      salario: 'Conta Salário',
      junior: 'Conta Jovem'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const handleViewApplication = (applicationId: string) => {
    setSelectedApplicationId(applicationId);
    setShowViewModal(true);
  };

  const handleApprove = async (applicationId: string) => {
    if (isProcessing) return;

    try {
      setIsProcessing(applicationId);
      const result = await approvalService.approveApplication(applicationId);

      // Atualizar a lista local
      setApplications(prev =>
        prev.map(app =>
          app.id === applicationId ? { ...app, status: 'approved' as const } : app
        )
      );

      toast({
        title: "Conta Aprovada com Sucesso!",
        description: `Conta ${result.account_number} criada e aprovada.`,
      });
    } catch (error: any) {
      console.error('Erro ao aprovar solicitação:', error);
      const translatedError = extractAndTranslateError(error);
      toast({
        title: "Erro ao Aprovar Solicitação",
        description: translatedError,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(null);
    }
  };

  const handleReject = (application: AccountApplication) => {
    if (isProcessing) return;
    setApplicationToReject(application);
    setShowRejectModal(true);
  };

  const handleConfirmReject = async (reason: string) => {
    if (!applicationToReject) return;

    try {
      setIsProcessing(applicationToReject.id);
      await approvalService.rejectApplication(applicationToReject.id, reason);

      // Atualizar a lista local
      setApplications(prev =>
        prev.map(app =>
          app.id === applicationToReject.id ? { ...app, status: 'rejected' as const, rejection_reason: reason } : app
        )
      );

      toast({
        title: "Solicitação Rejeitada",
        description: "A solicitação foi rejeitada com sucesso.",
      });

      // Fechar modal e limpar estado
      setShowRejectModal(false);
      setApplicationToReject(null);
    } catch (error: any) {
      console.error('Erro ao rejeitar solicitação:', error);
      const translatedError = extractAndTranslateError(error);
      toast({
        title: "Erro ao Rejeitar Solicitação",
        description: translatedError,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.document_number.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const pendingCount = applications.filter(app => app.status === 'pending').length;
  const approvedCount = applications.filter(app => app.status === 'approved').length;
  const rejectedCount = applications.filter(app => app.status === 'rejected').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Aprovação de Contas</h1>
          <p className="text-muted-foreground">
            Gerencie as solicitações de abertura de contas bancárias
          </p>
        </div>
        <Button onClick={loadApplications} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingCount}</div>
            <p className="text-xs text-muted-foreground">Aguardando aprovação</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aprovadas</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{approvedCount}</div>
            <p className="text-xs text-muted-foreground">Contas aprovadas</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejeitadas</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rejectedCount}</div>
            <p className="text-xs text-muted-foreground">Contas rejeitadas</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Pesquisar por nome ou documento..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="all">Todos os Status</option>
          <option value="pending">Pendentes</option>
          <option value="approved">Aprovadas</option>
          <option value="rejected">Rejeitadas</option>
        </select>
      </div>

      {/* Applications List */}
      <Card>
        <CardHeader>
          <CardTitle>
            Solicitações de Abertura de Contas ({filteredApplications.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">Carregando solicitações...</p>
            </div>
          ) : filteredApplications.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Nenhuma solicitação encontrada</p>
              <p className="text-sm">Tente ajustar os filtros de pesquisa</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredApplications.map((application) => (
                <div key={application.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="flex items-center gap-2">
                          {application.client_type === 'individual' ? (
                            <User className="h-5 w-5 text-blue-600" />
                          ) : (
                            <Building2 className="h-5 w-5 text-purple-600" />
                          )}
                          <h3 className="font-semibold text-lg">
                            {application.client_name}
                          </h3>
                        </div>
                        {getStatusBadge(application.status)}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Tipo de Conta:</span>
                          <span>{getAccountTypeLabel(application.account_type)}</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="font-medium">Documento:</span>
                          <span>{application.document_number}</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="font-medium">Balcão:</span>
                          <span>{application.branch_name}</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(application.created_at)}</span>
                        </div>
                      </div>

                      {application.monthly_income && (
                        <div className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Rendimento Mensal:</span>
                          <span className="ml-2">{formatCurrency(application.monthly_income)}</span>
                        </div>
                      )}

                      <div className="mt-2 text-sm text-gray-600">
                        <span className="font-medium">Solicitado por:</span>
                        <span className="ml-2">{application.requested_by}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewApplication(application.id)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Ver
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Visualizar detalhes da solicitação</p>
                        </TooltipContent>
                      </Tooltip>
                      {application.status === 'pending' && (
                        <>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleApprove(application.id)}
                                disabled={isProcessing === application.id}
                                className="text-green-600 hover:text-green-700 disabled:opacity-50"
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                {isProcessing === application.id ? 'Aprovando...' : 'Aprovar'}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Aprovar solicitação e criar conta automaticamente</p>
                            </TooltipContent>
                          </Tooltip>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleReject(application)}
                                disabled={isProcessing === application.id}
                                className="text-red-600 hover:text-red-700 disabled:opacity-50"
                              >
                                <XCircle className="h-4 w-4 mr-1" />
                                {isProcessing === application.id ? 'Rejeitando...' : 'Rejeitar'}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Rejeitar solicitação de abertura de conta</p>
                            </TooltipContent>
                          </Tooltip>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal de Visualização de Aplicação */}
      <ApplicationViewModal
        applicationId={selectedApplicationId}
        isOpen={showViewModal}
        onClose={() => {
          setShowViewModal(false);
          setSelectedApplicationId('');
        }}
      />

      {/* Modal de Rejeição */}
      <RejectApplicationModal
        isOpen={showRejectModal}
        onClose={() => {
          setShowRejectModal(false);
          setApplicationToReject(null);
        }}
        onConfirm={handleConfirmReject}
        isLoading={isProcessing === applicationToReject?.id}
        applicationId={applicationToReject?.id}
        clientName={applicationToReject?.client_name}
      />
    </div>
  );
};

export default AprovacaoContas;
