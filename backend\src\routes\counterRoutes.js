const express = require('express');
const { authorize } = require('../auth/middleware');
const { catchAsync } = require('../core/errorHandler');
const { AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const Joi = require('joi');

const router = express.Router();

// Schemas de validação
const deliverToCounterSchema = Joi.object({
  counter_user_id: Joi.string().uuid().required(),
  amount: Joi.number().positive().required(),
  denominations: Joi.object().optional(),
  notes: Joi.string().max(500).optional()
});

const deliverToTreasurerSchema = Joi.object({
  amount: Joi.number().positive().required(),
  notes: Joi.string().max(500).optional()
});

/**
 * GET /api/counter/balance
 * Obter saldo do balcão do utilizador atual
 */
router.get('/balance', authorize('balcao'), catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  // Buscar saldo atual do balcão
  const balances = await executeQuery(
    'SELECT balance FROM counter_balances WHERE user_id = ?',
    [userId]
  );

  const currentBalance = balances.length > 0 ? balances[0].balance : 0;

  res.status(200).json({
    status: 'success',
    data: {
      balance: currentBalance,
      user_id: userId,
      user_name: req.user.full_name
    }
  });
}));

/**
 * GET /api/counter/movements
 * Obter histórico de movimentos do balcão
 */
router.get('/movements', authorize('balcao'), catchAsync(async (req, res, next) => {
  const userId = req.user.id;
  const { page = 1, limit = 20 } = req.query;
  const offset = (page - 1) * limit;

  // Buscar movimentos do utilizador
  const movements = await executeQuery(
    `SELECT cm.*, u.full_name as created_by_name
     FROM counter_movements cm
     LEFT JOIN users u ON cm.created_by = u.id
     WHERE cm.user_id = ?
     ORDER BY cm.created_at DESC
     LIMIT ? OFFSET ?`,
    [userId, parseInt(limit), parseInt(offset)]
  );

  // Contar total de movimentos
  const totalResult = await executeQuery(
    'SELECT COUNT(*) as total FROM counter_movements WHERE user_id = ?',
    [userId]
  );
  const total = totalResult[0].total;

  res.status(200).json({
    status: 'success',
    data: {
      movements,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

/**
 * POST /api/counter/deliver-to-counter
 * Entrega de valores do tesoureiro para balcão (apenas tesoureiro pode fazer)
 */
router.post('/deliver-to-counter', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToCounterSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { counter_user_id, amount, denominations, notes } = value;
  const treasurerId = req.user.id;

  // 2. Verificar se o utilizador de balcão existe
  const counterUsers = await executeQuery(
    `SELECT u.id, u.full_name, u.email, r.name as role_name
     FROM users u
     JOIN roles r ON u.role_id = r.id
     WHERE u.id = ? AND r.name = 'balcao' AND u.is_active = 1`,
    [counter_user_id]
  );

  if (!counterUsers || counterUsers.length === 0) {
    return next(new AppError('Utilizador de balcão não encontrado ou inativo', 404, 'COUNTER_USER_NOT_FOUND'));
  }

  const counterUser = counterUsers[0];

  try {
    // 3. Iniciar transação
    await executeQuery('START TRANSACTION');

    // 4. Verificar/criar registo de saldo do balcão
    const existingBalance = await executeQuery(
      'SELECT balance FROM counter_balances WHERE user_id = ?',
      [counter_user_id]
    );

    let currentBalance = 0;
    if (existingBalance.length > 0) {
      currentBalance = parseFloat(existingBalance[0].balance);
      // Atualizar saldo
      await executeQuery(
        'UPDATE counter_balances SET balance = balance + ?, updated_at = NOW() WHERE user_id = ?',
        [amount, counter_user_id]
      );
    } else {
      // Criar novo registo de saldo
      await executeQuery(
        'INSERT INTO counter_balances (user_id, balance, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
        [counter_user_id, amount]
      );
    }

    // 5. Registar movimento
    await executeQuery(
      `INSERT INTO counter_movements (user_id, movement_type, amount, description, created_by, created_at)
       VALUES (?, 'credit', ?, ?, ?, NOW())`,
      [
        counter_user_id,
        amount,
        `Entrega do Tesoureiro - ${notes || 'Sem observações'}`,
        treasurerId
      ]
    );

    // 6. Confirmar transação
    await executeQuery('COMMIT');

    const newBalance = currentBalance + parseFloat(amount);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao balcão realizada com sucesso',
      data: {
        counter_user: {
          id: counterUser.id,
          name: counterUser.full_name,
          email: counterUser.email
        },
        amount: parseFloat(amount),
        previous_balance: currentBalance,
        new_balance: newBalance,
        treasurer: {
          id: treasurerId,
          name: req.user.full_name
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    throw error;
  }
}));

/**
 * POST /api/counter/deliver-to-treasurer
 * Entrega de valores do balcão para tesoureiro (apenas balcão pode fazer)
 */
router.post('/deliver-to-treasurer', authorize('balcao'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToTreasurerSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { amount, notes } = value;
  const userId = req.user.id;

  // 2. Verificar saldo atual do balcão
  const balances = await executeQuery(
    'SELECT balance FROM counter_balances WHERE user_id = ?',
    [userId]
  );

  if (!balances || balances.length === 0) {
    return next(new AppError('Saldo de balcão não encontrado', 404, 'COUNTER_BALANCE_NOT_FOUND'));
  }

  const currentBalance = parseFloat(balances[0].balance);
  if (currentBalance < amount) {
    return next(new AppError('Saldo insuficiente no balcão', 400, 'INSUFFICIENT_BALANCE'));
  }

  try {
    // 3. Iniciar transação
    await executeQuery('START TRANSACTION');

    // 4. Debitar saldo do balcão
    await executeQuery(
      'UPDATE counter_balances SET balance = balance - ?, updated_at = NOW() WHERE user_id = ?',
      [amount, userId]
    );

    // 5. Registar movimento
    await executeQuery(
      `INSERT INTO counter_movements (user_id, movement_type, amount, description, created_by, created_at)
       VALUES (?, 'debit', ?, ?, ?, NOW())`,
      [
        userId,
        amount,
        `Entrega ao Tesoureiro - ${notes || 'Sem observações'}`,
        userId
      ]
    );

    // 6. Confirmar transação
    await executeQuery('COMMIT');

    const newBalance = currentBalance - parseFloat(amount);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao tesoureiro realizada com sucesso',
      data: {
        amount: parseFloat(amount),
        previous_balance: currentBalance,
        new_balance: newBalance,
        counter_user: {
          id: userId,
          name: req.user.full_name
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    throw error;
  }
}));

/**
 * GET /api/counter/users
 * Listar utilizadores de balcão (para tesoureiros)
 */
router.get('/users', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  const counterUsers = await executeQuery(
    `SELECT u.id, u.full_name, u.email, u.is_active,
            cb.balance,
            b.name as branch_name
     FROM users u
     JOIN roles r ON u.role_id = r.id
     LEFT JOIN counter_balances cb ON u.id = cb.user_id
     LEFT JOIN branches b ON u.branch_id = b.id
     WHERE r.name = 'balcao'
     ORDER BY u.full_name`
  );

  res.status(200).json({
    status: 'success',
    data: {
      counter_users: counterUsers.map(user => ({
        ...user,
        balance: user.balance || 0
      }))
    }
  });
}));

module.exports = router;
