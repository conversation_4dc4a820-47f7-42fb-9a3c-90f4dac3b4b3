/**
 * Utilitários para manipulação de datas e timestamps
 * Garante consistência de timezone em todo o sistema
 */

/**
 * Obtém a data/hora atual em UTC
 * O MySQL fará a conversão para Angola (UTC+1) automaticamente
 * @returns {Date} Data atual em UTC
 */
const getCurrentAngolaTime = () => {
  // Retorna UTC puro - o MySQL converte para Angola timezone (+01:00)
  return new Date();
};

/**
 * Converte uma data para UTC (MySQL fará a conversão para Angola)
 * @param {Date|string} date - Data a converter
 * @returns {Date} Data em UTC para processamento pelo MySQL
 */
const convertToAngolaTime = (date) => {
  if (!date) return null;

  const dateObj = date instanceof Date ? date : new Date(date);
  if (isNaN(dateObj.getTime())) return null;

  // Retorna a data em UTC - MySQL converte para Angola timezone
  return dateObj;
};

/**
 * Formata uma data para string MySQL DATETIME
 * @param {Date} date - Data a formatar
 * @returns {string} String no formato 'YYYY-MM-DD HH:MM:SS'
 */
const formatForMySQL = (date = null) => {
  const targetDate = date || getCurrentAngolaTime();
  
  const year = targetDate.getFullYear();
  const month = String(targetDate.getMonth() + 1).padStart(2, '0');
  const day = String(targetDate.getDate()).padStart(2, '0');
  const hours = String(targetDate.getHours()).padStart(2, '0');
  const minutes = String(targetDate.getMinutes()).padStart(2, '0');
  const seconds = String(targetDate.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * Obtém timestamp atual formatado para MySQL
 * Substitui o uso de NOW() nas queries
 * @returns {string} Timestamp atual no formato MySQL
 */
const getCurrentTimestamp = () => {
  return formatForMySQL();
};

/**
 * Adiciona horas a uma data
 * @param {Date} date - Data base
 * @param {number} hours - Número de horas a adicionar
 * @returns {Date} Nova data com horas adicionadas
 */
const addHours = (date, hours) => {
  const result = new Date(date);
  result.setHours(result.getHours() + hours);
  return result;
};

/**
 * Adiciona dias a uma data
 * @param {Date} date - Data base
 * @param {number} days - Número de dias a adicionar
 * @returns {Date} Nova data com dias adicionados
 */
const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

/**
 * Verifica se uma data é válida
 * @param {any} date - Valor a verificar
 * @returns {boolean} True se for uma data válida
 */
const isValidDate = (date) => {
  return date instanceof Date && !isNaN(date.getTime());
};

/**
 * Formata data para exibição (DD/MM/YYYY HH:MM)
 * @param {Date|string} date - Data a formatar
 * @returns {string} Data formatada
 */
const formatForDisplay = (date) => {
  if (!date) return 'N/A';
  
  const dateObj = date instanceof Date ? date : new Date(date);
  if (!isValidDate(dateObj)) return 'N/A';
  
  const day = String(dateObj.getDate()).padStart(2, '0');
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const year = dateObj.getFullYear();
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  
  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

/**
 * Configurações de timezone para Angola
 */
const TIMEZONE_CONFIG = {
  ANGOLA_OFFSET: 1, // UTC+1
  TIMEZONE_NAME: 'Africa/Luanda',
  LOCALE: 'pt-AO'
};

module.exports = {
  getCurrentAngolaTime,
  convertToAngolaTime,
  formatForMySQL,
  getCurrentTimestamp,
  addHours,
  addDays,
  isValidDate,
  formatForDisplay,
  TIMEZONE_CONFIG
};
