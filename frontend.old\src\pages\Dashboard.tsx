
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import {
  Users,
  Wallet,
  ArrowLeftRight,
  CreditCard,
  DollarSign,
  AlertTriangle,
  TrendingUp,
  Building2
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { CounterBalanceCard } from '@/components/dashboard/CounterBalanceCard';
import { useAuth } from '@/contexts/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();
  // Dados fictícios para os gráficos
  const monthlyData = [
    { name: 'Jan', clients: 10 },
    { name: 'Fev', clients: 30 },
    { name: 'Mar', clients: 120 },
    { name: 'Abr', clients: 250 },
    { name: '<PERSON>', clients: 40 },
    { name: '<PERSON>', clients: 80 },
    { name: 'Jul', clients: 60 }
  ];

  const receiptData = [
    { name: '<PERSON><PERSON><PERSON>', value: 49.5, color: '#3B82F6' },
    { name: 'Euro', value: 25.3, color: '#10B981' },
    { name: 'USD', value: 9.9, color: '#F59E0B' },
    { name: 'SPTR', value: 7.1, color: '#EF4444' },
    { name: 'Outros', value: 8.2, color: '#8B5CF6' }
  ];

  const stats = [
    {
      title: 'Novos Clientes',
      value: '495',
      change: '+5%',
      icon: Users,
      color: 'bg-twins-primary'
    },
    {
      title: 'Total Receita',
      value: '7000.00k',
      change: 'AOA',
      icon: Wallet,
      color: 'bg-twins-secondary'
    },
    {
      title: 'Pedidos de Cartões',
      value: '10',
      change: 'pendentes',
      icon: CreditCard,
      color: 'bg-orange-500'
    },
    {
      title: 'Créditos Cedidos',
      value: '0',
      change: 'hoje',
      icon: DollarSign,
      color: 'bg-twins-primary'
    },
    {
      title: 'Total Clientes',
      value: '495',
      change: 'ativos',
      icon: Users,
      color: 'bg-twins-secondary'
    }
  ];

  const accounts = [
    { type: 'Particular', count: 419 },
    { type: 'Empresas', count: 0 },
    { type: 'Prazo', count: 0 }
  ];

  const balances = [
    { currency: 'Kwanza', amount: 0 },
    { currency: 'USD', amount: 0 },
    { currency: 'GBP', amount: 0 }
  ];

  const transfers = [
    { type: 'Interna', count: 818 },
    { type: 'SPTR', count: 0 },
    { type: 'STC', count: 0 }
  ];

  const cards = [
    { type: 'Requisitados', count: 201 },
    { type: 'Activos', count: 198 },
    { type: 'Anulados', count: 3 }
  ];

  const recentAccounts = [
    { id: '**********', name: 'reginaldo bruno dos santos mujusta', balance: 0, date: '2025-03-26' },
    { id: '**********', name: 'TUBUCA AMORIM JOAQUIM DE OLIVEIRA', balance: 0, date: '2025-03-26' },
    { id: '**********', name: 'Catarina Nayol Jerónimo do Nascimento', balance: 1037875, date: '2025-03-26' }
  ];

  const recentTransfers = [
    { id: '**********', date: '2025-03-26', amount: 300000, description: 'Olavio Alexandre Mira Fernandes', type: 'Transferencia Interna' },
    { id: '**********', date: '2025-03-26', amount: 300000, description: 'Leticia Rosaura Cipriano Félix Maria', type: 'Transferencia Interna' },
    { id: '**********', date: '2025-03-26', amount: 300000, description: 'Catarina Nayol Jerónimo do Nascimento', type: 'Transferencia Interna' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-twins-primary to-twins-secondary p-6 rounded-xl shadow-lg">
        <h1 className="text-3xl font-bold text-white">Dashboard twins_bank</h1>
        <p className="text-white/90">Visão geral das operações bancárias</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-6">
        {stats.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg hover:scale-105 transition-all duration-200 border-l-4 border-l-twins-primary bg-gradient-to-br from-white to-twins-accent/20 dark:from-gray-800 dark:to-twins-accent/10 min-h-[120px] flex flex-col justify-between">
            <CardHeader className="flex flex-row items-center justify-between pb-2 flex-shrink-0">
              <CardTitle className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-tight">
                {stat.title}
              </CardTitle>
              <div className={`p-2.5 rounded-xl ${stat.color} shadow-lg flex-shrink-0`}>
                <stat.icon className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col justify-end">
              <div className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-1">{stat.value}</div>
              <p className="text-xs text-twins-primary font-medium">{stat.change}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Card específico para perfil Balcão */}
        {user?.role === 'balcao' && (
          <CounterBalanceCard />
        )}

        {/* Contas AKZ - Oculto para perfil Balcão */}
        {user?.role !== 'balcao' && (
          <Card className="hover:shadow-lg transition-shadow duration-200 border-t-4 border-t-twins-primary dark:bg-gray-800">
            <CardHeader className="bg-gradient-to-r from-twins-accent/30 to-transparent dark:from-twins-accent/20">
              <CardTitle className="text-lg font-semibold text-twins-primary">Contas AKZ</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              {accounts.map((account, index) => (
                <div key={index} className="flex justify-between items-center p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-twins-accent/10 dark:hover:bg-twins-accent/20 transition-colors">
                  <span className="text-gray-700 dark:text-gray-300 font-medium">{account.type}:</span>
                  <span className="font-bold text-xl text-twins-primary">{account.count}</span>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Saldo Balcão */}
        <Card className="hover:shadow-lg transition-shadow duration-200 border-t-4 border-t-twins-secondary dark:bg-gray-800">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent dark:from-blue-900/20">
            <CardTitle className="text-lg font-semibold text-twins-secondary">Saldo Balcão</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            {balances.map((balance, index) => (
              <div key={index} className="flex justify-between items-center p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-twins-accent/10 dark:hover:bg-twins-accent/20 transition-colors">
                <span className="text-gray-700 dark:text-gray-300 font-medium">{balance.currency}:</span>
                <span className="font-bold text-xl text-twins-secondary">{balance.amount}</span>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Transferências */}
        <Card className="hover:shadow-lg transition-shadow duration-200 border-t-4 border-t-emerald-500 dark:bg-gray-800">
          <CardHeader className="bg-gradient-to-r from-emerald-50 to-transparent dark:from-emerald-900/20">
            <CardTitle className="text-lg font-semibold text-emerald-700 dark:text-emerald-400">Transferências</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            {transfers.map((transfer, index) => (
              <div key={index} className="flex justify-between items-center p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors">
                <span className="text-gray-700 dark:text-gray-300 font-medium">{transfer.type}:</span>
                <span className="font-bold text-xl text-emerald-700 dark:text-emerald-400">{transfer.count}</span>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Cartões */}
        <Card className="hover:shadow-lg transition-shadow duration-200 border-t-4 border-t-orange-500 dark:bg-gray-800">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-transparent dark:from-orange-900/20">
            <CardTitle className="text-lg font-semibold text-orange-700 dark:text-orange-400">Cartões</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            {cards.map((card, index) => (
              <div key={index} className="flex justify-between items-center p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors">
                <span className="text-gray-700 dark:text-gray-300 font-medium">{card.type}:</span>
                <span className="font-semibold text-lg text-gray-900 dark:text-gray-100">{card.count}</span>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Gráfico - Número de Clientes */}
        <Card className="dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="text-lg font-semibold dark:text-gray-100">Gráfico - Números Clientes</CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400">Número Total de Clientes, Anual</p>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="clients" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Gráfico - Total Receitas */}
        <Card className="dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="text-lg font-semibold dark:text-gray-100">Gráfico - Total receitas</CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400">Receitas</p>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={receiptData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  dataKey="value"
                >
                  {receiptData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value}%`, '']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Tables Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Últimas Contas Criadas */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Últimas Contas Criadas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentAccounts.map((account, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="font-medium text-sm">{account.id}</p>
                    <p className="text-xs text-gray-600 truncate max-w-48">{account.name}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{account.balance.toLocaleString()}</p>
                    <p className="text-xs text-gray-600">{account.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Últimas Transações */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Últimas Transações</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentTransfers.map((transfer, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="font-medium text-sm">{transfer.id}</p>
                    <p className="text-xs text-gray-600 truncate max-w-48">{transfer.description}</p>
                    <p className="text-xs text-blue-600">{transfer.type}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{transfer.amount.toLocaleString()}</p>
                    <p className="text-xs text-gray-600">{transfer.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
