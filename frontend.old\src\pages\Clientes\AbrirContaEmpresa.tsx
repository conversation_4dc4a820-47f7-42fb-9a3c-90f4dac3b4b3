import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { clientService } from '@/services/clientService';
import { accountService } from '@/services/accountService';
import { approvalService, CreateApplicationRequest } from '@/services/approvalService';
import { branchService } from '@/services/branchService';
import { CompanyClientForm } from '@/types/client';
import { CreateAccountRequest } from '@/services/accountService';
import { extractAndTranslateError } from '@/utils/errorTranslator';
import { Briefcase, Building, FileText, Phone, Mail, MapPin, Loader2 } from 'lucide-react';

const AbrirContaEmpresa = () => {
  const { toast } = useToast();

  // Estados para controle de loading e submissão
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [branches, setBranches] = useState<any[]>([]);
  const [loadingBranches, setLoadingBranches] = useState(true);

  const [dadosEmpresa, setDadosEmpresa] = useState({
    nomeEmpresa: '',
    nif: '',
    numeroRegistoComercial: '',
    dataConstituicao: '',
    capitalSocial: '',
    tipoSociedade: '',
    actividadePrincipal: '',
    numeroFuncionarios: '',
    nacionalidade: 'Angolana', // Valor padrão
    morada: '',
    provincia: '',
    municipio: '',
    telefone: '',
    email: '',
    website: '',
    representanteLegal: '',
    biRepresentante: '',
    cargoRepresentante: '',
    naturezaConta: '',
    tipoConta: '',
    finalidadeConta: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setDadosEmpresa(prev => ({ ...prev, [field]: value }));
  };

  // Carregar balcões disponíveis
  useEffect(() => {
    const loadBranches = async () => {
      try {
        setLoadingBranches(true);
        const branchList = await branchService.listBranches({
          page: 1,
          limit: 100,
          is_active: true
        });
        setBranches(branchList.branches);
      } catch (error) {
        console.error('Erro ao carregar balcões:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar lista de balcões",
          variant: "destructive"
        });
      } finally {
        setLoadingBranches(false);
      }
    };

    loadBranches();
  }, [toast]);

  const handleSubmit = async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Validar dados obrigatórios
      if (!dadosEmpresa.nomeEmpresa || !dadosEmpresa.nif || !dadosEmpresa.tipoConta) {
        toast({
          title: "Dados Incompletos",
          description: "Por favor, preencha todos os campos obrigatórios",
          variant: "destructive"
        });
        return;
      }

      // Preparar dados do cliente empresa
      const clientData: any = {
        company_name: dadosEmpresa.nomeEmpresa,
        document_type: 'NIF',
        document_number: dadosEmpresa.numeroRegistoComercial || dadosEmpresa.nif,
        nif: dadosEmpresa.nif,
        incorporation_date: dadosEmpresa.dataConstituicao,
        nationality: dadosEmpresa.nacionalidade,
        branch_id: branches.length > 0 ? branches[0].id : 1,
        address: {
          street: dadosEmpresa.morada,
          municipality: dadosEmpresa.municipio,
          province: dadosEmpresa.provincia,
          postal_code: '1000' // Código postal padrão para Luanda
        },
        contacts: {
          ...(dadosEmpresa.telefone && { phone_work: dadosEmpresa.telefone }),
          ...(dadosEmpresa.email && { email_work: dadosEmpresa.email })
        }
      };

      // Adicionar campos opcionais apenas se tiverem valor
      if (dadosEmpresa.actividadePrincipal) {
        clientData.profession = dadosEmpresa.actividadePrincipal;
      }

      // Criar cliente empresa
      const createdClient = await clientService.createCompanyClient(clientData);

      // Preparar dados da solicitação de conta
      const applicationData: CreateApplicationRequest = {
        client_id: createdClient.id,
        account_type: dadosEmpresa.tipoConta as 'corrente' | 'salario' | 'junior',
        currency_id: 1, // AOA
        initial_deposit: 0,
        branch_id: branches.length > 0 ? branches[0].id : 1,
        overdraft_limit: dadosEmpresa.tipoConta === 'corrente' ? 500000 : 0 // Limite maior para empresas
      };

      // Criar solicitação de abertura de conta
      const createdApplication = await approvalService.createCompanyApplication(applicationData);

      toast({
        title: "Solicitação Criada com Sucesso!",
        description: `Solicitação de abertura de conta criada para ${createdClient.company_name}. Aguardando aprovação.`,
      });

      // Limpar formulário após sucesso
      setDadosEmpresa({
        nomeEmpresa: '', nif: '', numeroRegistoComercial: '', dataConstituicao: '',
        capitalSocial: '', tipoSociedade: '', actividadePrincipal: '', numeroFuncionarios: '',
        morada: '', provincia: '', municipio: '', telefone: '', email: '', website: '',
        representanteLegal: '', biRepresentante: '', cargoRepresentante: '',
        naturezaConta: '', tipoConta: '', finalidadeConta: ''
      });

    } catch (error: any) {
      console.error('Erro ao criar conta empresarial:', error);
      const translatedError = extractAndTranslateError(error);
      toast({
        title: "Erro ao Criar Conta",
        description: translatedError,
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Briefcase className="h-8 w-8" />
          Abertura de Conta Empresa
        </h1>
        <p className="text-gray-600 dark:text-gray-400">Registar nova conta empresarial</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Dados da Empresa */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Dados da Empresa
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="nome-empresa">Nome da Empresa <span className="text-red-500">*</span></Label>
              <Input
                id="nome-empresa"
                value={dadosEmpresa.nomeEmpresa}
                onChange={(e) => handleInputChange('nomeEmpresa', e.target.value)}
                placeholder="Nome completo da empresa"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nif">NIF <span className="text-red-500">*</span></Label>
                <Input
                  id="nif"
                  value={dadosEmpresa.nif}
                  onChange={(e) => handleInputChange('nif', e.target.value)}
                  placeholder="Número de identificação fiscal"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="registo-comercial">Nº Registo Comercial <span className="text-red-500">*</span></Label>
                <Input
                  id="registo-comercial"
                  value={dadosEmpresa.numeroRegistoComercial}
                  onChange={(e) => handleInputChange('numeroRegistoComercial', e.target.value)}
                  placeholder="Número do registo comercial"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="data-constituicao">Data de Constituição <span className="text-red-500">*</span></Label>
                <Input
                  id="data-constituicao"
                  type="date"
                  value={dadosEmpresa.dataConstituicao}
                  onChange={(e) => handleInputChange('dataConstituicao', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="capital-social">Capital Social (Kz) <span className="text-red-500">*</span></Label>
                <Input
                  id="capital-social"
                  value={dadosEmpresa.capitalSocial}
                  onChange={(e) => handleInputChange('capitalSocial', e.target.value)}
                  placeholder="Valor do capital social"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tipo-sociedade">Tipo de Sociedade <span className="text-red-500">*</span></Label>
              <Select value={dadosEmpresa.tipoSociedade} onValueChange={(value) => handleInputChange('tipoSociedade', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de sociedade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="lda">Sociedade por Quotas (Lda)</SelectItem>
                  <SelectItem value="sa">Sociedade Anónima (SA)</SelectItem>
                  <SelectItem value="unipessoal">Sociedade Unipessoal</SelectItem>
                  <SelectItem value="cooperativa">Cooperativa</SelectItem>
                  <SelectItem value="outros">Outros</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="actividade-principal">Actividade Principal <span className="text-red-500">*</span></Label>
              <Textarea
                id="actividade-principal"
                value={dadosEmpresa.actividadePrincipal}
                onChange={(e) => handleInputChange('actividadePrincipal', e.target.value)}
                placeholder="Descreva a actividade principal da empresa"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nacionalidade">Nacionalidade <span className="text-red-500">*</span></Label>
                <Select value={dadosEmpresa.nacionalidade} onValueChange={(value) => handleInputChange('nacionalidade', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a nacionalidade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Angolana">Angolana</SelectItem>
                    <SelectItem value="Portuguesa">Portuguesa</SelectItem>
                    <SelectItem value="Brasileira">Brasileira</SelectItem>
                    <SelectItem value="Outras">Outras</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="numero-funcionarios">Número de Funcionários</Label>
                <Select value={dadosEmpresa.numeroFuncionarios} onValueChange={(value) => handleInputChange('numeroFuncionarios', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o número de funcionários" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-5">1-5 funcionários</SelectItem>
                    <SelectItem value="6-20">6-20 funcionários</SelectItem>
                    <SelectItem value="21-50">21-50 funcionários</SelectItem>
                    <SelectItem value="51-100">51-100 funcionários</SelectItem>
                    <SelectItem value="100+">Mais de 100 funcionários</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contactos e Localização */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Contactos e Localização
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="morada">Morada <span className="text-red-500">*</span></Label>
              <Textarea
                id="morada"
                value={dadosEmpresa.morada}
                onChange={(e) => handleInputChange('morada', e.target.value)}
                placeholder="Endereço completo da empresa"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="provincia">Província <span className="text-red-500">*</span></Label>
                <Select value={dadosEmpresa.provincia} onValueChange={(value) => handleInputChange('provincia', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a província" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Luanda">Luanda</SelectItem>
                    <SelectItem value="Benguela">Benguela</SelectItem>
                    <SelectItem value="Huambo">Huambo</SelectItem>
                    <SelectItem value="Lobito">Lobito</SelectItem>
                    <SelectItem value="Outras">Outras</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="municipio">Município <span className="text-red-500">*</span></Label>
                <Input
                  id="municipio"
                  value={dadosEmpresa.municipio}
                  onChange={(e) => handleInputChange('municipio', e.target.value)}
                  placeholder="Município"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="telefone">Telefone <span className="text-red-500">*</span></Label>
              <Input
                id="telefone"
                value={dadosEmpresa.telefone}
                onChange={(e) => handleInputChange('telefone', e.target.value)}
                placeholder="Telefone da empresa"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email <span className="text-red-500">*</span></Label>
              <Input
                id="email"
                type="email"
                value={dadosEmpresa.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Email da empresa"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={dadosEmpresa.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="www.exemplo.com"
              />
            </div>
          </CardContent>
        </Card>

        {/* Representante Legal */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Representante Legal
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="representante-legal">Nome Completo <span className="text-red-500">*</span></Label>
              <Input
                id="representante-legal"
                value={dadosEmpresa.representanteLegal}
                onChange={(e) => handleInputChange('representanteLegal', e.target.value)}
                placeholder="Nome do representante legal"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bi-representante">Bilhete de Identidade <span className="text-red-500">*</span></Label>
                <Input
                  id="bi-representante"
                  value={dadosEmpresa.biRepresentante}
                  onChange={(e) => handleInputChange('biRepresentante', e.target.value)}
                  placeholder="Número do BI"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cargo-representante">Cargo na Empresa <span className="text-red-500">*</span></Label>
                <Input
                  id="cargo-representante"
                  value={dadosEmpresa.cargoRepresentante}
                  onChange={(e) => handleInputChange('cargoRepresentante', e.target.value)}
                  placeholder="Ex: Administrador, Gerente"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dados da Conta */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Dados da Conta
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="natureza-conta">Natureza da Conta <span className="text-red-500">*</span></Label>
              <Select value={dadosEmpresa.naturezaConta} onValueChange={(value) => handleInputChange('naturezaConta', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a natureza" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kwanza">Kwanza - AKZ</SelectItem>
                  <SelectItem value="euro">EURO - EUR</SelectItem>
                  <SelectItem value="dolar">US Dolar - USD</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tipo-conta">Tipo de Conta <span className="text-red-500">*</span></Label>
              <Select value={dadosEmpresa.tipoConta} onValueChange={(value) => handleInputChange('tipoConta', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de conta" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="corrente">Conta Corrente Empresarial</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="finalidade-conta">Finalidade da Conta <span className="text-red-500">*</span></Label>
              <Textarea
                id="finalidade-conta"
                value={dadosEmpresa.finalidadeConta}
                onChange={(e) => handleInputChange('finalidadeConta', e.target.value)}
                placeholder="Descreva a finalidade da conta bancária"
                rows={3}
              />
            </div>

            <div className="pt-4">
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || loadingBranches}
                className="w-full bg-primary hover:bg-primary/90 disabled:opacity-50"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin mr-2" />
                    Criando Conta...
                  </>
                ) : (
                  'Criar Conta Empresa'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AbrirContaEmpresa;
