import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { API_CONFIG, API_ENDPOINTS } from '@/config/api';
import { 
  Shield, 
  Users, 
  Server, 
  Search, 
  Filter, 
  Download,
  Calendar,
  Clock,
  User,
  Globe,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw
} from 'lucide-react';

interface AuditLog {
  id: number;
  user_id?: string;
  user_name?: string;
  user_email?: string;
  user_role?: string;
  performed_by?: string;
  performer_email?: string;
  action: string;
  table_name?: string;
  record_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  risk_level?: 'low' | 'medium' | 'high';
}

interface AuditResponse {
  logs: AuditLog[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    per_page: number;
  };
}

interface AuditSummary {
  general_stats: {
    total_logs: number;
    unique_users: number;
    unique_ips: number;
    active_days: number;
  };
  top_actions: Array<{ action: string; count: number }>;
  top_users: Array<{ full_name: string; email: string; log_count: number }>;
  hourly_activity: Array<{ hour: number; count: number }>;
}

const AuditPage: React.FC = () => {
  const { hasRole } = useAuth();
  const { toast } = useToast();

  // Estados principais
  const [activeTab, setActiveTab] = useState('users');
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [summary, setSummary] = useState<AuditSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_records: 0,
    per_page: 20
  });

  // Estados de filtros
  const [filters, setFilters] = useState({
    search: '',
    start_date: '',
    end_date: '',
    action: '',
    user_id: '',
    ip_address: '',
    page: 1,
    limit: 20
  });

  // Verificar permissões
  if (!hasRole('admin', 'gerente')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Acesso Negado
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Você não tem permissão para visualizar os logs de auditoria.
          </p>
        </div>
      </div>
    );
  }

  // Carregar logs
  const loadLogs = async (tabType: string = activeTab) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value && key !== 'page' && key !== 'limit') {
          queryParams.append(key, value.toString());
        }
      });

      queryParams.append('page', filters.page.toString());
      queryParams.append('limit', filters.limit.toString());

      const token = localStorage.getItem('twins-bank-token');
      const url = `${API_CONFIG.baseURL}/audit/${tabType}?${queryParams}`;
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data: { data: AuditResponse } = await response.json();
        setLogs(data.data.logs);
        setPagination(data.data.pagination);
      } else {
        throw new Error('Erro ao carregar logs de auditoria');
      }
    } catch (error) {
      toast({
        title: "Erro ao carregar logs",
        description: error instanceof Error ? error.message : "Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar resumo
  const loadSummary = async () => {
    try {
      const queryParams = new URLSearchParams();
      if (filters.start_date) queryParams.append('start_date', filters.start_date);
      if (filters.end_date) queryParams.append('end_date', filters.end_date);

      const token = localStorage.getItem('twins-bank-token');
      const url = `${API_CONFIG.baseURL}/audit/summary?${queryParams}`;
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data: { data: AuditSummary } = await response.json();
        setSummary(data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar resumo:', error);
    }
  };

  // Efeitos
  useEffect(() => {
    loadLogs();
    if (hasRole('admin')) {
      loadSummary();
    }
  }, [activeTab, filters.page]);

  // Handlers
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setFilters(prev => ({ ...prev, page: 1 }));
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleSearch = () => {
    loadLogs();
  };

  const handleReset = () => {
    setFilters({
      search: '',
      start_date: '',
      end_date: '',
      action: '',
      user_id: '',
      ip_address: '',
      page: 1,
      limit: 20
    });
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  // Função para exportar logs em CSV
  const handleExport = async () => {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value && key !== 'page' && key !== 'limit') {
          queryParams.append(key, value.toString());
        }
      });

      // Exportar todos os logs (sem paginação)
      queryParams.append('limit', '10000');

      const token = localStorage.getItem('twins-bank-token');
      const url = `${API_CONFIG.baseURL}/audit/${activeTab}?${queryParams}`;
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data: { data: AuditResponse } = await response.json();
        exportToCSV(data.data.logs, `audit_logs_${activeTab}_${new Date().toISOString().split('T')[0]}.csv`);

        toast({
          title: "Exportação concluída",
          description: `${data.data.logs.length} registros exportados com sucesso.`,
        });
      } else {
        throw new Error('Erro ao exportar logs');
      }
    } catch (error) {
      toast({
        title: "Erro na exportação",
        description: error instanceof Error ? error.message : "Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Função para converter dados para CSV
  const exportToCSV = (data: AuditLog[], filename: string) => {
    const headers = [
      'ID',
      'Data/Hora',
      'Usuário',
      'Email',
      'Ação',
      'Tabela',
      'Registro ID',
      'IP',
      'User Agent',
      'Valores Antigos',
      'Valores Novos'
    ];

    const csvContent = [
      headers.join(','),
      ...data.map(log => [
        log.id,
        `"${formatDate(log.created_at)}"`,
        `"${log.user_name || log.performed_by || 'Sistema'}"`,
        `"${log.user_email || log.performer_email || ''}"`,
        `"${log.action}"`,
        `"${log.table_name || ''}"`,
        `"${log.record_id || ''}"`,
        `"${log.ip_address || ''}"`,
        `"${log.user_agent || ''}"`,
        `"${log.old_values ? JSON.stringify(log.old_values).replace(/"/g, '""') : ''}"`,
        `"${log.new_values ? JSON.stringify(log.new_values).replace(/"/g, '""') : ''}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-PT', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Função para obter cor do badge de risco
  const getRiskBadgeColor = (riskLevel?: string) => {
    switch (riskLevel) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default: return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    }
  };

  // Função para obter ícone da ação
  const getActionIcon = (action: string) => {
    const actionLower = action.toLowerCase();
    if (actionLower.includes('login')) return <User className="h-4 w-4" />;
    if (actionLower.includes('create')) return <CheckCircle className="h-4 w-4" />;
    if (actionLower.includes('update')) return <RefreshCw className="h-4 w-4" />;
    if (actionLower.includes('delete')) return <XCircle className="h-4 w-4" />;
    if (actionLower.includes('failed') || actionLower.includes('denied')) return <AlertTriangle className="h-4 w-4" />;
    return <Shield className="h-4 w-4" />;
  };

  // Função para obter badge da ação
  const getActionBadge = (action: string) => {
    const actionLower = action.toLowerCase();

    if (actionLower.includes('failed') || actionLower.includes('denied') || actionLower.includes('error')) {
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Erro
        </span>
      );
    }

    if (actionLower.includes('force') || actionLower.includes('admin') || actionLower.includes('override')) {
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          Crítico
        </span>
      );
    }

    if (actionLower.includes('create') || actionLower.includes('insert')) {
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Criação
        </span>
      );
    }

    if (actionLower.includes('update') || actionLower.includes('modify')) {
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          Alteração
        </span>
      );
    }

    if (actionLower.includes('delete') || actionLower.includes('remove')) {
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Exclusão
        </span>
      );
    }

    if (actionLower.includes('login') || actionLower.includes('logout')) {
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          Acesso
        </span>
      );
    }

    return null;
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Auditoria e Logs</h1>
          <p className="text-muted-foreground">
            Visualize e monitore todas as atividades do sistema
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => loadLogs()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Exportar CSV
          </Button>
        </div>
      </div>

      {/* Resumo Estatístico (apenas para admin) */}
      {hasRole('admin') && summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Logs</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.general_stats.total_logs}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Usuários Únicos</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.general_stats.unique_users}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">IPs Únicos</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.general_stats.unique_ips}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Dias Ativos</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.general_stats.active_days}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Pesquisar</Label>
              <Input
                id="search"
                placeholder="Nome, email, ação..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="start_date">Data Início</Label>
              <Input
                id="start_date"
                type="date"
                value={filters.start_date}
                onChange={(e) => handleFilterChange('start_date', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_date">Data Fim</Label>
              <Input
                id="end_date"
                type="date"
                value={filters.end_date}
                onChange={(e) => handleFilterChange('end_date', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="action">Ação</Label>
              <Input
                id="action"
                placeholder="LOGIN, CREATE..."
                value={filters.action}
                onChange={(e) => handleFilterChange('action', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="ip_address">IP</Label>
              <Input
                id="ip_address"
                placeholder="***********"
                value={filters.ip_address}
                onChange={(e) => handleFilterChange('ip_address', e.target.value)}
              />
            </div>
            <div className="space-y-2 flex items-end">
              <div className="flex gap-2 w-full">
                <Button onClick={handleSearch} className="flex-1">
                  <Search className="h-4 w-4 mr-2" />
                  Buscar
                </Button>
                <Button variant="outline" onClick={handleReset}>
                  Limpar
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs de Logs */}
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Usuários
          </TabsTrigger>
          {hasRole('admin') && (
            <>
              <TabsTrigger value="system" className="flex items-center gap-2">
                <Server className="h-4 w-4" />
                Sistema
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Segurança
              </TabsTrigger>
            </>
          )}
        </TabsList>

        {/* Conteúdo das Tabs */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Logs de Usuários</CardTitle>
              <CardDescription>
                Atividades relacionadas a usuários, logins e perfis
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  Carregando logs...
                </div>
              ) : logs.length > 0 ? (
                <div className="space-y-4">
                  {logs.map((log) => (
                    <div key={log.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getActionIcon(log.action)}
                          <span className="font-medium">{log.action}</span>
                          {log.risk_level && (
                            <Badge className={getRiskBadgeColor(log.risk_level)}>
                              {log.risk_level.toUpperCase()}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Clock className="h-4 w-4" />
                          {formatDate(log.created_at)}
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Usuário: </span>
                          {log.user_name || log.performed_by || 'Sistema'}
                          {log.user_email && (
                            <span className="text-muted-foreground"> ({log.user_email})</span>
                          )}
                        </div>
                        {log.ip_address && (
                          <div>
                            <span className="font-medium">IP: </span>
                            {log.ip_address}
                          </div>
                        )}
                        {log.table_name && (
                          <div>
                            <span className="font-medium">Tabela: </span>
                            {log.table_name}
                          </div>
                        )}
                        {log.record_id && (
                          <div>
                            <span className="font-medium">Registro: </span>
                            {log.record_id}
                          </div>
                        )}
                      </div>

                      {(log.old_values || log.new_values) && (
                        <div className="mt-2 p-2 bg-muted rounded text-xs">
                          {log.old_values && (
                            <div>
                              <span className="font-medium">Valores Antigos: </span>
                              <code>{JSON.stringify(log.old_values, null, 2)}</code>
                            </div>
                          )}
                          {log.new_values && (
                            <div>
                              <span className="font-medium">Valores Novos: </span>
                              <code>{JSON.stringify(log.new_values, null, 2)}</code>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Paginação */}
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      Mostrando {((pagination.current_page - 1) * pagination.per_page) + 1} a{' '}
                      {Math.min(pagination.current_page * pagination.per_page, pagination.total_records)} de{' '}
                      {pagination.total_records} registros
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.current_page - 1)}
                        disabled={pagination.current_page <= 1}
                      >
                        Anterior
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.current_page + 1)}
                        disabled={pagination.current_page >= pagination.total_pages}
                      >
                        Próximo
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-muted-foreground">Nenhum log encontrado</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {hasRole('admin') && (
          <>
            <TabsContent value="system" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Logs do Sistema</CardTitle>
                  <CardDescription>
                    Atividades do sistema, backups e operações internas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Carregando logs...
                    </div>
                  ) : logs.length > 0 ? (
                    <div className="space-y-4">
                      {logs.map((log) => (
                        <div key={log.id} className="border rounded-lg p-4 space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {getActionIcon(log.action)}
                              <span className="font-medium">{log.action}</span>
                              {getActionBadge(log.action)}
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Clock className="h-4 w-4" />
                              {formatDate(log.created_at)}
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="font-medium">Executado por: </span>
                              <span className="text-muted-foreground">
                                {log.performed_by || 'Sistema'}
                              </span>
                              {log.performer_email && (
                                <span className="text-muted-foreground"> ({log.performer_email})</span>
                              )}
                            </div>
                            {log.ip_address && (
                              <div>
                                <span className="font-medium">IP: </span>
                                <span className="text-muted-foreground">{log.ip_address}</span>
                              </div>
                            )}
                            {log.table_name && (
                              <div>
                                <span className="font-medium">Tabela: </span>
                                <span className="text-muted-foreground">{log.table_name}</span>
                              </div>
                            )}
                            {log.record_id && (
                              <div>
                                <span className="font-medium">Registro: </span>
                                <span className="text-muted-foreground">{log.record_id}</span>
                              </div>
                            )}
                          </div>
                          {(log.old_values || log.new_values) && (
                            <div className="space-y-2">
                              {log.old_values && (
                                <div>
                                  <span className="font-medium text-sm">Valores Antigos:</span>
                                  <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                                    {JSON.stringify(log.old_values, null, 2)}
                                  </pre>
                                </div>
                              )}
                              {log.new_values && (
                                <div>
                                  <span className="font-medium text-sm">Valores Novos:</span>
                                  <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                                    {JSON.stringify(log.new_values, null, 2)}
                                  </pre>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                      <div className="flex items-center justify-between pt-4">
                        <div className="text-sm text-muted-foreground">
                          Mostrando {((filters.page - 1) * filters.limit) + 1} a {Math.min(filters.page * filters.limit, pagination?.total_records || 0)} de {pagination?.total_records || 0} registros
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                            disabled={filters.page <= 1}
                          >
                            Anterior
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                            disabled={!pagination || filters.page >= pagination.total_pages}
                          >
                            Próximo
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Server className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-muted-foreground">Nenhum log encontrado</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Logs de Segurança</CardTitle>
                  <CardDescription>
                    Tentativas de login, acessos negados e eventos de segurança
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Carregando logs...
                    </div>
                  ) : logs.length > 0 ? (
                    <div className="space-y-4">
                      {logs.map((log) => (
                        <div key={log.id} className="border rounded-lg p-4 space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {getActionIcon(log.action)}
                              <span className="font-medium">{log.action}</span>
                              {log.risk_level && (
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  log.risk_level === 'high' ? 'bg-red-100 text-red-800' :
                                  log.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-green-100 text-green-800'
                                }`}>
                                  {log.risk_level === 'high' ? 'Alto Risco' :
                                   log.risk_level === 'medium' ? 'Médio Risco' : 'Baixo Risco'}
                                </span>
                              )}
                              {getActionBadge(log.action)}
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Clock className="h-4 w-4" />
                              {formatDate(log.created_at)}
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="font-medium">Usuário: </span>
                              <span className="text-muted-foreground">
                                {log.user_name || 'Sistema'}
                              </span>
                              {log.user_email && (
                                <span className="text-muted-foreground"> ({log.user_email})</span>
                              )}
                            </div>
                            {log.ip_address && (
                              <div>
                                <span className="font-medium">IP: </span>
                                <span className="text-muted-foreground">{log.ip_address}</span>
                              </div>
                            )}
                            {log.user_role && (
                              <div>
                                <span className="font-medium">Perfil: </span>
                                <span className="text-muted-foreground">{log.user_role}</span>
                              </div>
                            )}
                            {log.record_id && (
                              <div>
                                <span className="font-medium">Registro: </span>
                                <span className="text-muted-foreground">{log.record_id}</span>
                              </div>
                            )}
                          </div>
                          {(log.old_values || log.new_values) && (
                            <div className="space-y-2">
                              {log.old_values && (
                                <div>
                                  <span className="font-medium text-sm">Valores Antigos:</span>
                                  <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                                    {JSON.stringify(log.old_values, null, 2)}
                                  </pre>
                                </div>
                              )}
                              {log.new_values && (
                                <div>
                                  <span className="font-medium text-sm">Valores Novos:</span>
                                  <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                                    {JSON.stringify(log.new_values, null, 2)}
                                  </pre>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                      <div className="flex items-center justify-between pt-4">
                        <div className="text-sm text-muted-foreground">
                          Mostrando {((filters.page - 1) * filters.limit) + 1} a {Math.min(filters.page * filters.limit, pagination?.total_records || 0)} de {pagination?.total_records || 0} registros
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                            disabled={filters.page <= 1}
                          >
                            Anterior
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                            disabled={!pagination || filters.page >= pagination.total_pages}
                          >
                            Próximo
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-muted-foreground">Nenhum log encontrado</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  );
};

export default AuditPage;
