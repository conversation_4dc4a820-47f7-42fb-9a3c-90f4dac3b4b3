import { makeRequest } from '@/utils/api';

// Interfaces para operações do cofre
export interface Vault {
  id: number;
  vault_code: string;
  name: string;
  description?: string;
  current_balance: number;
  max_capacity: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  branch_name: string;
  branch_code: string;
  branch_id: number;
}

export interface VaultMovement {
  id: string;
  vault_id: number;
  movement_type: 'initial_balance' | 'deposit' | 'withdrawal' | 'transfer_in' | 'transfer_out' | 'adjustment';
  amount: number;
  previous_balance: number;
  new_balance: number;
  denominations?: any;
  reference_number?: string;
  description?: string;
  notes?: string;
  source_type?: 'cash_register' | 'treasury' | 'atm' | 'manual' | 'system';
  source_id?: string;
  destination_type?: 'cash_register' | 'treasury' | 'atm' | 'manual' | 'system';
  destination_id?: string;
  processed_by: string;
  processed_by_name?: string;
  authorized_by?: string;
  authorized_by_name?: string;
  processed_at: string;
  created_at: string;
}

export interface VaultStatistics {
  total_movements: number;
  total_deposits: number;
  total_withdrawals: number;
  average_movement: number;
  largest_movement: number;
  smallest_movement: number;
}

export interface VaultMovementsResponse {
  movements: VaultMovement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface VaultStatisticsResponse {
  vault: {
    id: number;
    name: string;
    current_balance: number;
    branch_name: string;
  };
  statistics: VaultStatistics;
  period: string;
}

class VaultService {
  // Obter todos os cofres
  async getAllVaults(filters: {
    branch_id?: number;
    is_active?: boolean;
  } = {}): Promise<{ vaults: Vault[]; total: number }> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await makeRequest<{ vaults: Vault[]; total: number }>(
      `/system/vaults?${queryParams.toString()}`
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao obter cofres');
  }

  // Obter cofre por ID
  async getVaultById(vaultId: number): Promise<Vault> {
    const response = await makeRequest<{ vault: Vault }>(`/system/vaults/${vaultId}`);

    if (response.data) {
      return response.data.vault;
    }
    throw new Error(response.message || 'Erro ao obter cofre');
  }

  // Obter movimentos do cofre
  async getVaultMovements(vaultId: number, filters: {
    page?: number;
    limit?: number;
    movement_type?: string;
    date_from?: string;
    date_to?: string;
  } = {}): Promise<VaultMovementsResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await makeRequest<VaultMovementsResponse>(
      `/system/vaults/${vaultId}/movements?${queryParams.toString()}`
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao obter movimentos do cofre');
  }

  // Obter estatísticas do cofre
  async getVaultStatistics(vaultId: number, period: 'today' | 'week' | 'month' | 'year' = 'month'): Promise<VaultStatisticsResponse> {
    const response = await makeRequest<VaultStatisticsResponse>(
      `/system/vaults/${vaultId}/statistics?period=${period}`
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao obter estatísticas do cofre');
  }

  // Formatar tipo de movimento para exibição
  formatMovementType(type: string): string {
    const types: Record<string, string> = {
      'initial_balance': 'Saldo Inicial',
      'deposit': 'Crédito',
      'withdrawal': 'Débito',
      'transfer_in': 'Transferência Entrada',
      'transfer_out': 'Transferência Saída',
      'adjustment': 'Ajuste'
    };
    return types[type] || type;
  }

  // Obter cor baseada no tipo de movimento
  getMovementColor(type: string): string {
    switch (type) {
      case 'deposit':
        return 'text-green-600';
      case 'withdrawal':
        return 'text-red-600';
      case 'initial_balance':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  }

  // Obter cores completas para estilização
  getMovementColors(type: string): { textColor: string; bgColor: string; iconColor: string } {
    switch (type) {
      case 'deposit':
        return {
          textColor: 'text-green-700',
          bgColor: 'bg-green-50',
          iconColor: 'text-green-600'
        };
      case 'withdrawal':
        return {
          textColor: 'text-red-700',
          bgColor: 'bg-red-50',
          iconColor: 'text-red-600'
        };
      case 'initial_balance':
        return {
          textColor: 'text-blue-700',
          bgColor: 'bg-blue-50',
          iconColor: 'text-blue-600'
        };
      default:
        return {
          textColor: 'text-gray-700',
          bgColor: 'bg-gray-50',
          iconColor: 'text-gray-600'
        };
    }
  }

  // Formatar fonte/destino para exibição
  formatSourceType(type: string): string {
    const sources: Record<string, string> = {
      'cash_register': 'Caixa',
      'treasury': 'Tesouraria',
      'atm': 'ATM',
      'manual': 'Manual',
      'system': 'Sistema'
    };
    return sources[type] || type;
  }

  // Formatar valor monetário
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  }

  // Formatar data
  formatDate(dateString: string): string {
    return new Intl.DateTimeFormat('pt-AO', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  }

  // Obter cor do movimento baseado no tipo
  getMovementColor(type: string): string {
    const colors: Record<string, string> = {
      'initial_balance': 'text-blue-600',
      'deposit': 'text-green-600',
      'withdrawal': 'text-red-600',
      'transfer_in': 'text-green-500',
      'transfer_out': 'text-red-500',
      'adjustment': 'text-yellow-600'
    };
    return colors[type] || 'text-gray-600';
  }

  // Obter ícone do movimento baseado no tipo
  getMovementIcon(type: string): string {
    const icons: Record<string, string> = {
      'initial_balance': '⚡',
      'deposit': '⬆️',
      'withdrawal': '⬇️',
      'transfer_in': '📥',
      'transfer_out': '📤',
      'adjustment': '⚙️'
    };
    return icons[type] || '💰';
  }
}

export const vaultService = new VaultService();
export default vaultService;
