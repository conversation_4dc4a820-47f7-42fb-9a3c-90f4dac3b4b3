import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { UserCheck, Calculator, CheckCircle, AlertCircle, RefreshCw, Loader2, Shield } from 'lucide-react';
import {
  treasuryService,
  CashDenominations,
  DeliverToTreasurerRequest
} from '@/services/treasuryService';
import { userService, User } from '@/services/userService';
import { systemService } from '@/services/systemService';
import { OperationSummary } from '@/components/common/OperationSummary';

const EntregaTesoureiro = () => {
  const [formData, setFormData] = useState({
    treasurer_id: '',
    amount: '',
    notes: ''
  });

  const [denominations, setDenominations] = useState<CashDenominations>(
    treasuryService.getEmptyDenominations()
  );

  const [availableTreasurers, setAvailableTreasurers] = useState<User[]>([]);
  const [isLoadingTreasurers, setIsLoadingTreasurers] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Estado para saldo do cofre (Admin/Gerente entregam do cofre)
  const [vaultBalance, setVaultBalance] = useState<number>(0);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);

  const { toast } = useToast();

  // Carregar dados iniciais ao montar componente
  useEffect(() => {
    loadAvailableTreasurers();
    loadVaultBalance();
  }, []);

  const loadAvailableTreasurers = async () => {
    try {
      setIsLoadingTreasurers(true);
      const treasurers = await userService.getTreasurers();
      setAvailableTreasurers(treasurers);
    } catch (error) {
      console.error('Erro ao carregar tesoureiros:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar a lista de tesoureiros",
        variant: "destructive"
      });
    } finally {
      setIsLoadingTreasurers(false);
    }
  };

  const loadVaultBalance = async () => {
    try {
      setIsLoadingBalance(true);
      const balanceData = await systemService.getVaultBalance();
      setVaultBalance(balanceData.balance || 0);
    } catch (error: any) {
      console.error('Erro ao carregar saldo do cofre:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar saldo do cofre",
        variant: "destructive"
      });
      setVaultBalance(0); // Valor padrão em caso de erro
    } finally {
      setIsLoadingBalance(false);
    }
  };

  // Calcular total das denominações
  const calculateDenominationsTotal = () => {
    return treasuryService.calculateDenominationsTotal(denominations);
  };

  // Validar formulário
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.treasurer_id) {
      newErrors.treasurer_id = 'Selecione um tesoureiro';
    }

    const denominationsTotal = calculateDenominationsTotal();

    if (denominationsTotal <= 0) {
      newErrors.denominations = 'Informe pelo menos uma denominação';
    }

    // Validar saldo suficiente no cofre
    if (denominationsTotal > vaultBalance) {
      newErrors.denominations = `Saldo insuficiente no cofre. Disponível: ${treasuryService.formatCurrency(vaultBalance)}`;
    }

    if (formData.notes && formData.notes.length > 500) {
      newErrors.notes = 'Observações não podem exceder 500 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submeter formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const totalAmount = calculateDenominationsTotal();

      const deliveryData: DeliverToTreasurerRequest = {
        treasurer_id: formData.treasurer_id,
        amount: totalAmount,
        denominations,
        notes: formData.notes || undefined
      };

      await treasuryService.deliverToTreasurer(deliveryData);

      toast({
        title: "Sucesso",
        description: `Entrega de ${treasuryService.formatCurrency(totalAmount)} ao tesoureiro realizada com sucesso`
      });

      // Limpar formulário e recarregar saldo
      setFormData({ treasurer_id: '', amount: '', notes: '' });
      setDenominations(treasuryService.getEmptyDenominations());
      setErrors({});
      await loadVaultBalance(); // Recarregar saldo do cofre após entrega

    } catch (error: any) {
      console.error('Erro ao processar entrega:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao processar entrega ao tesoureiro",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedTreasurer = availableTreasurers.find(t => t.id === formData.treasurer_id);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Entrega ao Tesoureiro</h1>
          <p className="text-muted-foreground">
            Transferir valores do cofre principal para tesoureiro
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Informações da Entrega */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Informações da Entrega
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-orange-50 border border-orange-200 p-4 rounded-lg dark:bg-orange-900/20 dark:border-orange-800">
                <div className="flex items-center gap-2 text-orange-700 dark:text-orange-300 mb-2">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Operação Restrita</span>
                </div>
                <p className="text-sm text-orange-600 dark:text-orange-400">
                  Esta operação transfere valores do cofre principal para o tesoureiro selecionado.
                  Apenas administradores e gerentes podem realizar esta operação.
                </p>
              </div>

              {/* Tesoureiro de Destino */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="treasurer_id">
                    Tesoureiro de Destino <span className="text-red-500">*</span>
                  </Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={loadAvailableTreasurers}
                    disabled={isLoadingTreasurers}
                    className="h-6 px-2 text-xs"
                  >
                    <RefreshCw className={`h-3 w-3 mr-1 ${isLoadingTreasurers ? 'animate-spin' : ''}`} />
                    Atualizar
                  </Button>
                </div>
                <Select
                  value={formData.treasurer_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, treasurer_id: value }))}
                >
                  <SelectTrigger className={errors.treasurer_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder={isLoadingTreasurers ? "Carregando..." : "Selecione um tesoureiro"} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTreasurers.map((treasurer) => (
                      <SelectItem key={treasurer.id} value={treasurer.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{treasurer.full_name}</span>
                          <span className="text-xs text-muted-foreground">{treasurer.email}</span>
                          {treasurer.branch_name && (
                            <span className="text-xs text-muted-foreground">Agência: {treasurer.branch_name}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.treasurer_id && (
                  <p className="text-sm text-red-500">{errors.treasurer_id}</p>
                )}
              </div>

              {/* Valor Total (Auto-calculado) */}
              <div className="space-y-2">
                <Label htmlFor="amount">
                  Valor Total (AOA) <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="amount"
                    type="text"
                    readOnly
                    value={treasuryService.formatCurrency(calculateDenominationsTotal())}
                    className="bg-gray-50 dark:bg-gray-800 font-semibold text-lg cursor-not-allowed"
                  />
                  <Calculator className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
                <p className="text-xs text-muted-foreground">
                  Valor calculado automaticamente a partir das denominações
                </p>
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="notes">Observações</Label>
                <Textarea
                  id="notes"
                  placeholder="Observações sobre a entrega (opcional)..."
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  className={errors.notes ? 'border-red-500' : ''}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  {formData.notes.length}/500 caracteres
                </p>
                {errors.notes && (
                  <p className="text-sm text-red-500">{errors.notes}</p>
                )}
              </div>

              {/* Informações do Tesoureiro Selecionado */}
              {selectedTreasurer && (
                <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Tesoureiro Selecionado
                  </h4>
                  <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                    <p><strong>Nome:</strong> {selectedTreasurer.full_name}</p>
                    <p><strong>Email:</strong> {selectedTreasurer.email}</p>
                    {selectedTreasurer.branch_name && (
                      <p><strong>Agência:</strong> {selectedTreasurer.branch_name}</p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Calculadora de Denominações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Denominações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="notes_10000">Notas de 10.000 AOA</Label>
                  <Input
                    id="notes_10000"
                    type="number"
                    min="0"
                    placeholder="0"
                    value={denominations.notes_10000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_10000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_5000">Notas de 5.000 AOA</Label>
                  <Input
                    id="notes_5000"
                    type="number"
                    min="0"
                    placeholder="0"
                    value={denominations.notes_5000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_5000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_2000">Notas de 2.000 AOA</Label>
                  <Input
                    id="notes_2000"
                    type="number"
                    min="0"
                    placeholder="0"
                    value={denominations.notes_2000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_2000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_1000">Notas de 1.000 AOA</Label>
                  <Input
                    id="notes_1000"
                    type="number"
                    min="0"
                    placeholder="0"
                    value={denominations.notes_1000}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_1000: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_500">Notas de 500 AOA</Label>
                  <Input
                    id="notes_500"
                    type="number"
                    min="0"
                    placeholder="0"
                    value={denominations.notes_500}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_500: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes_200">Notas de 200 AOA</Label>
                  <Input
                    id="notes_200"
                    type="number"
                    min="0"
                    placeholder="0"
                    value={denominations.notes_200}
                    onChange={(e) => setDenominations(prev => ({ ...prev, notes_200: parseInt(e.target.value) || 0 }))}
                  />
                </div>
              </div>

              <div className="bg-gray-50 border border-gray-200 p-3 rounded-lg dark:bg-gray-800 dark:border-gray-700">
                <div className="text-gray-700 dark:text-gray-300 text-sm">
                  <strong>Total Calculado:</strong> {treasuryService.formatCurrency(calculateDenominationsTotal())}
                </div>
              </div>

              {errors.denominations && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-red-600">
                    {errors.denominations}
                  </AlertDescription>
                </Alert>
              )}

              <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg dark:bg-yellow-900/20 dark:border-yellow-800">
                <div className="text-yellow-700 dark:text-yellow-300 text-sm">
                  <strong>Dica:</strong> O total das denominações deve corresponder exatamente ao valor da entrega.
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Resumo da Operação */}
          <OperationSummary
            currentBalance={vaultBalance}
            deliveryAmount={calculateDenominationsTotal()}
            title="Resumo da Operação"
            formatCurrency={treasuryService.formatCurrency}
            showValidation={true}
            className="lg:col-span-1"
          />
        </div>

        {/* Botões de Ação */}
        <div className="flex justify-end gap-4 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setFormData({ treasurer_id: '', amount: '', notes: '' });
              setDenominations(treasuryService.getEmptyDenominations());
              setErrors({});
            }}
            disabled={isSubmitting}
          >
            Limpar
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !formData.treasurer_id || calculateDenominationsTotal() <= 0}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processando...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Confirmar Entrega
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EntregaTesoureiro;
