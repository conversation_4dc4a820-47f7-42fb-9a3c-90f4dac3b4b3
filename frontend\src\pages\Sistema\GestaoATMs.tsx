import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Edit, 
  Trash2, 
  RefreshCw, 
  Banknote,
  MapPin,
  Building2,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { useConfirm } from '@/contexts/ConfirmDialogContext';
import { atmService, ATM } from '@/services/atmService';
import { branchService, Branch } from '@/services/branchService';

const GestaoATMs = () => {
  const confirmDialog = useConfirm();
  const [atms, setAtms] = useState<ATM[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedATM, setSelectedATM] = useState<ATM | null>(null);

  const [formData, setFormData] = useState({
    atm_code: '',
    location: '',
    branch_id: '',
    cash_capacity: '',
    status: 'online' as 'online' | 'offline' | 'maintenance'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [atmsResponse, branchesResponse] = await Promise.all([
        atmService.getATMs(),
        branchService.getActiveBranches()
      ]);
      setAtms(atmsResponse?.atms || []);
      setBranches(branchesResponse || []); // Correção: getActiveBranches() retorna diretamente um array
    } catch (error: any) {
      console.error('Erro ao carregar dados:', error);
      toast.error('Erro ao carregar dados');
      // Garantir que os arrays estão inicializados mesmo em caso de erro
      setAtms([]);
      setBranches([]);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.atm_code.trim()) {
      newErrors.atm_code = 'Código do ATM é obrigatório';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Localização é obrigatória';
    }

    if (!formData.branch_id) {
      newErrors.branch_id = 'Agência é obrigatória';
    }

    if (!formData.cash_capacity || parseFloat(formData.cash_capacity) <= 0) {
      newErrors.cash_capacity = 'Capacidade deve ser maior que zero';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const atmData = {
        atm_code: formData.atm_code,
        location: formData.location,
        branch_id: parseInt(formData.branch_id),
        cash_capacity: parseFloat(formData.cash_capacity),
        status: formData.status
      };

      if (isEditing && selectedATM) {
        await atmService.updateATM(selectedATM.id, atmData);
        toast.success('ATM atualizado com sucesso');
      } else {
        await atmService.createATM(atmData);
        toast.success('ATM criado com sucesso');
      }

      setIsModalOpen(false);
      resetForm();
      loadData();
    } catch (error: any) {
      console.error('Erro ao salvar ATM:', error);
      toast.error(error.message || 'Erro ao salvar ATM');
    }
  };

  const handleEdit = (atm: ATM) => {
    setSelectedATM(atm);
    setIsEditing(true);
    setFormData({
      atm_code: atm.atm_code,
      location: atm.location,
      branch_id: atm.branch_id?.toString() || '',
      cash_capacity: atm.cash_capacity.toString(),
      status: atm.status
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (atm: ATM) => {
    const confirmed = await confirmDialog.confirm({
      title: 'Eliminar ATM',
      description: `Tem certeza que deseja eliminar o ATM ${atm.atm_code}? Esta ação não pode ser desfeita.`,
      confirmText: 'Eliminar',
      cancelText: 'Cancelar',
      variant: 'destructive'
    });

    if (!confirmed) {
      return;
    }

    try {
      await atmService.deleteATM(atm.id);
      toast.success('ATM eliminado com sucesso');
      loadData();
    } catch (error: any) {
      console.error('Erro ao eliminar ATM:', error);
      toast.error(error.message || 'Erro ao eliminar ATM');
    }
  };

  const resetForm = () => {
    setFormData({
      atm_code: '',
      location: '',
      branch_id: '',
      cash_capacity: '',
      status: 'online'
    });
    setErrors({});
    setIsEditing(false);
    setSelectedATM(null);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      online: { label: 'Online', className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
      offline: { label: 'Offline', className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
      maintenance: { label: 'Manutenção', className: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' },
      low_balance: { label: 'Saldo Baixo', className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.offline;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestão de ATMs
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Registar e gerir caixas automáticos do sistema
          </p>
        </div>
        <Dialog open={isModalOpen} onOpenChange={(open) => {
          setIsModalOpen(open);
          if (!open) resetForm();
        }}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Novo ATM
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {isEditing ? 'Editar ATM' : 'Novo ATM'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="atm_code">Código do ATM *</Label>
                  <Input
                    id="atm_code"
                    value={formData.atm_code}
                    onChange={(e) => setFormData(prev => ({ ...prev, atm_code: e.target.value }))}
                    placeholder="ATM001"
                    className={errors.atm_code ? 'border-red-500' : ''}
                  />
                  {errors.atm_code && (
                    <p className="text-sm text-red-500">{errors.atm_code}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="branch_id">Agência *</Label>
                  <Select
                    value={formData.branch_id}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, branch_id: value }))}
                  >
                    <SelectTrigger className={errors.branch_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Selecione a agência" />
                    </SelectTrigger>
                    <SelectContent>
                      {branches && branches.length > 0 ? (
                        branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id.toString()}>
                            {branch.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-branch" disabled>Nenhuma agência disponível</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.branch_id && (
                    <p className="text-sm text-red-500">{errors.branch_id}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Localização *</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="Ex: Shopping Belas, Piso 1"
                  className={errors.location ? 'border-red-500' : ''}
                />
                {errors.location && (
                  <p className="text-sm text-red-500">{errors.location}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cash_capacity">Capacidade Máxima (AOA) *</Label>
                  <Input
                    id="cash_capacity"
                    type="number"
                    step="0.01"
                    value={formData.cash_capacity}
                    onChange={(e) => setFormData(prev => ({ ...prev, cash_capacity: e.target.value }))}
                    placeholder="0,00"
                    className={errors.cash_capacity ? 'border-red-500' : ''}
                  />
                  {errors.cash_capacity && (
                    <p className="text-sm text-red-500">{errors.cash_capacity}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Estado Inicial</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: any) => setFormData(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="online">Online</SelectItem>
                      <SelectItem value="offline">Offline</SelectItem>
                      <SelectItem value="maintenance">Em Manutenção</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsModalOpen(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit">
                  {isEditing ? 'Atualizar' : 'Criar'} ATM
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Lista de ATMs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Banknote className="h-5 w-5" />
            ATMs Registados ({atms.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {atms.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Nenhum ATM registado
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Código</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Localização</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Agência</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Capacidade</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Saldo Atual</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Estado</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Ações</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {atms && atms.length > 0 ? atms.map((atm) => (
                    <tr key={atm.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <Banknote className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">{atm.atm_code}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{atm.location}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{atm.branch_name || '-'}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm">
                        {atmService.formatCurrency(atm.cash_capacity)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        {atmService.formatCurrency(atm.current_balance)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        {getStatusBadge(atm.status)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(atm)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(atm)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan={7} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                        <div className="flex flex-col items-center gap-2">
                          <AlertCircle className="h-8 w-8 text-gray-400" />
                          <p>Nenhum ATM cadastrado</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GestaoATMs;

