const { executeQuery, executeTransaction } = require('../config/database');

/**
 * Executa uma query usando uma conexão específica (para transações)
 */
const executeQueryWithConnection = async (query, params = [], connection) => {
  const [result] = await connection.execute(query, params);
  return result;
};
const { AppError } = require('../core/errorHandler');
const { v4: uuidv4 } = require('uuid');
const logger = require('../core/logger');

/**
 * Serviço para operações de transferência
 */
class TransferService {
  
  /**
   * Verificar informações de uma conta
   */
  async verifyAccount(accountNumber) {
    try {
      const account = await executeQuery(`
        SELECT 
          a.id,
          a.account_number,
          a.balance,
          a.available_balance,
          a.status,
          a.account_type,
          c.full_name as client_name,
          curr.code as currency_code,
          curr.symbol as currency_symbol
        FROM accounts a
        JOIN account_holders ah ON a.id = ah.account_id
        JOIN clients c ON ah.client_id = c.id
        JOIN currencies curr ON a.currency_id = curr.id
        WHERE a.account_number = ? AND a.status = 'active'
        LIMIT 1
      `, [accountNumber]);

      if (!account || account.length === 0) {
        throw new AppError('Conta não encontrada ou inativa', 404, 'ACCOUNT_NOT_FOUND');
      }

      return {
        accountNumber: account[0].account_number,
        clientName: account[0].client_name,
        accountType: account[0].account_type,
        balance: parseFloat(account[0].balance),
        availableBalance: parseFloat(account[0].available_balance),
        currency: {
          code: account[0].currency_code,
          symbol: account[0].currency_symbol
        },
        status: account[0].status
      };
    } catch (error) {
      logger.error('Erro ao verificar conta:', error);
      throw error;
    }
  }

  /**
   * Criar transferência interna
   */
  async createInternalTransfer(transferData, userId) {
    const { sourceAccount, destinationAccount, amount, description, natureza } = transferData;

    try {
      return await executeTransaction(async (connection) => {
        // 1. Verificar conta origem
        const sourceAccountInfo = await this.verifyAccount(sourceAccount);
        
        // 2. Verificar conta destino
        const destinationAccountInfo = await this.verifyAccount(destinationAccount);

        // 3. Validar saldo suficiente
        if (sourceAccountInfo.availableBalance < amount) {
          throw new AppError('Saldo insuficiente na conta origem', 400, 'INSUFFICIENT_BALANCE');
        }

        // 4. Validar limite de transferência (10.000.000 Kz)
        if (amount > ********) {
          throw new AppError('Valor excede o limite máximo de transferência', 400, 'AMOUNT_LIMIT_EXCEEDED');
        }

        // 5. Gerar IDs únicos
        const transactionId = uuidv4();
        const transferId = uuidv4();
        const transactionCode = `TI${Date.now()}`;

        // 6. Criar transação principal
        await executeQueryWithConnection(`
          INSERT INTO transactions (
            id, transaction_code, transaction_type, amount, currency_id,
            source_account_id, destination_account_id, description,
            status, processed_by, processed_at
          ) VALUES (?, ?, 'transfer', ?, 1,
            (SELECT id FROM accounts WHERE account_number = ?),
            (SELECT id FROM accounts WHERE account_number = ?),
            ?, 'completed', ?, NOW())
        `, [
          transactionId, transactionCode, amount,
          sourceAccount, destinationAccount, description || 'Transferência Interna',
          userId
        ], connection);

        // 7. Criar registro de transferência
        await executeQueryWithConnection(`
          INSERT INTO transfers (
            id, transaction_id, transfer_type, source_account_number,
            destination_account_number, beneficiary_name, purpose_code
          ) VALUES (?, ?, 'internal', ?, ?, ?, ?)
        `, [
          transferId, transactionId, sourceAccount, destinationAccount,
          destinationAccountInfo.clientName, natureza
        ], connection);

        // 8. Atualizar saldo da conta origem (débito)
        await executeQueryWithConnection(`
          UPDATE accounts
          SET balance = balance - ?, available_balance = available_balance - ?
          WHERE account_number = ?
        `, [amount, amount, sourceAccount], connection);

        // 9. Atualizar saldo da conta destino (crédito)
        await executeQueryWithConnection(`
          UPDATE accounts
          SET balance = balance + ?, available_balance = available_balance + ?
          WHERE account_number = ?
        `, [amount, amount, destinationAccount], connection);

        // 10. Log de auditoria
        await executeQueryWithConnection(`
          INSERT INTO audit_logs (
            user_id, action, table_name, record_id,
            old_values, new_values, ip_address
          ) VALUES (?, 'CREATE', 'transfers', ?,
            NULL, ?, '127.0.0.1')
        `, [
          userId, transferId,
          JSON.stringify({
            type: 'internal_transfer',
            amount,
            sourceAccount,
            destinationAccount,
            description
          })
        ], connection);

        return {
          id: transferId,
          transactionId,
          transactionCode,
          type: 'internal',
          sourceAccount,
          destinationAccount,
          beneficiaryName: destinationAccountInfo.clientName,
          amount: parseFloat(amount),
          currency: sourceAccountInfo.currency,
          description: description || 'Transferência Interna',
          status: 'completed',
          createdAt: new Date().toISOString(),
          processedBy: userId
        };
      });
    } catch (error) {
      logger.error('Erro ao criar transferência interna:', error);
      throw error;
    }
  }

  /**
   * Listar transferências com filtros
   */
  async getTransfers(filters = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        searchTerm,
        status,
        natureza,
        dateFrom,
        dateTo
      } = filters;

      const offset = (page - 1) * limit;
      let whereConditions = [];
      let queryParams = [];

      // Construir condições WHERE
      if (searchTerm) {
        whereConditions.push(`(
          tr.source_account_number LIKE ? OR 
          tr.destination_account_number LIKE ? OR 
          tr.beneficiary_name LIKE ? OR
          t.transaction_code LIKE ?
        )`);
        const searchPattern = `%${searchTerm}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
      }

      if (status) {
        whereConditions.push('t.status = ?');
        queryParams.push(status);
      }

      if (natureza) {
        whereConditions.push('tr.purpose_code = ?');
        queryParams.push(natureza);
      }

      if (dateFrom) {
        whereConditions.push('DATE(t.created_at) >= ?');
        queryParams.push(dateFrom);
      }

      if (dateTo) {
        whereConditions.push('DATE(t.created_at) <= ?');
        queryParams.push(dateTo);
      }

      const whereClause = whereConditions.length > 0 
        ? `WHERE ${whereConditions.join(' AND ')}` 
        : '';

      // Query principal
      const transfersQuery = `
        SELECT 
          tr.id,
          t.transaction_code,
          tr.transfer_type,
          tr.source_account_number,
          tr.destination_account_number,
          tr.beneficiary_name,
          t.amount,
          t.status,
          t.description,
          tr.purpose_code as natureza,
          t.created_at,
          t.processed_at,
          u.full_name as processed_by_name,
          curr.code as currency_code,
          curr.symbol as currency_symbol
        FROM transfers tr
        JOIN transactions t ON tr.transaction_id = t.id
        JOIN users u ON t.processed_by = u.id
        JOIN currencies curr ON t.currency_id = curr.id
        ${whereClause}
        ORDER BY t.created_at DESC
        LIMIT ? OFFSET ?
      `;

      // Query para contar total
      const countQuery = `
        SELECT COUNT(*) as total
        FROM transfers tr
        JOIN transactions t ON tr.transaction_id = t.id
        ${whereClause}
      `;

      const [transfers, countResult] = await Promise.all([
        executeQuery(transfersQuery, [...queryParams, limit, offset]),
        executeQuery(countQuery, queryParams)
      ]);

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      return {
        transfers: transfers.map(transfer => ({
          id: transfer.id,
          transactionCode: transfer.transaction_code,
          type: transfer.transfer_type,
          sourceAccount: transfer.source_account_number,
          destinationAccount: transfer.destination_account_number,
          beneficiaryName: transfer.beneficiary_name,
          amount: parseFloat(transfer.amount),
          currency: {
            code: transfer.currency_code,
            symbol: transfer.currency_symbol
          },
          status: transfer.status,
          description: transfer.description,
          natureza: transfer.natureza,
          createdAt: transfer.created_at,
          processedAt: transfer.processed_at,
          processedBy: transfer.processed_by_name
        })),
        total,
        page: parseInt(page),
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };
    } catch (error) {
      logger.error('Erro ao listar transferências:', error);
      throw error;
    }
  }

  /**
   * Obter naturezas de transferência disponíveis
   */
  async getTransferNatures() {
    return [
      { value: 'kwanza-aoa', label: 'Kwanza - AOA' },
      { value: 'us-dollar-usd', label: 'US Dollar - USD' },
      { value: 'gbp-pound', label: 'Libra Esterlina - GBP' },
      { value: 'salary', label: 'Pagamento de Salário' },
      { value: 'supplier', label: 'Pagamento a Fornecedor' },
      { value: 'personal', label: 'Transferência Pessoal' },
      { value: 'business', label: 'Operação Comercial' }
    ];
  }

  /**
   * Cancelar transferência
   */
  async cancelTransfer(transferId, motivo, userId) {
    try {
      return await executeTransaction(async (connection) => {
        // Verificar se a transferência existe e pode ser cancelada
        const transfer = await executeQueryWithConnection(`
          SELECT tr.*, t.status, t.amount, t.source_account_id, t.destination_account_id
          FROM transfers tr
          JOIN transactions t ON tr.transaction_id = t.id
          WHERE tr.id = ?
        `, [transferId], connection);

        if (!transfer || transfer.length === 0) {
          throw new AppError('Transferência não encontrada', 404, 'TRANSFER_NOT_FOUND');
        }

        if (transfer[0].status !== 'completed') {
          throw new AppError('Apenas transferências concluídas podem ser canceladas', 400, 'INVALID_STATUS');
        }

        // Reverter os saldos das contas
        const amount = transfer[0].amount;
        
        // Reverter conta origem (crédito)
        await executeQueryWithConnection(`
          UPDATE accounts
          SET balance = balance + ?, available_balance = available_balance + ?
          WHERE id = ?
        `, [amount, amount, transfer[0].source_account_id], connection);

        // Reverter conta destino (débito)
        await executeQueryWithConnection(`
          UPDATE accounts
          SET balance = balance - ?, available_balance = available_balance - ?
          WHERE id = ?
        `, [amount, amount, transfer[0].destination_account_id], connection);

        // Atualizar status da transação
        await executeQueryWithConnection(`
          UPDATE transactions
          SET status = 'cancelled', description = CONCAT(description, ' - CANCELADA: ', ?)
          WHERE id = ?
        `, [motivo, transfer[0].transaction_id], connection);

        // Log de auditoria
        await executeQueryWithConnection(`
          INSERT INTO audit_logs (
            user_id, action, table_name, record_id,
            old_values, new_values, ip_address
          ) VALUES (?, 'UPDATE', 'transfers', ?,
            ?, ?, '127.0.0.1')
        `, [
          userId, transferId,
          JSON.stringify({ status: 'completed' }),
          JSON.stringify({ status: 'cancelled', motivo })
        ], connection);

        return { success: true, message: 'Transferência cancelada com sucesso' };
      });
    } catch (error) {
      logger.error('Erro ao cancelar transferência:', error);
      throw error;
    }
  }
}

module.exports = new TransferService();
