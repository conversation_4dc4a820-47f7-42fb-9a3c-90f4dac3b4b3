import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import ActionMenu from '@/components/ui/ActionMenu';
import { Search, Eye, CheckCircle, XCircle, AlertTriangle, Loader2 } from 'lucide-react';
import { useEnhancedToast } from '@/hooks/useEnhancedToast';
import { useConfirm } from '@/contexts/ConfirmDialogContext';
import { transferService, type SuspendedMovement, type TransferFilters } from '@/services/transferService';

export default function MovimentosSuspensos() {
  const { success, error, info } = useEnhancedToast();
  const confirm = useConfirm();
  
  // Estados para dados e paginação
  const [movements, setMovements] = useState<SuspendedMovement[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  
  // Estados para filtros
  const [filtros, setFiltros] = useState({
    search: '',
    status: 'all',
    itemsPorPagina: '10',
    dateFrom: '',
    dateTo: ''
  });
  
  // Estados para modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedMovement, setSelectedMovement] = useState<SuspendedMovement | null>(null);
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(null);
  const [observacoes, setObservacoes] = useState('');

  // Função para carregar movimentos suspensos do backend
  const loadMovements = async (page: number = 1) => {
    setLoading(true);
    try {
      const filters: TransferFilters = {
        page,
        limit: parseInt(filtros.itemsPorPagina),
        searchTerm: filtros.search || undefined,
        status: filtros.status !== 'all' ? filtros.status : undefined,
        dateRange: filtros.dateFrom && filtros.dateTo ? {
          from: filtros.dateFrom,
          to: filtros.dateTo
        } : undefined
      };

      const response = await transferService.getSuspendedMovements(filters);
      
      setMovements(response.movements);
      setTotalPages(response.totalPages);
      setCurrentPage(response.page);
      setTotalItems(response.total);
      
    } catch (err: any) {
      console.error('Erro ao carregar movimentos suspensos:', err);
      error({
        title: 'Erro ao carregar movimentos',
        description: err.response?.data?.message || 'Ocorreu um erro ao buscar os movimentos suspensos. Tente novamente.'
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar dados na inicialização e quando filtros mudarem
  useEffect(() => {
    loadMovements(1);
  }, [filtros.status, filtros.itemsPorPagina, filtros.dateFrom, filtros.dateTo]);

  // Função para pesquisar
  const handleSearch = () => {
    loadMovements(1);
  };

  const formatarMoeda = (valor: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(valor);
  };

  const formatarData = (data: string) => {
    return new Date(data).toLocaleDateString('pt-AO');
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'Suspenso': 'destructive',
      'Aprovado': 'default',
      'Rejeitado': 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status}
      </Badge>
    );
  };

  const handleVisualizarMovement = (movement: SuspendedMovement) => {
    setSelectedMovement(movement);
    setActionType(null);
    setObservacoes('');
    setIsModalOpen(true);
  };

  const handleAprovarMovement = (movement: SuspendedMovement) => {
    setSelectedMovement(movement);
    setActionType('approve');
    setObservacoes('');
    setIsModalOpen(true);
  };

  const handleRejeitarMovement = (movement: SuspendedMovement) => {
    setSelectedMovement(movement);
    setActionType('reject');
    setObservacoes('');
    setIsModalOpen(true);
  };

  const handleConfirmarAcao = async () => {
    if (!selectedMovement || !actionType) return;

    if (actionType === 'reject' && !observacoes.trim()) {
      error({
        title: 'Motivo obrigatório',
        description: 'É necessário informar o motivo da rejeição.'
      });
      return;
    }

    try {
      if (actionType === 'approve') {
        await transferService.approveSuspendedMovement(selectedMovement.id, observacoes.trim() || undefined);
        success({
          title: 'Movimento aprovado',
          description: `O movimento ${selectedMovement.codMovimento} foi aprovado com sucesso.`
        });
      } else {
        await transferService.rejectSuspendedMovement(selectedMovement.id, observacoes.trim());
        success({
          title: 'Movimento rejeitado',
          description: `O movimento ${selectedMovement.codMovimento} foi rejeitado.`
        });
      }

      setIsModalOpen(false);
      loadMovements(currentPage);
    } catch (err: any) {
      console.error('Erro ao processar movimento:', err);
      error({
        title: 'Erro ao processar',
        description: err.response?.data?.message || 'Ocorreu um erro ao processar o movimento. Tente novamente.'
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight dark:text-gray-100 flex items-center gap-2">
            <AlertTriangle className="h-8 w-8 text-yellow-600" />
            Movimentos Suspensos
          </h1>
          <p className="text-muted-foreground dark:text-gray-400">
            Gerencie movimentos bancários que requerem aprovação
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {loading ? '...' : totalItems} movimentos
        </Badge>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {movements.filter(m => m.status === 'Suspenso').length}
              </div>
              <div className="text-sm text-gray-600">Pendentes</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {movements.filter(m => m.status === 'Aprovado').length}
              </div>
              <div className="text-sm text-gray-600">Aprovados</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {movements.filter(m => m.status === 'Rejeitado').length}
              </div>
              <div className="text-sm text-gray-600">Rejeitados</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {totalItems}
              </div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros de Pesquisa</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2 md:col-span-2">
              <label className="text-sm font-medium">Pesquisar</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Código, conta ou usuário..."
                  value={filtros.search}
                  onChange={(e) => setFiltros(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Data Início</label>
              <Input
                type="date"
                value={filtros.dateFrom}
                onChange={(e) => setFiltros(prev => ({ ...prev, dateFrom: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Data Fim</label>
              <Input
                type="date"
                value={filtros.dateTo}
                onChange={(e) => setFiltros(prev => ({ ...prev, dateTo: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={filtros.status} onValueChange={(value) => setFiltros(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="Suspenso">Suspenso</SelectItem>
                  <SelectItem value="Aprovado">Aprovado</SelectItem>
                  <SelectItem value="Rejeitado">Rejeitado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex justify-between items-center mt-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Itens por página</label>
              <Select value={filtros.itemsPorPagina} onValueChange={(value) => setFiltros(prev => ({ ...prev, itemsPorPagina: value }))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button onClick={handleSearch} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Pesquisando...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Pesquisar
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Movimentos Suspensos */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Movimentos Suspensos ({loading ? '...' : movements.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cód Movimento</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Conta</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead>Motivo</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Ação</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Carregando movimentos...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : movements.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      Nenhum movimento suspenso encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  movements.map((movement) => (
                    <TableRow key={movement.id}>
                      <TableCell className="font-medium">{movement.codMovimento}</TableCell>
                      <TableCell>{movement.tipo}</TableCell>
                      <TableCell>{movement.conta}</TableCell>
                      <TableCell className="font-medium text-green-600">
                        {formatarMoeda(movement.valor)}
                      </TableCell>
                      <TableCell>{formatarData(movement.data)}</TableCell>
                      <TableCell className="max-w-xs truncate" title={movement.motivo}>
                        {movement.motivo}
                      </TableCell>
                      <TableCell>{getStatusBadge(movement.status)}</TableCell>
                      <TableCell>
                        <ActionMenu
                          items={[
                            {
                              label: 'Visualizar',
                              icon: Eye,
                              onClick: () => handleVisualizarMovement(movement)
                            },
                            {
                              label: 'Aprovar',
                              icon: CheckCircle,
                              onClick: () => handleAprovarMovement(movement),
                              disabled: movement.status !== 'Suspenso',
                              separator: true
                            },
                            {
                              label: 'Rejeitar',
                              icon: XCircle,
                              onClick: () => handleRejeitarMovement(movement),
                              variant: 'destructive',
                              disabled: movement.status !== 'Suspenso'
                            }
                          ]}
                        />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>

        {/* Paginação */}
        {!loading && totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-muted-foreground">
              Mostrando {((currentPage - 1) * parseInt(filtros.itemsPorPagina)) + 1} a {Math.min(currentPage * parseInt(filtros.itemsPorPagina), totalItems)} de {totalItems} movimentos
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadMovements(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                Anterior
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + Math.max(1, currentPage - 2);
                  if (page > totalPages) return null;
                  return (
                    <Button
                      key={page}
                      variant={page === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => loadMovements(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadMovements(currentPage + 1)}
                disabled={currentPage >= totalPages}
              >
                Próxima
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Modal de Detalhes/Ação */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-gray-100">
              {actionType === 'approve' ? 'Aprovar Movimento' :
               actionType === 'reject' ? 'Rejeitar Movimento' :
               'Detalhes do Movimento'}
            </DialogTitle>
          </DialogHeader>
          {selectedMovement && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Código</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedMovement.codMovimento}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Status</label>
                  <p className="text-sm">{getStatusBadge(selectedMovement.status)}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Tipo</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedMovement.tipo}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Conta</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedMovement.conta}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Valor</label>
                  <p className="text-sm font-medium text-green-600">{formatarMoeda(selectedMovement.valor)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Data</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{formatarData(selectedMovement.data)}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium dark:text-gray-100">Motivo da Suspensão</label>
                <p className="text-sm text-gray-600 dark:text-gray-400">{selectedMovement.motivo}</p>
              </div>

              <div>
                <label className="text-sm font-medium dark:text-gray-100">Usuário</label>
                <p className="text-sm text-gray-600 dark:text-gray-400">{selectedMovement.usuario}</p>
              </div>

              {actionType && (
                <div className="space-y-2">
                  <label className="text-sm font-medium dark:text-gray-100">
                    {actionType === 'approve' ? 'Observações (opcional)' : 'Motivo da Rejeição *'}
                  </label>
                  <Textarea
                    placeholder={actionType === 'approve' ? 'Observações sobre a aprovação...' : 'Informe o motivo da rejeição...'}
                    value={observacoes}
                    onChange={(e) => setObservacoes(e.target.value)}
                    rows={3}
                  />
                </div>
              )}

              {actionType && (
                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleConfirmarAcao}
                    variant={actionType === 'approve' ? 'default' : 'destructive'}
                  >
                    {actionType === 'approve' ? 'Aprovar' : 'Rejeitar'}
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
