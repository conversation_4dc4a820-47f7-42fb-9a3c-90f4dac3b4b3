
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Banknote, MapPin, Battery, Wifi, RefreshCw, AlertTriangle, Calculator, Wallet } from 'lucide-react';
import { atmService, ATM, LoadATMRequest } from '@/services/atmService';
import { treasuryService } from '@/services/treasuryService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

const CarregamentoATM = () => {
  const { hasRole } = useAuth();
  const [atms, setAtms] = useState<ATM[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedATM, setSelectedATM] = useState<ATM | null>(null);
  const [isLoadingModalOpen, setIsLoadingModalOpen] = useState(false);
  const [treasurerBalance, setTreasurerBalance] = useState<number>(0);
  const [loadingBalance, setLoadingBalance] = useState(false);
  const [loadingForm, setLoadingForm] = useState({
    notes: '',
    denominations: {
      notes_10000: 0,
      notes_5000: 0,
      notes_2000: 0,
      notes_1000: 0,
      notes_500: 0,
      notes_200: 0
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Carregar ATMs e saldo do tesoureiro ao montar o componente
  useEffect(() => {
    loadATMs();
    loadTreasurerBalance();
  }, []);

  // Auto-calcular valor total quando denominações mudarem
  useEffect(() => {
    // Este useEffect garante que o valor total seja sempre calculado automaticamente
  }, [loadingForm.denominations]);

  const loadATMs = async () => {
    try {
      setLoading(true);
      const data = await atmService.getATMs();
      setAtms(data.atms);
    } catch (error) {
      console.error('Erro ao carregar ATMs:', error);
      toast.error('Erro ao carregar lista de ATMs');
    } finally {
      setLoading(false);
    }
  };

  // Carregar saldo do tesoureiro
  const loadTreasurerBalance = async () => {
    try {
      setLoadingBalance(true);
      const balanceData = await treasuryService.getMyBalance();
      // Extrair o valor numérico do objeto retornado
      const validBalance = balanceData && balanceData.current_balance
        ? parseFloat(balanceData.current_balance)
        : 0;
      setTreasurerBalance(validBalance);
    } catch (error) {
      console.error('Erro ao carregar saldo do tesoureiro:', error);
      toast.error('Erro ao carregar saldo do tesoureiro');
      setTreasurerBalance(0);
    } finally {
      setLoadingBalance(false);
    }
  };

  // Calcular total das denominações (apenas 200 Kz para cima)
  const calculateDenominationsTotal = () => {
    const { denominations } = loadingForm;
    return (
      denominations.notes_10000 * 10000 +
      denominations.notes_5000 * 5000 +
      denominations.notes_2000 * 2000 +
      denominations.notes_1000 * 1000 +
      denominations.notes_500 * 500 +
      denominations.notes_200 * 200
    );
  };

  // Atualizar denominação
  const updateDenomination = (key: string, value: number) => {
    setLoadingForm(prev => ({
      ...prev,
      denominations: {
        ...prev.denominations,
        [key]: Math.max(0, value)
      }
    }));
  };

  // Abrir modal de carregamento
  const openLoadingModal = (atm: ATM) => {
    if (atm.status === 'offline') {
      toast.error('ATM está offline e não pode ser carregado');
      return;
    }
    setSelectedATM(atm);
    setIsLoadingModalOpen(true);
    // Reset form (removidas denominações baixas)
    setLoadingForm({
      notes: '',
      denominations: {
        notes_10000: 0,
        notes_5000: 0,
        notes_2000: 0,
        notes_1000: 0,
        notes_500: 0,
        notes_200: 0
      }
    });
  };

  // Submeter carregamento
  const handleLoadATM = async () => {
    if (!selectedATM) return;

    const amount = calculateDenominationsTotal(); // Valor agora é auto-calculado

    // Validações
    if (amount <= 0) {
      toast.error('Informe pelo menos uma denominação para carregar');
      return;
    }

    // Validação de saldo do tesoureiro
    if (amount > treasurerBalance) {
      toast.error(`Saldo insuficiente. Saldo atual: ${treasuryService.formatCurrency(treasurerBalance)}`);
      return;
    }

    const newBalance = selectedATM.current_balance + amount;
    if (newBalance > selectedATM.cash_capacity) {
      toast.error('Carregamento excede a capacidade do ATM');
      return;
    }

    try {
      setIsSubmitting(true);

      const loadRequest: LoadATMRequest = {
        atm_id: selectedATM.id,
        amount,
        denominations: loadingForm.denominations,
        notes: loadingForm.notes || undefined
      };

      await atmService.loadATM(loadRequest);

      toast.success(`Carregamento de ${treasuryService.formatCurrency(amount)} realizado com sucesso`);
      setIsLoadingModalOpen(false);
      loadATMs(); // Recarregar lista de ATMs
      loadTreasurerBalance(); // Recarregar saldo do tesoureiro

    } catch (error: any) {
      console.error('Erro ao carregar ATM:', error);
      toast.error(error.message || 'Erro ao realizar carregamento');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando ATMs...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Carregamento ATM</h1>
          <p className="text-gray-600 dark:text-gray-400">Gestão e carregamento dos caixas automáticos</p>
        </div>
        <Button onClick={loadATMs} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Atualizar
        </Button>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {atms.map((atm) => (
          <Card key={atm.id}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {atm.location}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className={`font-bold ${atmService.getStatusColor(atm.status)}`}>
                    {atmService.getStatusText(atm.status)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Saldo Atual:</span>
                  <span className={`font-bold ${atm.status === 'low_balance' ? 'text-orange-600' : ''}`}>
                    {atmService.formatCurrency(atm.current_balance)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Capacidade:</span>
                  <span className="font-bold">{atmService.formatCurrency(atm.cash_capacity)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Ocupação:</span>
                  <span className="font-bold">{atm.capacity_percentage}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Conexão:</span>
                  <Wifi className={`h-4 w-4 ${atm.status === 'offline' ? 'text-red-600' : 'text-green-600'}`} />
                </div>
                <div className="flex justify-between items-center">
                  <span>Energia:</span>
                  <Battery className={`h-4 w-4 ${atm.status === 'offline' ? 'text-red-600' : 'text-green-600'}`} />
                </div>

                {atm.status === 'offline' ? (
                  <Button className="w-full mt-4" variant="destructive" disabled>
                    Manutenção
                  </Button>
                ) : (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="w-full">
                          <Button
                            className="w-full mt-4"
                            variant={atm.status === 'low_balance' ? 'default' : 'outline'}
                            onClick={() => openLoadingModal(atm)}
                            disabled={hasRole(['admin', 'gerente'])}
                          >
                            {atm.status === 'low_balance' && <AlertTriangle className="h-4 w-4 mr-2" />}
                            Carregar ATM
                          </Button>
                        </div>
                      </TooltipTrigger>
                      {hasRole(['admin', 'gerente']) && (
                        <TooltipContent>
                          <p>Apenas tesoureiros podem carregar ATMs</p>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

      </div>

      <Card>
        <CardHeader>
          <CardTitle className="dark:text-gray-100">Histórico de Carregamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Principal - Carregamento</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Hoje, 09:30 por João Silva</div>
              </div>
              <div className="text-green-600 font-bold">+22.500.000 Kz</div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Entrada - Carregamento</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Ontem, 16:45 por Maria Santos</div>
              </div>
              <div className="text-green-600 font-bold">+13.500.000 Kz</div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Drive-Through - Manutenção</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">2 dias atrás por Técnico Externo</div>
              </div>
              <div className="text-red-600 font-bold">Offline</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Carregamento */}
      <Dialog open={isLoadingModalOpen} onOpenChange={setIsLoadingModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Carregar {selectedATM?.location}
            </DialogTitle>
          </DialogHeader>

          {selectedATM && (
            <div className="space-y-6">
              {/* Saldo do Tesoureiro - Movido para cima */}
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 mb-2">
                  <Wallet className="h-4 w-4" />
                  <span className="font-medium">Saldo Atual do Tesoureiro</span>
                </div>
                <p className="text-lg font-bold text-blue-800 dark:text-blue-200">
                  {loadingBalance ? 'Carregando...' : treasuryService.formatCurrency(treasurerBalance)}
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  Valor disponível para carregamento de ATMs
                </p>
              </div>

              {/* Informações do ATM - Movido para baixo */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Saldo Atual ATM:</span>
                  <p className="font-bold">{atmService.formatCurrency(selectedATM.current_balance)}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Capacidade ATM:</span>
                  <p className="font-bold">{atmService.formatCurrency(selectedATM.cash_capacity)}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Ocupação ATM:</span>
                  <p className="font-bold">{selectedATM.capacity_percentage}%</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Espaço Disponível:</span>
                  <p className="font-bold">{atmService.formatCurrency(selectedATM.cash_capacity - selectedATM.current_balance)}</p>
                </div>
              </div>

              {/* Formulário de Carregamento */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Informações Gerais */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="amount">Valor Total (Kz) *</Label>
                    <div className="relative">
                      <Input
                        id="amount"
                        type="text"
                        readOnly
                        value={treasuryService.formatCurrency(calculateDenominationsTotal())}
                        className="bg-gray-50 dark:bg-gray-800 text-lg font-bold cursor-not-allowed"
                      />
                      <Calculator className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Valor calculado automaticamente a partir das denominações
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="notes">Observações</Label>
                    <Textarea
                      id="notes"
                      value={loadingForm.notes}
                      onChange={(e) => setLoadingForm(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Observações sobre o carregamento..."
                      rows={3}
                    />
                  </div>

                  {/* Alerta de validação de saldo */}
                  {calculateDenominationsTotal() > 0 && (
                    <Alert className={calculateDenominationsTotal() > treasurerBalance ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : ''}>
                      <AlertTriangle className={`h-4 w-4 ${calculateDenominationsTotal() > treasurerBalance ? 'text-red-600' : ''}`} />
                      <AlertDescription>
                        <strong>Total a Carregar:</strong> {treasuryService.formatCurrency(calculateDenominationsTotal())}
                        {calculateDenominationsTotal() > treasurerBalance && (
                          <div className="text-red-600 font-medium mt-1">
                            ⚠️ Saldo insuficiente! Disponível: {treasuryService.formatCurrency(treasurerBalance)}
                          </div>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Denominações */}
                <div className="space-y-4">
                  <h4 className="font-semibold">Denominações a Carregar</h4>
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {[
                      { key: 'notes_10000', label: '10.000 Kz', value: 10000 },
                      { key: 'notes_5000', label: '5.000 Kz', value: 5000 },
                      { key: 'notes_2000', label: '2.000 Kz', value: 2000 },
                      { key: 'notes_1000', label: '1.000 Kz', value: 1000 },
                      { key: 'notes_500', label: '500 Kz', value: 500 },
                      { key: 'notes_200', label: '200 Kz', value: 200 }
                    ].map(({ key, label, value }) => (
                      <div key={key} className="grid grid-cols-2 gap-2 items-center">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">{label}</span>
                          <Input
                            type="number"
                            min="0"
                            value={loadingForm.denominations[key as keyof typeof loadingForm.denominations]}
                            onChange={(e) => updateDenomination(key, parseInt(e.target.value) || 0)}
                            className="w-20 text-center"
                          />
                        </div>
                        <div className="text-right">
                          <span className="text-sm text-gray-600">
                            {treasuryService.formatCurrency(
                              (loadingForm.denominations[key as keyof typeof loadingForm.denominations] || 0) * value
                            )}
                          </span>
                        </div>
                      </div>
                    ))}

                    <div className="border-t pt-3 mt-3">
                      <div className="flex justify-between font-bold">
                        <span>Total das Denominações:</span>
                        <span>{treasuryService.formatCurrency(calculateDenominationsTotal())}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Botões */}
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setIsLoadingModalOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleLoadATM}
                  disabled={isSubmitting || calculateDenominationsTotal() === 0 || calculateDenominationsTotal() > treasurerBalance}
                >
                  {isSubmitting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Carregando...
                    </>
                  ) : (
                    <>
                      <Banknote className="h-4 w-4 mr-2" />
                      Confirmar Carregamento
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CarregamentoATM;
