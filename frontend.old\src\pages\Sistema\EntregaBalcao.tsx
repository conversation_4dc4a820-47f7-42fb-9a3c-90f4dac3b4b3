import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Building, Calculator, CheckCircle, AlertCircle, Loader2, Shield, Users } from 'lucide-react';
import {
  treasuryService,
  CashDenominations
} from '@/services/treasuryService';
import { counterService, CounterUser } from '@/services/counterService';
import { OperationSummary } from '@/components/common/OperationSummary';

const EntregaBalcao = () => {
  const [formData, setFormData] = useState({
    counter_user_id: '',
    operator_name: '',
    branch_name: '',
    amount: '',
    notes: ''
  });

  const [denominations, setDenominations] = useState<CashDenominations>(
    treasuryService.getEmptyDenominations()
  );

  const [counterOperators, setCounterOperators] = useState<CounterUser[]>([]);
  const [isLoadingOperators, setIsLoadingOperators] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { toast } = useToast();
  const { user } = useAuth();

  // Carregar operadores de balcão
  useEffect(() => {
    const loadCounterOperators = async () => {
      if (!user) return;

      try {
        setIsLoadingOperators(true);
        const operators = await counterService.getCounterUsers();

        // Filtrar operadores da mesma agência do tesoureiro logado
        const filteredOperators = operators.filter(operator =>
          operator.is_active && (
            operator.branch_name === user.branch?.name ||
            !operator.branch_name ||
            !user.branch?.name
          )
        );

        setCounterOperators(filteredOperators);
      } catch (error) {
        console.error('Erro ao carregar operadores de balcão:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar lista de operadores",
          variant: "destructive"
        });
      } finally {
        setIsLoadingOperators(false);
      }
    };

    loadCounterOperators();
  }, [user, toast]);

  // Validar formulário
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.counter_user_id) {
      newErrors.counter_user_id = 'Operador de destino é obrigatório';
    }

    const denominationsTotal = treasuryService.calculateDenominationsTotal(denominations);

    if (denominationsTotal <= 0) {
      newErrors.denominations = 'Informe pelo menos uma denominação';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submeter formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const totalAmount = treasuryService.calculateDenominationsTotal(denominations);

      const deliveryData = {
        counter_user_id: formData.counter_user_id,
        amount: totalAmount,
        denominations,
        notes: formData.notes || undefined
      };

      await counterService.deliverToCounter(deliveryData);

      toast({
        title: "Sucesso",
        description: `Entrega de ${treasuryService.formatCurrency(totalAmount)} ao operador ${formData.operator_name} realizada com sucesso`
      });

      // Limpar formulário
      setFormData({
        counter_user_id: '',
        operator_name: '',
        branch_name: '',
        amount: '',
        notes: ''
      });
      setDenominations(treasuryService.getEmptyDenominations());
      setErrors({});

    } catch (error: any) {
      console.error('Erro ao realizar entrega:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao realizar entrega ao balcão. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Atualizar dados do operador selecionado
  const handleOperatorChange = (operatorId: string) => {
    const selectedOperator = counterOperators.find(op => op.id === operatorId);
    if (selectedOperator) {
      setFormData(prev => ({
        ...prev,
        counter_user_id: operatorId,
        operator_name: selectedOperator.full_name,
        branch_name: selectedOperator.branch_name || user?.branch?.name || ''
      }));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Entrega ao Balcão</h1>
          <p className="text-gray-600 dark:text-gray-400">Registar entrega de valores para operadores de balcão</p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Informações da Entrega
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 mb-2">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Entrega para Operador de Balcão</span>
                </div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Esta operação registará a entrega de valores do cofre principal para um operador de balcão específico.
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="counter_user_id">Operador de Destino *</Label>
                  <Select
                    value={formData.counter_user_id}
                    onValueChange={handleOperatorChange}
                    disabled={isLoadingOperators}
                  >
                    <SelectTrigger className={errors.counter_user_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder={
                        isLoadingOperators
                          ? "Carregando operadores..."
                          : counterOperators.length === 0
                            ? "Nenhum operador disponível"
                            : "Selecione o operador"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {counterOperators.map((operator) => (
                        <SelectItem key={operator.id} value={operator.id}>
                          {operator.full_name} - Saldo: {treasuryService.formatCurrency(operator.balance)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.counter_user_id && (
                    <p className="text-sm text-red-500 mt-1">{errors.counter_user_id}</p>
                  )}
                </div>

                {formData.operator_name && (
                  <div className="bg-gray-50 p-3 rounded border dark:bg-gray-700">
                    <div className="text-sm">
                      <div><strong>Operador:</strong> {formData.operator_name}</div>
                      <div><strong>Agência:</strong> {formData.branch_name}</div>
                      {(() => {
                        const selectedOperator = counterOperators.find(op => op.id === formData.counter_user_id);
                        return selectedOperator && (
                          <div><strong>Saldo Atual:</strong> {treasuryService.formatCurrency(selectedOperator.balance)}</div>
                        );
                      })()}
                    </div>
                  </div>
                )}

                <div>
                  <Label htmlFor="amount">Valor Total (AOA) *</Label>
                  <div className="relative">
                    <Input
                      id="amount"
                      type="text"
                      readOnly
                      value={treasuryService.formatCurrency(treasuryService.calculateDenominationsTotal(denominations))}
                      className="bg-gray-50 dark:bg-gray-800 text-lg font-bold cursor-not-allowed"
                    />
                    <Calculator className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Valor calculado automaticamente a partir das denominações
                  </p>
                </div>

                <div>
                  <Label htmlFor="notes">Observações</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Observações sobre a entrega (opcional)"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Denominações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Contagem de Denominações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Notas Grandes */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Grandes</h4>
                {[
                  { key: 'notes_10000', label: 'Notas de 10.000 Kz', value: 10000 },
                  { key: 'notes_5000', label: 'Notas de 5.000 Kz', value: 5000 },
                  { key: 'notes_2000', label: 'Notas de 2.000 Kz', value: 2000 },
                  { key: 'notes_1000', label: 'Notas de 1.000 Kz', value: 1000 }
                ].map(({ key, label, value }) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-sm">{label}</span>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="0"
                        className="w-20 text-center"
                        value={denominations[key as keyof CashDenominations]}
                        onChange={(e) => setDenominations(prev => ({
                          ...prev,
                          [key]: parseInt(e.target.value) || 0
                        }))}
                      />
                      <span className="text-sm text-muted-foreground w-24">
                        {treasuryService.formatCurrency((denominations[key as keyof CashDenominations] || 0) * value)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Notas Pequenas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Pequenas</h4>
                {[
                  { key: 'notes_500', label: 'Notas de 500 Kz', value: 500 },
                  { key: 'notes_200', label: 'Notas de 200 Kz', value: 200 }
                ].map(({ key, label, value }) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-sm">{label}</span>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="0"
                        className="w-20 text-center"
                        value={denominations[key as keyof CashDenominations]}
                        onChange={(e) => setDenominations(prev => ({
                          ...prev,
                          [key]: parseInt(e.target.value) || 0
                        }))}
                      />
                      <span className="text-sm text-muted-foreground w-24">
                        {treasuryService.formatCurrency((denominations[key as keyof CashDenominations] || 0) * value)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Total Calculado */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Calculado:</span>
                  <span className="text-lg font-bold">
                    {treasuryService.formatCurrency(treasuryService.calculateDenominationsTotal(denominations))}
                  </span>
                </div>
                {errors.denominations && (
                  <p className="text-sm text-red-500 mt-2">{errors.denominations}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Resumo da Operação */}
          <OperationSummary
            currentBalance={0} // TODO: Implementar carregamento do saldo do cofre principal
            deliveryAmount={treasuryService.calculateDenominationsTotal(denominations)}
            title="Resumo da Operação"
            formatCurrency={treasuryService.formatCurrency}
            showValidation={true}
            className="lg:col-span-1"
          />
        </div>

        {/* Botão de Submissão */}
        <div className="flex justify-end mt-6">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center gap-2 min-w-[200px]"
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4" />
            )}
            {isSubmitting ? 'Processando...' : 'Confirmar Entrega'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EntregaBalcao;
