import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';

/**
 * Hook para facilitar verificações de permissões e roles
 */
export const usePermissions = () => {
  const { user, hasPermission, hasRole } = useAuth();

  /**
   * Verifica se o usuário pode acessar uma funcionalidade específica
   */
  const canAccess = (module: string, action: string = 'read'): boolean => {
    return hasPermission(module, action);
  };

  /**
   * Verifica se o usuário pode editar/modificar uma funcionalidade
   */
  const canEdit = (module: string): boolean => {
    return hasPermission(module, 'write');
  };

  /**
   * Verifica se o usuário pode deletar em uma funcionalidade
   */
  const canDelete = (module: string): boolean => {
    return hasPermission(module, 'delete');
  };

  /**
   * Verifica se o usuário tem um dos roles especificados
   */
  const isRole = (roles: UserRole | UserRole[]): boolean => {
    return hasRole(roles);
  };

  /**
   * Verifica se é administrador
   */
  const isAdmin = (): boolean => {
    return hasRole('admin');
  };

  /**
   * Verifica se é gerente ou superior
   */
  const isManager = (): boolean => {
    return hasRole(['admin', 'gerente']);
  };

  /**
   * Verifica se pode acessar configurações do sistema
   */
  const canAccessSystemSettings = (): boolean => {
    return hasPermission('sistema', 'read');
  };

  /**
   * Verifica se pode gerenciar usuários
   */
  const canManageUsers = (): boolean => {
    return hasPermission('usuarios', 'write');
  };

  /**
   * Verifica se pode acessar tesouraria
   */
  const canAccessTreasury = (): boolean => {
    return hasPermission('tesouraria', 'read');
  };

  /**
   * Verifica se pode operar caixa
   */
  const canOperateCashier = (): boolean => {
    return hasPermission('caixa', 'write');
  };

  /**
   * Verifica se pode acessar relatórios
   */
  const canAccessReports = (): boolean => {
    return hasPermission('relatorios', 'read');
  };

  /**
   * Verifica se pode gerenciar clientes
   */
  const canManageClients = (): boolean => {
    return hasPermission('clientes', 'write');
  };

  /**
   * Verifica se pode fazer transferências
   */
  const canMakeTransfers = (): boolean => {
    return hasPermission('transferencias', 'write');
  };

  /**
   * Verifica se pode gerenciar cartões
   */
  const canManageCards = (): boolean => {
    return hasPermission('cartoes', 'write');
  };

  /**
   * Verifica se pode operar câmbios
   */
  const canOperateExchange = (): boolean => {
    return hasPermission('cambios', 'write');
  };

  /**
   * Verifica se pode gerenciar seguros
   */
  const canManageInsurance = (): boolean => {
    return hasPermission('seguros', 'write');
  };

  /**
   * Verifica se pode gerenciar ATMs
   */
  const canManageATM = (): boolean => {
    return hasPermission('atm', 'write');
  };

  return {
    user,
    canAccess,
    canEdit,
    canDelete,
    isRole,
    isAdmin,
    isManager,
    canAccessSystemSettings,
    canManageUsers,
    canAccessTreasury,
    canOperateCashier,
    canAccessReports,
    canManageClients,
    canMakeTransfers,
    canManageCards,
    canOperateExchange,
    canManageInsurance,
    canManageATM
  };
};
