
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { DollarSign, TrendingUp, TrendingDown, RefreshCw, Wifi, WifiOff, Clock } from 'lucide-react';
import { exchangeService, ExchangeRate, WorldCurrencyRate } from '@/services/exchangeService';

const Cambios = () => {
  const { toast } = useToast();

  // Estados para cotações em tempo real
  const [exchangeRates, setExchangeRates] = useState<ExchangeRate[]>([]);
  const [worldCurrencies, setWorldCurrencies] = useState<WorldCurrencyRate[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<string>('');
  const [isOnline, setIsOnline] = useState(true);

  // Estados para conversor
  const [fromCurrency, setFromCurrency] = useState('USD');
  const [toCurrency, setToCurrency] = useState('AOA');
  const [amount, setAmount] = useState<string>('');
  const [convertedAmount, setConvertedAmount] = useState<string>('0.00');
  const [conversionRate, setConversionRate] = useState<number>(0);

  // Carregar cotações
  const loadExchangeRates = async () => {
    try {
      setLoading(true);

      // Carregar cotações para Angola (USD, EUR, BRL, GBP -> AOA)
      const rates = await exchangeService.getRealTimeRates();
      setExchangeRates(rates);

      // Carregar taxas das moedas mundiais (em relação ao USD)
      const worldRates = await exchangeService.getWorldCurrencyRates();
      setWorldCurrencies(worldRates);

      setLastUpdate(exchangeService.getLastUpdateTime());
      setIsOnline(exchangeService.isDataFresh());

      // Atualizar taxa de conversão se necessário
      if (fromCurrency && toCurrency) {
        updateConversionRate(fromCurrency, toCurrency, rates);
      }

    } catch (error) {
      console.error('Erro ao carregar cotações:', error);
      setIsOnline(false);
      toast({
        title: "Erro ao carregar cotações",
        description: "Usando dados em cache. Verifique sua conexão.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Atualizar taxa de conversão (suporta todas as combinações)
  const updateConversionRate = async (from: string, to: string) => {
    try {
      if (from === to) {
        setConversionRate(1);
        if (amount && !isNaN(parseFloat(amount))) {
          setConvertedAmount(parseFloat(amount).toFixed(2));
        }
        return;
      }

      // Usar o serviço para calcular a conversão
      const conversion = await exchangeService.convertCurrency(1, from, to);
      setConversionRate(conversion.rate);

      // Recalcular conversão se há valor
      if (amount && !isNaN(parseFloat(amount))) {
        const result = parseFloat(amount) * conversion.rate;
        setConvertedAmount(result.toFixed(2));
      }
    } catch (error) {
      console.error('Erro ao calcular taxa de conversão:', error);
      setConversionRate(0);
      setConvertedAmount('0.00');
    }
  };

  // Converter valor
  const handleAmountChange = (value: string) => {
    setAmount(value);

    if (value && !isNaN(parseFloat(value)) && conversionRate > 0) {
      const result = parseFloat(value) * conversionRate;
      setConvertedAmount(result.toFixed(2));
    } else {
      setConvertedAmount('0.00');
    }
  };

  // Efeitos
  useEffect(() => {
    loadExchangeRates();

    // Atualização automática a cada 60 segundos
    const interval = setInterval(loadExchangeRates, 60000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    updateConversionRate(fromCurrency, toCurrency);
  }, [fromCurrency, toCurrency]);

  // Formatar moeda
  const formatCurrency = (value: number, currency: string) => {
    const currencySymbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'BRL': 'R$',
      'AOA': 'Kz',
      'JPY': '¥',
      'CAD': 'C$',
      'AUD': 'A$',
      'CHF': 'CHF',
      'CNY': '¥',
      'ZAR': 'R'
    };

    const symbol = currencySymbols[currency] || currency;

    // Moedas que não usam decimais (como JPY)
    const noDecimalCurrencies = ['JPY'];
    const decimals = noDecimalCurrencies.includes(currency) ? 0 : 2;

    if (currency === 'AOA') {
      return `${value.toFixed(decimals)} ${symbol}`;
    }

    return `${symbol} ${value.toFixed(decimals)}`;
  };

  // Formatar variação
  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <DollarSign className="h-8 w-8 text-twins-primary" />
            Operações de Câmbio
          </h1>
          <p className="text-gray-600 dark:text-gray-400">Gestão de moedas estrangeiras e taxas de câmbio em tempo real</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isOnline ? "default" : "destructive"} className="flex items-center gap-1">
            {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
            {isOnline ? 'Online' : 'Offline'}
          </Badge>
          <Button onClick={loadExchangeRates} disabled={loading} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar Cotações
          </Button>
        </div>
      </div>

      {/* Indicador de última atualização */}
      <div className="flex items-center justify-between bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
        <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
          <Clock className="h-4 w-4" />
          <span className="text-sm font-medium">{lastUpdate}</span>
        </div>
        <div className="text-xs text-blue-600 dark:text-blue-400">
          Atualização automática a cada 60 segundos
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16"></div>
                <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-24 mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20"></div>
              </CardContent>
            </Card>
          ))
        ) : (
          // Cotações reais
          exchangeRates.map((rate) => (
            <Card key={`${rate.from}-${rate.to}`}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {rate.from}/{rate.to === 'AOA' ? 'KZ' : rate.to}
                </CardTitle>
                {rate.trend === 'up' && <TrendingUp className="h-4 w-4 text-green-600" />}
                {rate.trend === 'down' && <TrendingDown className="h-4 w-4 text-red-600" />}
                {rate.trend === 'stable' && <div className="h-4 w-4 rounded-full bg-gray-400"></div>}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold dark:text-gray-100">
                  {formatCurrency(rate.rate, rate.to)}
                </div>
                <p className={`text-xs ${
                  rate.trend === 'up' ? 'text-green-600' :
                  rate.trend === 'down' ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {formatChange(rate.change, rate.changePercent)}
                </p>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Conversor de Moedas
              <Button variant="outline" size="sm" onClick={loadExchangeRates} disabled={loading}>
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium dark:text-gray-100">De</label>
                <Select value={fromCurrency} onValueChange={setFromCurrency}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecionar moeda" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - Dólar Americano</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="BRL">BRL - Real Brasileiro</SelectItem>
                    <SelectItem value="GBP">GBP - Libra Esterlina</SelectItem>
                    <SelectItem value="AOA">AOA - Kwanza</SelectItem>
                    <SelectItem value="JPY">JPY - Iene Japonês</SelectItem>
                    <SelectItem value="CAD">CAD - Dólar Canadense</SelectItem>
                    <SelectItem value="AUD">AUD - Dólar Australiano</SelectItem>
                    <SelectItem value="CHF">CHF - Franco Suíço</SelectItem>
                    <SelectItem value="CNY">CNY - Yuan Chinês</SelectItem>
                    <SelectItem value="ZAR">ZAR - Rand Sul-Africano</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Para</label>
                <Select value={toCurrency} onValueChange={setToCurrency}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecionar moeda" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - Dólar Americano</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="BRL">BRL - Real Brasileiro</SelectItem>
                    <SelectItem value="GBP">GBP - Libra Esterlina</SelectItem>
                    <SelectItem value="AOA">AOA - Kwanza</SelectItem>
                    <SelectItem value="JPY">JPY - Iene Japonês</SelectItem>
                    <SelectItem value="CAD">CAD - Dólar Canadense</SelectItem>
                    <SelectItem value="AUD">AUD - Dólar Australiano</SelectItem>
                    <SelectItem value="CHF">CHF - Franco Suíço</SelectItem>
                    <SelectItem value="CNY">CNY - Yuan Chinês</SelectItem>
                    <SelectItem value="ZAR">ZAR - Rand Sul-Africano</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Valor</label>
                <Input
                  type="number"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => handleAmountChange(e.target.value)}
                  className="dark:bg-gray-700 dark:text-gray-100"
                />
              </div>
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Resultado</label>
                <Input
                  value={convertedAmount}
                  readOnly
                  className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100"
                />
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              <div className="text-sm text-blue-700 dark:text-blue-300">
                <strong>Taxa aplicada:</strong> 1 {fromCurrency} = {conversionRate.toFixed(4)} {toCurrency}
              </div>
              <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                {lastUpdate}
              </div>
            </div>

            <Button className="w-full" disabled={!amount || conversionRate === 0}>
              Executar Operação
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Principais Moedas Mundiais
              <Button variant="outline" size="sm" onClick={loadExchangeRates} disabled={loading}>
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {/* Tabela de moedas principais */}
              <div className="grid grid-cols-3 gap-2 text-xs font-medium text-gray-500 dark:text-gray-400 pb-2 border-b">
                <div>Moeda</div>
                <div className="text-right">Taxa (USD)</div>
                <div className="text-right">Variação</div>
              </div>

              {loading ? (
                // Skeleton loading
                Array.from({ length: 8 }).map((_, index) => (
                  <div key={index} className="grid grid-cols-3 gap-2 py-2 animate-pulse">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 mr-2"></div>
                      <div className="space-y-1">
                        <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-2 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded ml-auto"></div>
                    </div>
                    <div className="text-right">
                      <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded ml-auto"></div>
                    </div>
                  </div>
                ))
              ) : (
                // Dados reais da API
                worldCurrencies.map((currency) => {
                  const isPositive = currency.changePercent >= 0;
                  return (
                    <div key={currency.code} className="grid grid-cols-3 gap-2 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-xs font-bold text-blue-600 dark:text-blue-300 mr-2">
                          {currency.code.charAt(0)}
                        </div>
                        <div>
                          <div className="font-medium text-sm dark:text-gray-100">{currency.code}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{currency.name}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium dark:text-gray-100">
                          {currency.rate.toFixed(currency.code === 'JPY' ? 2 : 4)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                          {isPositive ? '+' : ''}{currency.changePercent.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  );
                })
              )}

              <div className="pt-3 border-t">
                <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  Taxas em relação ao USD • Atualizado {lastUpdate}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Cambios;
