import React from 'react';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';

export interface ActionMenuItem {
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  variant?: 'default' | 'destructive' | 'warning';
  disabled?: boolean;
  separator?: boolean; // Add separator after this item
}

interface ActionMenuProps {
  items: ActionMenuItem[];
  className?: string;
}

const ActionMenu: React.FC<ActionMenuProps> = ({ items, className = "" }) => {
  const getItemClassName = (variant?: string) => {
    switch (variant) {
      case 'destructive':
        return 'text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 hover:bg-yellow-50 dark:hover:bg-yellow-900/20';
      default:
        return 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100';
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 ${className}`}
          aria-label="Mais ações"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="w-48 dark:bg-gray-800 dark:border-gray-700"
        sideOffset={5}
      >
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <DropdownMenuItem
              onClick={item.onClick}
              disabled={item.disabled}
              className={`flex items-center gap-2 cursor-pointer ${getItemClassName(item.variant)} ${
                item.disabled ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <item.icon className="h-4 w-4" />
              {item.label}
            </DropdownMenuItem>
            {item.separator && index < items.length - 1 && (
              <DropdownMenuSeparator className="dark:bg-gray-700" />
            )}
          </React.Fragment>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ActionMenu;
