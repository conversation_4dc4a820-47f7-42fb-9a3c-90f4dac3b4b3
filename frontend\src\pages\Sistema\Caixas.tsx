import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useConfirm } from '@/contexts/ConfirmDialogContext';
import { Wallet, Search, Trash2, Plus, Edit, RefreshCw, Power } from 'lucide-react';
import {
  CashRegister,
  cashRegisterManagementService
} from '@/services/cashRegisterManagementService';
import CashRegisterModal from '@/components/cash-register/CashRegisterModal';
import { useAuth } from '@/contexts/AuthContext';

const Caixas = () => {
  const confirmDialog = useConfirm();
  const [caixas, setCaixas] = useState<CashRegister[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCashRegister, setSelectedCashRegister] = useState<CashRegister | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  const [filtros, setFiltros] = useState({
    search: '',
    itemsPorPagina: '10'
  });

  const { toast } = useToast();
  const { user } = useAuth();

  // Carregar caixas ao montar componente
  useEffect(() => {
    loadCashRegisters();
  }, []);

  const loadCashRegisters = async () => {
    try {
      setIsLoading(true);
      const cashRegisters = await cashRegisterManagementService.getCashRegisters();
      setCaixas(cashRegisters);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar caixas",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const caixasFiltradas = caixas.filter(caixa => {
    const matchSearch = !filtros.search ||
      caixa.register_number.toLowerCase().includes(filtros.search.toLowerCase()) ||
      caixa.branch_name.toLowerCase().includes(filtros.search.toLowerCase()) ||
      caixa.description?.toLowerCase().includes(filtros.search.toLowerCase()) ||
      caixa.status.toLowerCase().includes(filtros.search.toLowerCase());

    return matchSearch;
  });

  const handleCreateCashRegister = () => {
    setSelectedCashRegister(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleEditCashRegister = (cashRegister: CashRegister) => {
    setSelectedCashRegister(cashRegister);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDeleteCashRegister = async (id: number) => {
    const confirmed = await confirmDialog.confirm({
      title: 'Excluir Caixa',
      description: 'Tem certeza que deseja excluir este caixa? Esta ação não pode ser desfeita.',
      confirmText: 'Excluir',
      cancelText: 'Cancelar',
      variant: 'destructive'
    });

    if (!confirmed) {
      return;
    }

    try {
      await cashRegisterManagementService.deleteCashRegister(id);
      toast({
        title: "Sucesso",
        description: "Caixa excluído com sucesso"
      });
      loadCashRegisters();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao excluir caixa",
        variant: "destructive"
      });
    }
  };

  // Função para forçar fecho de caixa (apenas para Gerente e Admin)
  const handleForceClose = async (caixa: CashRegister) => {
    if (!confirm(`Tem certeza que deseja forçar o fecho do caixa ${caixa.register_number}?\n\nEsta ação irá:\n- Fechar automaticamente o caixa\n- Transferir o saldo para a Tesouraria\n- Criar um registro de auditoria`)) {
      return;
    }

    try {
      const result = await cashRegisterManagementService.forceCloseCashRegister(caixa.id);

      toast({
        title: "Caixa fechado com sucesso",
        description: `Caixa ${result.cash_register.register_number} foi fechado forçadamente. Saldo final: ${new Intl.NumberFormat('pt-AO', {
          style: 'currency',
          currency: 'AOA',
          minimumFractionDigits: 2
        }).format(result.session.closing_balance).replace('AOA', 'Kz')}`,
      });

      // Recarregar a lista de caixas
      loadCashRegisters();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao forçar fecho do caixa",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = cashRegisterManagementService.getStatusBadge(status);
    return <Badge className={statusConfig.className}>{statusConfig.text}</Badge>;
  };

  // Verificar se usuário tem permissão para criar/editar
  const canManageCashRegisters = user?.role === 'admin' || user?.role === 'gerente';

  return (
    <div className="space-y-6 content-container">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Wallet className="h-8 w-8" />
            Gestão de Caixas
            <Badge className="bg-twins-primary text-white hover:bg-twins-primary ml-2">
              {caixas.length}
            </Badge>
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Gestão de caixas físicos do sistema bancário</p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={loadCashRegisters}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>

          {canManageCashRegisters && (
            <Button onClick={handleCreateCashRegister}>
              <Plus className="h-4 w-4 mr-2" />
              Novo Caixa
            </Button>
          )}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-green-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-600">Disponíveis</CardTitle>
            <Wallet className="h-5 w-5 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {caixas.filter(c => c.status === 'available').length}
            </div>
            <p className="text-xs text-green-500 mt-1">
              Prontos para uso
            </p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-600">Em Uso</CardTitle>
            <Wallet className="h-5 w-5 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {caixas.filter(c => c.status === 'in_use').length}
            </div>
            <p className="text-xs text-blue-500 mt-1">
              Em operação
            </p>
          </CardContent>
        </Card>

        <Card className="border-red-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-600">Fechados</CardTitle>
            <Wallet className="h-5 w-5 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {caixas.filter(c => c.status === 'closed').length}
            </div>
            <p className="text-xs text-red-500 mt-1">
              Fora de operação
            </p>
          </CardContent>
        </Card>

        <Card className="border-yellow-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-600">Manutenção</CardTitle>
            <Wallet className="h-5 w-5 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-yellow-600">
              {caixas.filter(c => c.status === 'maintenance').length}
            </div>
            <p className="text-xs text-yellow-500 mt-1">
              Em manutenção
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent className="p-6">
          {/* Controles da tabela */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm">Show</span>
              <Select value={filtros.itemsPorPagina} onValueChange={(value) => setFiltros(prev => ({ ...prev, itemsPorPagina: value }))}>
                <SelectTrigger className="w-16">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm">entries</span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm">Search:</span>
              <div className="relative">
                <Input
                  placeholder=""
                  value={filtros.search}
                  onChange={(e) => setFiltros(prev => ({ ...prev, search: e.target.value }))}
                  className="w-48"
                />
              </div>
            </div>
          </div>

          {/* Tabela */}
          <div className="table-container rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[120px]">
                    Número ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[150px]">
                    Balcão ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[100px]">
                    Status ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[200px]">
                    Descrição ↕
                  </TableHead>
                  <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[120px]">
                    Criado em ↕
                  </TableHead>
                  {canManageCashRegisters && (
                    <TableHead className="cursor-pointer hover:bg-gray-50 min-w-[100px]">
                      Ações ↕
                    </TableHead>
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={canManageCashRegisters ? 6 : 5} className="text-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                      Carregando caixas...
                    </TableCell>
                  </TableRow>
                ) : caixasFiltradas.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={canManageCashRegisters ? 6 : 5} className="text-center py-8 text-gray-500">
                      Nenhum caixa encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  caixasFiltradas.slice(0, parseInt(filtros.itemsPorPagina)).map((caixa) => (
                    <TableRow key={caixa.id}>
                      <TableCell className="font-medium">{caixa.register_number}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{caixa.branch_name}</div>
                          <div className="text-sm text-gray-500">{caixa.branch_code}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(caixa.status)}</TableCell>
                      <TableCell className="table-cell-content" title={caixa.description}>
                        {caixa.description || '-'}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {new Date(caixa.created_at).toLocaleDateString('pt-AO')}
                      </TableCell>
                      {canManageCashRegisters && (
                        <TableCell>
                          <div className="flex gap-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditCashRegister(caixa)}
                              className="h-8 w-8 p-0"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {/* Botão Forçar Fecho - apenas para caixas em uso e perfis Gerente/Admin */}
                            {caixa.status === 'in_use' && (user?.role === 'admin' || user?.role === 'gerente') && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleForceClose(caixa)}
                                className="h-8 w-8 p-0 text-orange-600 hover:text-orange-700 border-orange-300 hover:border-orange-400"
                                title="Forçar Fecho do Caixa"
                              >
                                <Power className="h-4 w-4" />
                              </Button>
                            )}
                            {user?.role === 'admin' && (
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleDeleteCashRegister(caixa.id)}
                                className="h-8 w-8 p-0"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Informações da paginação */}
          <div className="flex justify-between items-center mt-4 text-sm text-gray-600">
            <div>
              Showing {Math.min(parseInt(filtros.itemsPorPagina), caixasFiltradas.length)} of {caixasFiltradas.length} entries
              {filtros.search && ` (filtered from ${caixas.length} total entries)`}
            </div>
            
            {caixasFiltradas.length > parseInt(filtros.itemsPorPagina) && (
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button variant="outline" size="sm" className="bg-primary text-white">
                  1
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Modal para criar/editar caixa */}
      <CashRegisterModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={loadCashRegisters}
        cashRegister={selectedCashRegister}
        mode={modalMode}
      />
    </div>
  );
};

export default Caixas;
