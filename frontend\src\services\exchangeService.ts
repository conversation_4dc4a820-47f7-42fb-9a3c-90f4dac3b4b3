// Serviço de Câmbios com API em Tempo Real
// Integração com ExchangeRate-API (gratuita)

// Interface para cotações de câmbio
export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  change: number;
  changePercent: number;
  lastUpdate: string;
  trend: 'up' | 'down' | 'stable';
}

// Interface para resposta da API externa
interface ExchangeRateAPIResponse {
  success: boolean;
  timestamp: number;
  base: string;
  date: string;
  rates: Record<string, number>;
}

// Interface para dados históricos (para calcular variação)
interface HistoricalData {
  rates: Record<string, number>;
  timestamp: number;
}

// Interface para taxas de moedas mundiais (em relação ao USD)
export interface WorldCurrencyRate {
  code: string;
  name: string;
  rate: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
}

class ExchangeService {
  private readonly BASE_URL = 'https://api.exchangerate-api.com/v4/latest';
  private readonly FALLBACK_URL = 'https://api.fixer.io/latest'; // Fallback
  
  // Cache local para última cotação válida
  private lastValidRates: ExchangeRate[] = [];
  private lastUpdateTime: number = 0;
  
  // Principais pares de moedas para Angola
  private readonly CURRENCY_PAIRS = [
    { from: 'USD', to: 'AOA', name: 'Dólar Americano' },
    { from: 'EUR', to: 'AOA', name: 'Euro' },
    { from: 'BRL', to: 'AOA', name: 'Real Brasileiro' },
    { from: 'GBP', to: 'AOA', name: 'Libra Esterlina' }
  ];

  /**
   * Obter cotações em tempo real
   */
  async getRealTimeRates(): Promise<ExchangeRate[]> {
    try {
      // Verificar se precisa atualizar (cache de 30 segundos)
      const now = Date.now();
      if (now - this.lastUpdateTime < 30000 && this.lastValidRates.length > 0) {
        return this.lastValidRates;
      }

      // Buscar cotações atuais
      const currentRates = await this.fetchCurrentRates();
      
      // Buscar dados históricos para calcular variação (24h atrás)
      const historicalRates = await this.fetchHistoricalRates();
      
      // Processar e combinar dados
      const processedRates = this.processRates(currentRates, historicalRates);
      
      // Atualizar cache
      this.lastValidRates = processedRates;
      this.lastUpdateTime = now;
      
      return processedRates;
      
    } catch (error) {
      console.error('Erro ao obter cotações em tempo real:', error);
      
      // Retornar última cotação válida se disponível
      if (this.lastValidRates.length > 0) {
        console.warn('Usando última cotação válida do cache');
        return this.lastValidRates;
      }
      
      // Fallback para dados estáticos se tudo falhar
      return this.getFallbackRates();
    }
  }

  /**
   * Buscar cotações atuais da API
   */
  private async fetchCurrentRates(): Promise<ExchangeRateAPIResponse> {
    try {
      // Tentar API principal (ExchangeRate-API)
      const response = await fetch(`${this.BASE_URL}/USD`);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (!data.success && data.success !== undefined) {
        throw new Error('API returned unsuccessful response');
      }
      
      return data;
      
    } catch (error) {
      console.warn('API principal falhou, tentando fallback...');
      
      // Tentar API de fallback
      const fallbackResponse = await fetch(`${this.FALLBACK_URL}?access_key=free&base=USD`);
      
      if (!fallbackResponse.ok) {
        throw new Error('Todas as APIs de câmbio falharam');
      }
      
      return await fallbackResponse.json();
    }
  }

  /**
   * Buscar dados históricos (24h atrás) para calcular variação
   */
  private async fetchHistoricalRates(): Promise<HistoricalData | null> {
    try {
      // Para API gratuita, simular dados históricos com pequena variação
      // Em produção, usar endpoint histórico real
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      // Simular variações diferentes para cada moeda (-2% a +2%)
      const aoaVariation = (Math.random() - 0.5) * 0.04;
      const eurVariation = (Math.random() - 0.5) * 0.04;
      const gbpVariation = (Math.random() - 0.5) * 0.04;
      const brlVariation = (Math.random() - 0.5) * 0.04;

      return {
        rates: {
          AOA: 900.0 * (1 + aoaVariation),    // Taxa histórica USD/AOA
          EUR: 0.85 * (1 + eurVariation),     // Taxa histórica USD/EUR
          GBP: 0.74 * (1 + gbpVariation),     // Taxa histórica USD/GBP
          BRL: 5.40 * (1 + brlVariation),     // Taxa histórica USD/BRL
        },
        timestamp: yesterday.getTime()
      };

    } catch (error) {
      console.warn('Não foi possível obter dados históricos:', error);
      return null;
    }
  }

  /**
   * Processar e combinar dados atuais com históricos
   */
  private processRates(current: ExchangeRateAPIResponse, historical: HistoricalData | null): ExchangeRate[] {
    const rates: ExchangeRate[] = [];
    const now = new Date().toISOString();

    this.CURRENCY_PAIRS.forEach(pair => {
      let currentRate = 0;

      // Calcular taxa correta baseada no par de moedas
      if (pair.from === 'USD' && pair.to === 'AOA') {
        // USD para AOA - taxa direta
        currentRate = current.rates[pair.to] || 0;
      } else if (pair.to === 'AOA') {
        // Outras moedas para AOA - calcular taxa cruzada
        const usdToAoa = current.rates['AOA'] || 0;
        const usdToFromCurrency = current.rates[pair.from] || 0;

        if (usdToFromCurrency > 0) {
          // Taxa cruzada: (USD/AOA) / (USD/FROM) = FROM/AOA
          currentRate = usdToAoa / usdToFromCurrency;
        }
      }

      // Calcular taxa histórica usando a mesma lógica
      let historicalRate = currentRate;
      if (historical) {
        if (pair.from === 'USD' && pair.to === 'AOA') {
          historicalRate = historical.rates[pair.to] || currentRate;
        } else if (pair.to === 'AOA') {
          const historicalUsdToAoa = historical.rates['AOA'] || 0;
          const historicalUsdToFromCurrency = historical.rates[pair.from] || 0;

          if (historicalUsdToFromCurrency > 0) {
            historicalRate = historicalUsdToAoa / historicalUsdToFromCurrency;
          }
        }
      }

      const change = currentRate - historicalRate;
      const changePercent = historicalRate > 0 ? (change / historicalRate) * 100 : 0;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (Math.abs(changePercent) > 0.1) {
        trend = changePercent > 0 ? 'up' : 'down';
      }

      rates.push({
        from: pair.from,
        to: pair.to,
        rate: currentRate,
        change: change,
        changePercent: changePercent,
        lastUpdate: now,
        trend: trend
      });
    });

    return rates;
  }

  /**
   * Dados de fallback quando todas as APIs falham
   */
  private getFallbackRates(): ExchangeRate[] {
    const now = new Date().toISOString();
    
    return [
      {
        from: 'USD',
        to: 'AOA',
        rate: 827.50,
        change: 2.07,
        changePercent: 0.25,
        lastUpdate: now,
        trend: 'up'
      },
      {
        from: 'EUR',
        to: 'AOA',
        rate: 895.30,
        change: -1.45,
        changePercent: -0.16,
        lastUpdate: now,
        trend: 'down'
      },
      {
        from: 'BRL',
        to: 'AOA',
        rate: 156.80,
        change: 0.95,
        changePercent: 0.61,
        lastUpdate: now,
        trend: 'up'
      },
      {
        from: 'GBP',
        to: 'AOA',
        rate: 1045.20,
        change: -3.20,
        changePercent: -0.31,
        lastUpdate: now,
        trend: 'down'
      }
    ];
  }

  /**
   * Converter valor entre moedas (suporta todas as combinações)
   */
  async convertCurrency(amount: number, from: string, to: string): Promise<{
    amount: number;
    result: number;
    rate: number;
    lastUpdate: string;
  }> {
    try {
      // Se as moedas são iguais, retornar o mesmo valor
      if (from === to) {
        return {
          amount,
          result: amount,
          rate: 1,
          lastUpdate: new Date().toISOString()
        };
      }

      // Buscar dados da API
      const apiData = await this.fetchCurrentRates();
      const now = new Date().toISOString();

      let rate = 0;

      // Calcular taxa de conversão baseada na combinação de moedas
      if (from === 'USD') {
        // USD para outras moedas - taxa direta da API
        rate = apiData.rates[to] || 0;
      } else if (to === 'USD') {
        // Outras moedas para USD - inverso da taxa
        const fromRate = apiData.rates[from] || 0;
        rate = fromRate > 0 ? 1 / fromRate : 0;
      } else {
        // Conversão cruzada entre duas moedas não-USD
        const fromRate = apiData.rates[from] || 0;
        const toRate = apiData.rates[to] || 0;

        if (fromRate > 0 && toRate > 0) {
          // Converter: FROM → USD → TO
          rate = toRate / fromRate;
        }
      }

      if (rate === 0) {
        throw new Error(`Não foi possível calcular a taxa para ${from}/${to}`);
      }

      const result = amount * rate;

      return {
        amount,
        result,
        rate,
        lastUpdate: now
      };

    } catch (error) {
      console.error('Erro na conversão:', error);
      throw error;
    }
  }

  /**
   * Obter timestamp da última atualização
   */
  getLastUpdateTime(): string {
    if (this.lastUpdateTime === 0) {
      return 'Nunca atualizado';
    }
    
    const now = Date.now();
    const diffSeconds = Math.floor((now - this.lastUpdateTime) / 1000);
    
    if (diffSeconds < 60) {
      return `Atualizado há ${diffSeconds} segundos`;
    } else if (diffSeconds < 3600) {
      const minutes = Math.floor(diffSeconds / 60);
      return `Atualizado há ${minutes} minuto${minutes > 1 ? 's' : ''}`;
    } else {
      const hours = Math.floor(diffSeconds / 3600);
      return `Atualizado há ${hours} hora${hours > 1 ? 's' : ''}`;
    }
  }

  /**
   * Verificar se as cotações estão atualizadas
   */
  isDataFresh(): boolean {
    const now = Date.now();
    return (now - this.lastUpdateTime) < 60000; // Considera fresco se < 1 minuto
  }

  /**
   * Obter taxas das principais moedas mundiais em relação ao USD
   */
  async getWorldCurrencyRates(): Promise<WorldCurrencyRate[]> {
    try {
      // Buscar cotações atuais da API
      const currentRates = await this.fetchCurrentRates();

      // Buscar dados históricos para calcular variação
      const historicalRates = await this.fetchHistoricalRates();

      // Principais moedas mundiais
      const worldCurrencies = [
        { code: 'EUR', name: 'Euro' },
        { code: 'GBP', name: 'Libra Esterlina' },
        { code: 'JPY', name: 'Iene Japonês' },
        { code: 'CAD', name: 'Dólar Canadense' },
        { code: 'AUD', name: 'Dólar Australiano' },
        { code: 'CHF', name: 'Franco Suíço' },
        { code: 'CNY', name: 'Yuan Chinês' },
        { code: 'BRL', name: 'Real Brasileiro' }
      ];

      const rates: WorldCurrencyRate[] = worldCurrencies.map(currency => {
        // Taxa atual em relação ao USD
        const currentRate = currentRates.rates[currency.code] || 0;

        // Taxa histórica (24h atrás)
        let historicalRate = currentRate;
        if (historicalRates && historicalRates.rates[currency.code]) {
          historicalRate = historicalRates.rates[currency.code];
        }

        // Calcular variação
        const change = currentRate - historicalRate;
        const changePercent = historicalRate > 0 ? (change / historicalRate) * 100 : 0;

        // Determinar tendência
        let trend: 'up' | 'down' | 'stable' = 'stable';
        if (Math.abs(changePercent) > 0.1) {
          trend = changePercent > 0 ? 'up' : 'down';
        }

        return {
          code: currency.code,
          name: currency.name,
          rate: currentRate,
          change: change,
          changePercent: changePercent,
          trend: trend
        };
      });

      return rates;

    } catch (error) {
      console.error('Erro ao obter taxas de moedas mundiais:', error);

      // Retornar dados de fallback
      return this.getWorldCurrenciesFallback();
    }
  }

  /**
   * Dados de fallback para moedas mundiais
   */
  private getWorldCurrenciesFallback(): WorldCurrencyRate[] {
    return [
      { code: 'EUR', name: 'Euro', rate: 0.854, change: 0.0062, changePercent: 0.73, trend: 'up' },
      { code: 'GBP', name: 'Libra Esterlina', rate: 0.739, change: 0.005, changePercent: 0.68, trend: 'up' },
      { code: 'JPY', name: 'Iene Japonês', rate: 149.50, change: 1.32, changePercent: 0.89, trend: 'up' },
      { code: 'CAD', name: 'Dólar Canadense', rate: 1.361, change: 0.0016, changePercent: 0.12, trend: 'up' },
      { code: 'AUD', name: 'Dólar Australiano', rate: 1.486, change: -0.0012, changePercent: -0.08, trend: 'down' },
      { code: 'CHF', name: 'Franco Suíço', rate: 0.847, change: 0.0025, changePercent: 0.30, trend: 'up' },
      { code: 'CNY', name: 'Yuan Chinês', rate: 7.234, change: 0.013, changePercent: 0.18, trend: 'up' },
      { code: 'BRL', name: 'Real Brasileiro', rate: 5.43, change: 0.032, changePercent: 0.59, trend: 'up' }
    ];
  }
}

// Instância singleton
export const exchangeService = new ExchangeService();
export default exchangeService;
