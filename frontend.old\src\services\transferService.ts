import { makeRequest } from '@/utils/api';

// Interfaces para Transferências
export interface TransferAccount {
  numero: string;
  titular: string;
  tipo: string;
  saldo?: number;
  status: 'ativa' | 'inativa' | 'bloqueada';
}

export interface InternalTransferRequest {
  natureza: string;
  contaOrigem: string;
  contaDestino: string;
  valor: number;
  descricao?: string;
}

export interface TransferResponse {
  id: string;
  codTransf: string;
  nrContaOrigem: string;
  nrContaDestino: string;
  natureza: string;
  valor: number;
  data: string;
  destinatario: string;
  status: 'Processada' | 'Pendente' | 'Cancelada' | 'Rejeitada';
  descricao?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TransferFilters {
  searchTerm?: string;
  dateRange?: {
    from: string;
    to: string;
  };
  status?: string;
  natureza?: string;
  valorMin?: number;
  valorMax?: number;
  page?: number;
  limit?: number;
}

export interface SuspendedMovement {
  id: string;
  codMovimento: string;
  tipo: 'Transferência' | 'Depósito' | 'Levantamento';
  conta: string;
  valor: number;
  data: string;
  motivo: string;
  status: 'Suspenso' | 'Aprovado' | 'Rejeitado';
  usuario: string;
}

// Serviço de Transferências
export const transferService = {
  // Verificar informações de uma conta
  async verifyAccount(accountNumber: string): Promise<TransferAccount> {
    try {
      const response = await makeRequest<TransferAccount>(`/transfers/verify-account/${accountNumber}`);
      if (response.data) {
        return response.data;
      }
      throw new Error('Dados da conta não encontrados');
    } catch (error) {
      console.error('Erro ao verificar conta:', error);
      throw error;
    }
  },

  // Criar transferência interna
  async createInternalTransfer(transfer: InternalTransferRequest): Promise<TransferResponse> {
    try {
      const response = await makeRequest<TransferResponse>('/transfers/internal', {
        method: 'POST',
        body: JSON.stringify(transfer)
      });
      if (response.data) {
        return response.data;
      }
      throw new Error('Erro ao processar transferência');
    } catch (error) {
      console.error('Erro ao criar transferência interna:', error);
      throw error;
    }
  },

  // Listar transferências com filtros
  async getTransfers(filters: TransferFilters = {}): Promise<{
    transfers: TransferResponse[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const params = new URLSearchParams();

      if (filters.searchTerm) params.append('search', filters.searchTerm);
      if (filters.dateRange?.from) params.append('dateFrom', filters.dateRange.from);
      if (filters.dateRange?.to) params.append('dateTo', filters.dateRange.to);
      if (filters.status) params.append('status', filters.status);
      if (filters.natureza) params.append('natureza', filters.natureza);
      if (filters.valorMin) params.append('valorMin', filters.valorMin.toString());
      if (filters.valorMax) params.append('valorMax', filters.valorMax.toString());
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await makeRequest<{
        transfers: TransferResponse[];
        total: number;
        page: number;
        totalPages: number;
      }>(`/transfers?${params.toString()}`);

      if (response.data) {
        return response.data;
      }
      throw new Error('Erro ao buscar transferências');
    } catch (error) {
      console.error('Erro ao buscar transferências:', error);
      throw error;
    }
  },

  // Obter detalhes de uma transferência
  async getTransferById(id: string): Promise<TransferResponse> {
    try {
      const response = await makeRequest<TransferResponse>(`/transfers/${id}`);
      if (response.data) {
        return response.data;
      }
      throw new Error('Transferência não encontrada');
    } catch (error) {
      console.error('Erro ao buscar transferência:', error);
      throw error;
    }
  },

  // Cancelar transferência (se permitido)
  async cancelTransfer(id: string, motivo?: string): Promise<void> {
    try {
      await makeRequest(`/transfers/${id}/cancel`, {
        method: 'PATCH',
        body: JSON.stringify({ motivo })
      });
    } catch (error) {
      console.error('Erro ao cancelar transferência:', error);
      throw error;
    }
  },

  // Listar movimentos suspensos
  async getSuspendedMovements(filters: TransferFilters = {}): Promise<{
    movements: SuspendedMovement[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const params = new URLSearchParams();

      if (filters.searchTerm) params.append('search', filters.searchTerm);
      if (filters.dateRange?.from) params.append('dateFrom', filters.dateRange.from);
      if (filters.dateRange?.to) params.append('dateTo', filters.dateRange.to);
      if (filters.status) params.append('status', filters.status);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await makeRequest<{
        movements: SuspendedMovement[];
        total: number;
        page: number;
        totalPages: number;
      }>(`/movements/suspended?${params.toString()}`);

      if (response.data) {
        return response.data;
      }
      throw new Error('Erro ao buscar movimentos suspensos');
    } catch (error) {
      console.error('Erro ao buscar movimentos suspensos:', error);
      throw error;
    }
  },

  // Aprovar movimento suspenso
  async approveSuspendedMovement(id: string, observacoes?: string): Promise<void> {
    try {
      await makeRequest(`/movements/suspended/${id}/approve`, {
        method: 'PATCH',
        body: JSON.stringify({ observacoes })
      });
    } catch (error) {
      console.error('Erro ao aprovar movimento suspenso:', error);
      throw error;
    }
  },

  // Rejeitar movimento suspenso
  async rejectSuspendedMovement(id: string, motivo: string): Promise<void> {
    try {
      await makeRequest(`/movements/suspended/${id}/reject`, {
        method: 'PATCH',
        body: JSON.stringify({ motivo })
      });
    } catch (error) {
      console.error('Erro ao rejeitar movimento suspenso:', error);
      throw error;
    }
  },

  // Obter naturezas de transferência disponíveis
  async getTransferNatures(): Promise<Array<{ value: string; label: string; }>> {
    try {
      const response = await makeRequest<Array<{ value: string; label: string; }>>('/transfers/natures');
      if (response.data) {
        return response.data;
      }
      throw new Error('Erro ao buscar naturezas');
    } catch (error) {
      console.error('Erro ao buscar naturezas de transferência:', error);
      // Fallback para naturezas padrão
      return [
        { value: 'kwanza-aoa', label: 'Kwanza - AOA' },
        { value: 'us-dollar-usd', label: 'US Dollar - USD' },
        { value: 'gbp-pound', label: 'Libra Esterlina - GBP' }
      ];
    }
  }
};
