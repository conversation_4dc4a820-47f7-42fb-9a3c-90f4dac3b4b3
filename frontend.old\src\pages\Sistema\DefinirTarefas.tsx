import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { ClipboardList, Plus, Edit, Trash2, Calendar, User, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import ActionMenu, { ActionMenuItem } from '@/components/ui/ActionMenu';

interface Tarefa {
  id: number;
  titulo: string;
  descricao: string;
  responsavel: string;
  prioridade: 'baixa' | 'media' | 'alta';
  status: 'pendente' | 'em_andamento' | 'concluida';
  dataVencimento: string;
  dataCriacao: string;
}

const DefinirTarefas = () => {
  const [tarefas, setTarefas] = useState<Tarefa[]>([
    {
      id: 1,
      titulo: 'Backup Diário do Sistema',
      descricao: 'Realizar backup completo da base de dados',
      responsavel: 'João Manuel Silva',
      prioridade: 'alta',
      status: 'em_andamento',
      dataVencimento: '2025-06-17',
      dataCriacao: '2025-06-15'
    },
    {
      id: 2,
      titulo: 'Atualização de Segurança',
      descricao: 'Aplicar patches de segurança nos servidores',
      responsavel: 'Maria Fernanda Costa',
      prioridade: 'alta',
      status: 'pendente',
      dataVencimento: '2025-06-18',
      dataCriacao: '2025-06-16'
    },
    {
      id: 3,
      titulo: 'Relatório Mensal',
      descricao: 'Gerar relatório mensal de transações',
      responsavel: 'António José Pereira',
      prioridade: 'media',
      status: 'concluida',
      dataVencimento: '2025-06-15',
      dataCriacao: '2025-06-10'
    }
  ]);

  const [novaTarefa, setNovaTarefa] = useState({
    titulo: '',
    descricao: '',
    responsavel: '',
    prioridade: '',
    dataVencimento: ''
  });

  const [editandoTarefa, setEditandoTarefa] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { toast } = useToast();

  const responsaveis = [
    'João Manuel Silva',
    'Maria Fernanda Costa',
    'António José Pereira',
    'Ana Paula Santos',
    'Carlos Eduardo Lima'
  ];

  const prioridades = [
    { value: 'baixa', label: 'Baixa', color: 'bg-green-100 text-green-800' },
    { value: 'media', label: 'Média', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'alta', label: 'Alta', color: 'bg-red-100 text-red-800' }
  ];

  const statusOptions = [
    { value: 'pendente', label: 'Pendente', color: 'bg-gray-100 text-gray-800' },
    { value: 'em_andamento', label: 'Em Andamento', color: 'bg-blue-100 text-blue-800' },
    { value: 'concluida', label: 'Concluída', color: 'bg-green-100 text-green-800' }
  ];

  const getPrioridadeBadge = (prioridade: string) => {
    const prio = prioridades.find(p => p.value === prioridade);
    return (
      <Badge className={`${prio?.color} hover:${prio?.color}`}>
        {prio?.label}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const stat = statusOptions.find(s => s.value === status);
    return (
      <Badge className={`${stat?.color} hover:${stat?.color}`}>
        {stat?.label}
      </Badge>
    );
  };

  const handleAdicionarTarefa = () => {
    if (!novaTarefa.titulo || !novaTarefa.responsavel || !novaTarefa.prioridade || !novaTarefa.dataVencimento) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha todos os campos obrigatórios",
        variant: "destructive"
      });
      return;
    }

    const tarefa: Tarefa = {
      id: Date.now(),
      titulo: novaTarefa.titulo,
      descricao: novaTarefa.descricao,
      responsavel: novaTarefa.responsavel,
      prioridade: novaTarefa.prioridade as 'baixa' | 'media' | 'alta',
      status: 'pendente',
      dataVencimento: novaTarefa.dataVencimento,
      dataCriacao: new Date().toISOString().split('T')[0]
    };

    setTarefas([...tarefas, tarefa]);
    setNovaTarefa({
      titulo: '',
      descricao: '',
      responsavel: '',
      prioridade: '',
      dataVencimento: ''
    });
    setIsModalOpen(false);

    toast({
      title: "Tarefa criada",
      description: "A tarefa foi criada com sucesso",
    });
  };

  const handleEditarTarefa = (id: number) => {
    setEditandoTarefa(id);
    toast({
      title: "Editar tarefa",
      description: "Funcionalidade de edição será implementada",
    });
  };

  const handleExcluirTarefa = (id: number) => {
    setTarefas(tarefas.filter(t => t.id !== id));
    toast({
      title: "Tarefa excluída",
      description: "A tarefa foi excluída com sucesso",
      variant: "destructive"
    });
  };

  const handleAlterarStatus = (id: number, novoStatus: string) => {
    setTarefas(tarefas.map(t => 
      t.id === id ? { ...t, status: novoStatus as 'pendente' | 'em_andamento' | 'concluida' } : t
    ));
    
    toast({
      title: "Status atualizado",
      description: "O status da tarefa foi atualizado",
    });
  };

  return (
    <div className="space-y-6 content-container">
      {/* Title and Subtitle */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <ClipboardList className="h-8 w-8" />
            Definir Tarefas
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Gerenciar tarefas e responsabilidades do sistema</p>
        </div>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button size="lg" className="bg-twins-primary hover:bg-twins-secondary text-white shadow-lg">
              <Plus className="h-5 w-5 mr-2" />
              Definir Tarefa
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Nova Tarefa
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="titulo">
                  Título <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="titulo"
                  value={novaTarefa.titulo}
                  onChange={(e) => setNovaTarefa(prev => ({ ...prev, titulo: e.target.value }))}
                  placeholder="Digite o título da tarefa"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="descricao">Descrição</Label>
                <Textarea
                  id="descricao"
                  value={novaTarefa.descricao}
                  onChange={(e) => setNovaTarefa(prev => ({ ...prev, descricao: e.target.value }))}
                  placeholder="Descreva a tarefa..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="responsavel">
                  Responsável <span className="text-red-500">*</span>
                </Label>
                <Select value={novaTarefa.responsavel} onValueChange={(value) => setNovaTarefa(prev => ({ ...prev, responsavel: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o responsável" />
                  </SelectTrigger>
                  <SelectContent>
                    {responsaveis.map((responsavel) => (
                      <SelectItem key={responsavel} value={responsavel}>
                        {responsavel}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="prioridade">
                  Prioridade <span className="text-red-500">*</span>
                </Label>
                <Select value={novaTarefa.prioridade} onValueChange={(value) => setNovaTarefa(prev => ({ ...prev, prioridade: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a prioridade" />
                  </SelectTrigger>
                  <SelectContent>
                    {prioridades.map((prioridade) => (
                      <SelectItem key={prioridade.value} value={prioridade.value}>
                        {prioridade.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dataVencimento">
                  Data de Vencimento <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="dataVencimento"
                  type="date"
                  value={novaTarefa.dataVencimento}
                  onChange={(e) => setNovaTarefa(prev => ({ ...prev, dataVencimento: e.target.value }))}
                />
              </div>

              <Button onClick={handleAdicionarTarefa} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Tarefa
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-gray-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Tarefas Pendentes</CardTitle>
            <Clock className="h-5 w-5 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-700">
              {tarefas.filter(t => t.status === 'pendente').length}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Aguardando início
            </p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-600">Em Andamento</CardTitle>
            <AlertTriangle className="h-5 w-5 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {tarefas.filter(t => t.status === 'em_andamento').length}
            </div>
            <p className="text-xs text-blue-500 mt-1">
              Em execução
            </p>
          </CardContent>
        </Card>

        <Card className="border-green-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-600">Concluídas</CardTitle>
            <CheckCircle className="h-5 w-5 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {tarefas.filter(t => t.status === 'concluida').length}
            </div>
            <p className="text-xs text-green-500 mt-1">
              Finalizadas
            </p>
          </CardContent>
        </Card>

        <Card className="border-red-200 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-600">Alta Prioridade</CardTitle>
            <AlertTriangle className="h-5 w-5 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {tarefas.filter(t => t.prioridade === 'alta').length}
            </div>
            <p className="text-xs text-red-500 mt-1">
              Urgentes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Tarefas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ClipboardList className="h-5 w-5" />
            Lista de Tarefas ({tarefas.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="table-container rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Título</TableHead>
                  <TableHead>Responsável</TableHead>
                  <TableHead>Prioridade</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Vencimento</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tarefas.map((tarefa) => (
                  <TableRow key={tarefa.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{tarefa.titulo}</div>
                        {tarefa.descricao && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {tarefa.descricao}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {tarefa.responsavel}
                      </div>
                    </TableCell>
                    <TableCell>{getPrioridadeBadge(tarefa.prioridade)}</TableCell>
                    <TableCell>
                      <Select value={tarefa.status} onValueChange={(value) => handleAlterarStatus(tarefa.id, value)}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {statusOptions.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {new Date(tarefa.dataVencimento).toLocaleDateString('pt-AO')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <ActionMenu
                        items={[
                          {
                            label: 'Editar',
                            icon: Edit,
                            onClick: () => handleEditarTarefa(tarefa.id)
                          },
                          {
                            label: 'Excluir',
                            icon: Trash2,
                            onClick: () => handleExcluirTarefa(tarefa.id),
                            variant: 'destructive'
                          }
                        ]}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DefinirTarefas;
