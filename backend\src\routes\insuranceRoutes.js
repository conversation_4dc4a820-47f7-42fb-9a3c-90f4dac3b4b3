const express = require('express');
const { authorize } = require('../auth/middleware');

const router = express.Router();

// Placeholder routes for insurance management
// TODO: Implement full insurance functionality

/**
 * GET /api/insurance
 * Listar apólices de seguro
 */
router.get('/', authorize('admin', 'gerente', 'caixa'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Listagem de seguros - Em desenvolvimento',
    data: []
  });
});

/**
 * POST /api/insurance
 * Criar apólice de seguro
 */
router.post('/', authorize('admin', 'gerente', 'caixa'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Criação de apólice - Em desenvolvimento'
  });
});

module.exports = router;
