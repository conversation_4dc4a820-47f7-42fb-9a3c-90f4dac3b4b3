import { API_CONFIG, API_ENDPOINTS } from '@/config/api';
import { 
  Client, 
  ClientListResponse, 
  ClientFilters, 
  IndividualClientForm, 
  CompanyClientForm,
  ClientStats 
} from '@/types/client';

class ClientService {
  private getAuthHeaders() {
    const token = localStorage.getItem('twins-bank-token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  // Listar clientes com filtros e paginação
  async getClients(filters: ClientFilters): Promise<ClientListResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.LIST}?${queryParams}`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Erro ao carregar clientes: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  // Obter cliente por ID
  async getClientById(id: string): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.GET(id)}`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Erro ao carregar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Criar cliente individual
  async createIndividualClient(clientData: IndividualClientForm): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.CREATE_INDIVIDUAL}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(clientData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `Erro ao criar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Criar cliente empresa
  async createCompanyClient(clientData: CompanyClientForm): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.CREATE_COMPANY}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(clientData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `Erro ao criar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Atualizar cliente
  async updateClient(id: string, clientData: Partial<Client>): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.UPDATE(id)}`;
    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(clientData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `Erro ao atualizar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Excluir cliente
  async deleteClient(id: string): Promise<void> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.DELETE(id)}`;
    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `Erro ao excluir cliente: ${response.statusText}`);
    }
  }

  // Obter estatísticas de clientes
  async getClientStats(): Promise<ClientStats> {
    // Obter apenas clientes ativos para estatísticas (excluindo pendentes)
    const activeClients = await this.getClients({ page: 1, limit: 100, status: 'active' });

    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Calcular estatísticas baseadas apenas em clientes ativos
    const stats: ClientStats = {
      total_clients: activeClients.pagination.total_records, // Apenas clientes ativos
      active_clients: activeClients.clients.length, // Todos os retornados são ativos
      new_this_month: activeClients.clients.filter(c =>
        new Date(c.created_at) >= thisMonth
      ).length,
      individual_clients: activeClients.clients.filter(c => c.client_type === 'individual').length,
      company_clients: activeClients.clients.filter(c => c.client_type === 'company').length
    };

    return stats;
  }

  // Exportar clientes para CSV
  async exportClients(filters: ClientFilters): Promise<void> {
    // Fetch all clients by paginating through all pages
    let allClients: Client[] = [];
    let currentPage = 1;
    const pageSize = 100; // Maximum allowed by backend
    let hasMorePages = true;

    while (hasMorePages) {
      const response = await this.getClients({
        ...filters,
        page: currentPage,
        limit: pageSize
      });

      allClients = [...allClients, ...response.clients];

      // Check if there are more pages
      hasMorePages = response.pagination.has_next;
      currentPage++;
    }

    const headers = [
      'ID',
      'Tipo',
      'Nome/Empresa',
      'Documento',
      'Número Documento',
      'NIF',
      'Nacionalidade',
      'Profissão',
      'Rendimento Mensal',
      'Status',
      'Balcão',
      'Data Criação',
      'Telefone',
      'Email',
      'Endereço'
    ];

    const csvContent = [
      headers.join(','),
      ...allClients.map(client => [
        client.id,
        client.client_type === 'individual' ? 'Individual' : 'Empresa',
        `"${client.full_name || client.company_name || ''}"`,
        client.document_type,
        client.document_number,
        client.nif || '',
        `"${client.nationality}"`,
        `"${client.profession || ''}"`,
        client.monthly_income || '',
        client.status,
        `"${client.branch_name}"`,
        new Date(client.created_at).toLocaleDateString('pt-PT'),
        client.primary_contact?.phone || '',
        client.primary_contact?.email || '',
        client.primary_address ?
          `"${client.primary_address.street}, ${client.primary_address.municipality}, ${client.primary_address.province}"` : ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `clientes_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}

export const clientService = new ClientService();
