// E2E Tests for ATM CRUD Operations
// Tests: Create, Read, Update, Delete ATM functionality

const { test, expect } = require('@playwright/test');

test.describe('ATM CRUD Operations', () => {
  
  test.beforeEach(async ({ page }) => {
    // Login as admin before each test
    await page.goto('http://localhost:8080/login');
    await page.getByRole('textbox', { name: 'Email *' }).fill('<EMAIL>');
    await page.getByRole('textbox', { name: 'Senha *' }).fill('123456789');
    await page.getByRole('button', { name: 'Entrar' }).click();
    
    // Wait for login to complete
    await page.waitForURL('http://localhost:8080/');
    await page.waitForTimeout(2000);
  });

  test('Test 1: Navigate to ATM Management Page', async ({ page }) => {
    // Navigate to ATM management page
    await page.goto('http://localhost:8080/sistema/gestao-atms');
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Verify page title and content
    await expect(page.locator('h1')).toContainText('Gestão de ATMs');
    await expect(page.locator('text=Registar e gerir caixas automáticos do sistema')).toBeVisible();
    
    // Verify "Novo ATM" button is present
    await expect(page.getByRole('button', { name: 'Novo ATM' })).toBeVisible();
    
    // Verify ATMs table is present and shows existing ATMs
    await expect(page.locator('text=ATMs Registados')).toBeVisible();
    await expect(page.locator('table')).toBeVisible();
    
    console.log('✅ Test 1 PASSED: Successfully navigated to ATM Management page');
  });

  test('Test 2: Open Create ATM Modal', async ({ page }) => {
    // Navigate to ATM management page
    await page.goto('http://localhost:8080/sistema/gestao-atms');
    await page.waitForTimeout(3000);
    
    // Click "Novo ATM" button
    await page.getByRole('button', { name: 'Novo ATM' }).click();
    
    // Wait for modal to appear
    await page.waitForTimeout(1000);
    
    // Verify modal is open and contains expected fields
    await expect(page.locator('text=Registar Novo ATM')).toBeVisible();
    
    // Verify form fields are present
    await expect(page.getByLabel('Código do ATM *')).toBeVisible();
    await expect(page.getByLabel('Nome/Localização *')).toBeVisible();
    await expect(page.getByLabel('Agência *')).toBeVisible();
    await expect(page.getByLabel('Capacidade Máxima (AOA) *')).toBeVisible();
    
    // Verify buttons are present
    await expect(page.getByRole('button', { name: 'Cancelar' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Registar ATM' })).toBeVisible();
    
    console.log('✅ Test 2 PASSED: Successfully opened Create ATM modal');
  });

  test('Test 3: Create New ATM', async ({ page }) => {
    // Navigate to ATM management page
    await page.goto('http://localhost:8080/sistema/gestao-atms');
    await page.waitForTimeout(3000);
    
    // Click "Novo ATM" button
    await page.getByRole('button', { name: 'Novo ATM' }).click();
    await page.waitForTimeout(1000);
    
    // Fill form fields
    await page.getByLabel('Código do ATM *').fill('ATM999');
    await page.getByLabel('Nome/Localização *').fill('ATM Teste E2E - Shopping Center');
    
    // Select branch from dropdown
    await page.getByLabel('Agência *').click();
    await page.waitForTimeout(500);
    // Select the first available branch
    await page.locator('[role="option"]').first().click();
    
    await page.getByLabel('Capacidade Máxima (AOA) *').fill('50000000');
    
    // Submit form
    await page.getByRole('button', { name: 'Registar ATM' }).click();
    
    // Wait for success message or page update
    await page.waitForTimeout(3000);
    
    // Verify ATM was created by checking if it appears in the table
    await expect(page.locator('text=ATM999')).toBeVisible();
    await expect(page.locator('text=ATM Teste E2E - Shopping Center')).toBeVisible();
    
    console.log('✅ Test 3 PASSED: Successfully created new ATM');
  });

  test('Test 4: View ATM Details', async ({ page }) => {
    // Navigate to ATM management page
    await page.goto('http://localhost:8080/sistema/gestao-atms');
    await page.waitForTimeout(3000);
    
    // Find the first ATM row and click the view button (eye icon)
    const firstViewButton = page.locator('table tbody tr').first().locator('button').first();
    await firstViewButton.click();
    
    // Wait for details modal/page to load
    await page.waitForTimeout(2000);
    
    // Verify ATM details are displayed
    // This could be a modal or a separate page depending on implementation
    const hasModal = await page.locator('[role="dialog"]').isVisible();
    const hasDetailsPage = await page.locator('text=Detalhes do ATM').isVisible();
    
    expect(hasModal || hasDetailsPage).toBeTruthy();
    
    console.log('✅ Test 4 PASSED: Successfully viewed ATM details');
  });

  test('Test 5: Edit ATM', async ({ page }) => {
    // Navigate to ATM management page
    await page.goto('http://localhost:8080/sistema/gestao-atms');
    await page.waitForTimeout(3000);
    
    // Find the first ATM row and click the edit button (pencil icon)
    const firstEditButton = page.locator('table tbody tr').first().locator('button').nth(1);
    await firstEditButton.click();
    
    // Wait for edit modal to appear
    await page.waitForTimeout(1000);
    
    // Verify edit modal is open
    await expect(page.locator('text=Editar ATM')).toBeVisible();
    
    // Modify the location field
    const locationField = page.getByLabel('Nome/Localização *');
    await locationField.clear();
    await locationField.fill('ATM Principal - Agência Central (Editado)');
    
    // Submit changes
    await page.getByRole('button', { name: 'Salvar Alterações' }).click();
    
    // Wait for update to complete
    await page.waitForTimeout(3000);
    
    // Verify the change was applied
    await expect(page.locator('text=ATM Principal - Agência Central (Editado)')).toBeVisible();
    
    console.log('✅ Test 5 PASSED: Successfully edited ATM');
  });

  test('Test 6: Delete ATM', async ({ page }) => {
    // Navigate to ATM management page
    await page.goto('http://localhost:8080/sistema/gestao-atms');
    await page.waitForTimeout(3000);
    
    // Count initial number of ATMs
    const initialCount = await page.locator('table tbody tr').count();
    
    // Find the last ATM row (likely our test ATM) and click delete button
    const lastRow = page.locator('table tbody tr').last();
    const deleteButton = lastRow.locator('button').last();
    
    // Get the ATM code for verification
    const atmCode = await lastRow.locator('td').first().textContent();
    
    await deleteButton.click();
    
    // Wait for confirmation dialog
    await page.waitForTimeout(1000);
    
    // Confirm deletion
    await page.getByRole('button', { name: 'Confirmar' }).click();
    
    // Wait for deletion to complete
    await page.waitForTimeout(3000);
    
    // Verify ATM was deleted
    const finalCount = await page.locator('table tbody tr').count();
    expect(finalCount).toBe(initialCount - 1);
    
    // Verify the specific ATM is no longer in the table
    await expect(page.locator(`text=${atmCode}`)).not.toBeVisible();
    
    console.log('✅ Test 6 PASSED: Successfully deleted ATM');
  });

});
