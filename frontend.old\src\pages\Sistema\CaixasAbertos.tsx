import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FolderOpen, AlertTriangle, RefreshCw, Clock, User, Wallet, DollarSign, XCircle, CheckCircle } from 'lucide-react';
import { cashRegisterSessionService, CashRegisterSession } from '@/services/cashRegisterSessionService';
import { toast } from 'sonner';

const CaixasAbertos = () => {
  const [sessions, setSessions] = useState<CashRegisterSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSession, setSelectedSession] = useState<CashRegisterSession | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isForceClosing, setIsForceClosing] = useState(false);

  useEffect(() => {
    loadActiveSessions();
    // Atualizar a cada 30 segundos
    const interval = setInterval(loadActiveSessions, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadActiveSessions = async () => {
    try {
      setLoading(true);
      const data = await cashRegisterSessionService.getSessions({ status: 'open' });
      setSessions(data.sessions);
    } catch (error: any) {
      console.error('Erro ao carregar sessões ativas:', error);
      toast.error('Erro ao carregar sessões ativas');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value).replace('AOA', 'Kz');
  };

  const formatDuration = (openedAt: string) => {
    const start = new Date(openedAt);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    }
    return `${diffMinutes}m`;
  };

  const openSessionDetails = (session: CashRegisterSession) => {
    setSelectedSession(session);
    setIsDetailModalOpen(true);
  };

  const handleForceClose = async (sessionId: string) => {
    if (!confirm('Tem certeza que deseja forçar o fechamento desta sessão? Esta ação não pode ser desfeita.')) {
      return;
    }

    try {
      setIsForceClosing(true);
      // Aqui você implementaria a chamada para forçar fechamento
      // await cashRegisterService.forceCloseSession(sessionId);
      toast.success('Sessão fechada com sucesso');
      setIsDetailModalOpen(false);
      loadActiveSessions();
    } catch (error: any) {
      console.error('Erro ao forçar fechamento:', error);
      toast.error('Erro ao forçar fechamento da sessão');
    } finally {
      setIsForceClosing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando sessões ativas...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <FolderOpen className="h-8 w-8" />
            Caixas Abertos
          </h1>
          <p className="text-gray-600">Monitoramento de caixas em operação</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadActiveSessions} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Caixas Ativos</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessions.length}</div>
            <p className="text-xs text-muted-foreground">Sessões abertas</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(sessions.reduce((total, session) => total + session.current_balance, 0))}
            </div>
            <p className="text-xs text-muted-foreground">Em todos os caixas</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tempo Médio</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sessions.length > 0 ?
                Math.round(sessions.reduce((total, session) => {
                  const diffMs = new Date().getTime() - new Date(session.opened_at).getTime();
                  return total + (diffMs / (1000 * 60));
                }, 0) / sessions.length) + 'm' : '0m'
              }
            </div>
            <p className="text-xs text-muted-foreground">De operação</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Operadores</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(sessions.map(s => s.user_id)).size}
            </div>
            <p className="text-xs text-muted-foreground">Únicos ativos</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Sessões */}
      {sessions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FolderOpen className="h-24 w-24 mx-auto text-gray-300 mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">
              Nenhum Caixa Aberto
            </h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              Não há sessões de caixa ativas no momento. Os operadores podem abrir suas sessões através da página de abertura de caixa.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {sessions.map((session) => (
            <Card key={session.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{session.cash_register_number}</CardTitle>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Ativo
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{session.branch_name}</p>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{session.user_name}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Wallet className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">{formatCurrency(session.current_balance)}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{formatDuration(session.opened_at)}</span>
                </div>

                <div className="pt-2 border-t">
                  <Button
                    onClick={() => openSessionDetails(session)}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    Ver Detalhes
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Modal de Detalhes */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Detalhes da Sessão - {selectedSession?.cash_register_number}
            </DialogTitle>
          </DialogHeader>

          {selectedSession && (
            <div className="space-y-6">
              {/* Informações Básicas */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Informações da Sessão</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Caixa:</strong> {selectedSession.cash_register_number}</div>
                    <div><strong>Agência:</strong> {selectedSession.branch_name}</div>
                    <div><strong>Operador:</strong> {selectedSession.user_name}</div>
                    <div><strong>Status:</strong>
                      <Badge className="ml-2 bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Ativo
                      </Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Informações Financeiras</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Saldo Inicial:</strong> {formatCurrency(selectedSession.opening_balance)}</div>
                    <div><strong>Saldo Atual:</strong> {formatCurrency(selectedSession.current_balance)}</div>
                    <div><strong>Diferença:</strong>
                      <span className={`ml-1 font-medium ${
                        (selectedSession.current_balance - selectedSession.opening_balance) >= 0
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {(selectedSession.current_balance - selectedSession.opening_balance) >= 0 ? '+' : ''}
                        {formatCurrency(selectedSession.current_balance - selectedSession.opening_balance)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Informações de Tempo */}
              <div>
                <h4 className="font-semibold mb-2">Informações de Tempo</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div><strong>Aberto em:</strong> {new Date(selectedSession.opened_at).toLocaleString('pt-AO')}</div>
                  <div><strong>Tempo Ativo:</strong> {formatDuration(selectedSession.opened_at)}</div>
                </div>
              </div>

              {/* Observações */}
              {selectedSession.notes && (
                <div>
                  <h4 className="font-semibold mb-2">Observações</h4>
                  <p className="text-sm bg-gray-50 p-3 rounded">{selectedSession.notes}</p>
                </div>
              )}

              {/* Ações */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button variant="outline" onClick={() => setIsDetailModalOpen(false)}>
                  Fechar
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleForceClose(selectedSession.id)}
                  disabled={isForceClosing}
                >
                  {isForceClosing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Fechando...
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Forçar Fechamento
                    </>
                  )}
                </Button>
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Atenção:</strong> O fechamento forçado deve ser usado apenas em situações excepcionais.
                  Esta ação registrará o fechamento com o saldo atual calculado automaticamente.
                </AlertDescription>
              </Alert>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CaixasAbertos;
