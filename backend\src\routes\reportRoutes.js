const express = require('express');
const { authorize } = require('../auth/middleware');

const router = express.Router();

// Placeholder routes for reports
// TODO: Implement full reporting functionality

/**
 * GET /api/reports/dashboard
 * Dados do dashboard
 */
router.get('/dashboard', authorize('admin', 'gerente'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Dados do dashboard - Em desenvolvimento',
    data: {
      total_clients: 0,
      total_accounts: 0,
      total_transactions: 0,
      total_balance: 0
    }
  });
});

/**
 * GET /api/reports/transactions
 * Relatório de transações
 */
router.get('/transactions', authorize('admin', 'gerente'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Relatório de transações - Em desenvolvimento',
    data: []
  });
});

module.exports = router;
