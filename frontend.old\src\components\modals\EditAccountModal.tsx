import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { accountService, Account } from '@/services/accountService';

interface EditAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  account: Account | null;
  onAccountUpdated: () => void;
}

interface EditAccountForm {
  account_type: string;
  status: string;
  overdraft_limit: number;
}

interface FormErrors {
  account_type?: string;
  status?: string;
  overdraft_limit?: string;
}

const EditAccountModal: React.FC<EditAccountModalProps> = ({
  isOpen,
  onClose,
  account,
  onAccountUpdated,
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<EditAccountForm>({
    account_type: '',
    status: '',
    overdraft_limit: 0,
  });
  const [errors, setErrors] = useState<FormErrors>({});

  // Preencher formulário quando a conta for selecionada
  useEffect(() => {
    if (account) {
      setFormData({
        account_type: account.account_type,
        status: account.status,
        overdraft_limit: parseFloat(account.overdraft_limit) || 0,
      });
      setErrors({});
    }
  }, [account]);

  const handleInputChange = (field: keyof EditAccountForm, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.account_type) {
      newErrors.account_type = 'Tipo de conta é obrigatório';
    }

    if (!formData.status) {
      newErrors.status = 'Status é obrigatório';
    }

    if (formData.overdraft_limit < 0) {
      newErrors.overdraft_limit = 'Limite de descoberto não pode ser negativo';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!account || !validateForm()) {
      return;
    }

    setLoading(true);

    try {
      await accountService.updateAccount(account.id, {
        account_type: formData.account_type,
        status: formData.status,
        overdraft_limit: formData.overdraft_limit,
      });

      toast({
        title: "Conta atualizada com sucesso",
        description: `Conta ${account.account_number} foi atualizada.`,
      });

      onAccountUpdated();
      onClose();
    } catch (error: any) {
      console.error('Erro ao atualizar conta:', error);
      toast({
        title: "Erro ao atualizar conta",
        description: error.message || "Ocorreu um erro inesperado",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        account_type: '',
        status: '',
        overdraft_limit: 0,
      });
      setErrors({});
      onClose();
    }
  };

  // Função para formatar tipo de conta
  const formatAccountType = (type: string) => {
    const types: Record<string, string> = {
      'corrente': 'Conta Particular/Singular',
      'salario': 'Conta Salário',
      'junior': 'Conta Júnior (2 titular)'
    };
    return types[type] || type;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            Editar Conta: {account?.account_number}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Tipo de Conta */}
          <div className="space-y-2">
            <Label htmlFor="account-type">
              Tipo de Conta <span className="text-red-500">*</span>
            </Label>
            <Select 
              value={formData.account_type} 
              onValueChange={(value) => handleInputChange('account_type', value)}
            >
              <SelectTrigger className={errors.account_type ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="corrente">Conta Particular/Singular</SelectItem>
                <SelectItem value="salario">Conta Salário</SelectItem>
                <SelectItem value="junior">Conta Júnior (2 titular)</SelectItem>
              </SelectContent>
            </Select>
            {errors.account_type && (
              <Alert variant="destructive" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  {errors.account_type}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">
              Status <span className="text-red-500">*</span>
            </Label>
            <Select 
              value={formData.status} 
              onValueChange={(value) => handleInputChange('status', value)}
            >
              <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione o status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Ativo</SelectItem>
                <SelectItem value="blocked">Bloqueado</SelectItem>
                <SelectItem value="closed">Fechado</SelectItem>
              </SelectContent>
            </Select>
            {errors.status && (
              <Alert variant="destructive" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  {errors.status}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Limite de Descoberto */}
          <div className="space-y-2">
            <Label htmlFor="overdraft-limit">
              Limite de Descoberto (AOA)
            </Label>
            <Input
              id="overdraft-limit"
              type="number"
              min="0"
              step="0.01"
              value={formData.overdraft_limit}
              onChange={(e) => handleInputChange('overdraft_limit', parseFloat(e.target.value) || 0)}
              className={errors.overdraft_limit ? 'border-red-500' : ''}
              placeholder="0.00"
            />
            {errors.overdraft_limit && (
              <Alert variant="destructive" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  {errors.overdraft_limit}
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={loading}>
              Cancelar
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Salvando...' : 'Salvar Alterações'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditAccountModal;
