import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { User, MapPin, Phone, FileText } from 'lucide-react';
import { Client } from '@/types/client';
import { clientService } from '@/services/clientService';

interface ClientEditModalProps {
  client: Client | null;
  isOpen: boolean;
  onClose: () => void;
  onClientUpdated: () => void;
}

const ClientEditModal: React.FC<ClientEditModalProps> = ({
  client,
  isOpen,
  onClose,
  onClientUpdated,
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Dados básicos
    full_name: '',
    company_name: '',
    document_type: '',
    document_number: '',
    nif: '',
    birth_date: '',
    nationality: '',
    gender: '',
    marital_status: '',
    profession: '',
    monthly_income: '',
    status: 'active' as 'active' | 'inactive' | 'blocked',

    // Endereço
    province: '',
    municipality: '',
    neighborhood: '',
    street: '',
    house_number: '',
    postal_code: '',

    // Contactos
    phone_personal: '',
    email_personal: '',
  });

  useEffect(() => {
    if (client) {
      // Carregar dados completos do cliente
      const loadClientData = async () => {
        try {
          const fullClientData = await clientService.getClientById(client.id);

          setFormData({
            // Dados básicos
            full_name: fullClientData.full_name || '',
            company_name: fullClientData.company_name || '',
            document_type: fullClientData.document_type || '',
            document_number: fullClientData.document_number || '',
            nif: fullClientData.nif || '',
            birth_date: fullClientData.birth_date ? fullClientData.birth_date.split('T')[0] : '',
            nationality: fullClientData.nationality || '',
            gender: fullClientData.gender || '',
            marital_status: fullClientData.marital_status || '',
            profession: fullClientData.profession || '',
            monthly_income: fullClientData.monthly_income?.toString() || '',
            status: fullClientData.status,

            // Endereço (primeiro endereço primário)
            province: fullClientData.addresses?.[0]?.province || '',
            municipality: fullClientData.addresses?.[0]?.municipality || '',
            neighborhood: fullClientData.addresses?.[0]?.neighborhood || '',
            street: fullClientData.addresses?.[0]?.street || '',
            house_number: fullClientData.addresses?.[0]?.house_number || '',
            postal_code: fullClientData.addresses?.[0]?.postal_code || '',

            // Contactos (primeiro contacto de cada tipo)
            phone_personal: fullClientData.contacts?.find(c => c.contact_type === 'phone_personal')?.contact_value || '',
            email_personal: fullClientData.contacts?.find(c => c.contact_type === 'email_personal')?.contact_value || '',
          });
        } catch (error) {
          console.error('Erro ao carregar dados do cliente:', error);
          // Fallback para dados básicos
          setFormData({
            full_name: client.full_name || '',
            company_name: client.company_name || '',
            document_type: '',
            document_number: '',
            nif: client.nif || '',
            birth_date: '',
            nationality: '',
            gender: '',
            marital_status: '',
            profession: client.profession || '',
            monthly_income: client.monthly_income?.toString() || '',
            status: client.status,
            province: '',
            municipality: '',
            neighborhood: '',
            street: '',
            house_number: '',
            postal_code: '',
            phone_personal: '',
            email_personal: '',
          });
        }
      };

      loadClientData();
    }
  }, [client]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!client) return;

    setLoading(true);
    try {
      // Preparar dados básicos do cliente
      const clientData = {
        full_name: formData.full_name || undefined,
        company_name: formData.company_name || undefined,
        document_type: formData.document_type || undefined,
        document_number: formData.document_number || undefined,
        nif: formData.nif || undefined,
        birth_date: formData.birth_date || undefined,
        nationality: formData.nationality || undefined,
        gender: formData.gender || undefined,
        marital_status: formData.marital_status || undefined,
        profession: formData.profession || undefined,
        monthly_income: formData.monthly_income ? parseFloat(formData.monthly_income) : undefined,
        status: formData.status,
      };

      // Preparar dados de endereço
      const addressData = {
        province: formData.province || undefined,
        municipality: formData.municipality || undefined,
        neighborhood: formData.neighborhood || undefined,
        street: formData.street || undefined,
        house_number: formData.house_number || undefined,
        postal_code: formData.postal_code || undefined,
      };

      // Preparar dados de contacto
      const contactData = {
        phone_personal: formData.phone_personal || undefined,
        email_personal: formData.email_personal || undefined,
      };

      // Remove campos vazios
      Object.keys(clientData).forEach(key => {
        if (clientData[key as keyof typeof clientData] === '' || clientData[key as keyof typeof clientData] === undefined) {
          delete clientData[key as keyof typeof clientData];
        }
      });

      // Atualizar cliente (apenas dados básicos por enquanto)
      await clientService.updateClient(client.id, clientData);

      toast({
        title: "Cliente atualizado",
        description: "Todas as informações do cliente foram atualizadas com sucesso.",
      });

      onClientUpdated();
      onClose();
    } catch (error) {
      toast({
        title: "Erro ao atualizar cliente",
        description: error instanceof Error ? error.message : "Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };



  if (!client) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Editar Cliente: {client.full_name || client.company_name}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="personal" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="personal" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Pessoais
              </TabsTrigger>
              <TabsTrigger value="documents" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Documentos
              </TabsTrigger>
              <TabsTrigger value="address" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Endereço
              </TabsTrigger>
              <TabsTrigger value="contacts" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Contactos
              </TabsTrigger>
            </TabsList>

            {/* Aba Dados Pessoais */}
            <TabsContent value="personal" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {client.client_type === 'individual' ? (
                  <div className="md:col-span-2">
                    <Label htmlFor="full_name">Nome Completo</Label>
                    <Input
                      id="full_name"
                      value={formData.full_name}
                      onChange={(e) => handleInputChange('full_name', e.target.value)}
                      placeholder="Nome completo do cliente"
                    />
                  </div>
                ) : (
                  <div className="md:col-span-2">
                    <Label htmlFor="company_name">Nome da Empresa</Label>
                    <Input
                      id="company_name"
                      value={formData.company_name}
                      onChange={(e) => handleInputChange('company_name', e.target.value)}
                      placeholder="Nome da empresa"
                    />
                  </div>
                )}

                {client.client_type === 'individual' && (
                  <>
                    <div>
                      <Label htmlFor="birth_date">Data de Nascimento</Label>
                      <Input
                        id="birth_date"
                        type="date"
                        value={formData.birth_date}
                        onChange={(e) => handleInputChange('birth_date', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="nationality">Nacionalidade</Label>
                      <Input
                        id="nationality"
                        value={formData.nationality}
                        onChange={(e) => handleInputChange('nationality', e.target.value)}
                        placeholder="Nacionalidade"
                      />
                    </div>

                    <div>
                      <Label htmlFor="gender">Género</Label>
                      <Select
                        value={formData.gender}
                        onValueChange={(value) => handleInputChange('gender', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o género" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="M">Masculino</SelectItem>
                          <SelectItem value="F">Feminino</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="marital_status">Estado Civil</Label>
                      <Select
                        value={formData.marital_status}
                        onValueChange={(value) => handleInputChange('marital_status', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o estado civil" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Solteiro">Solteiro</SelectItem>
                          <SelectItem value="Casado">Casado</SelectItem>
                          <SelectItem value="Divorciado">Divorciado</SelectItem>
                          <SelectItem value="Viúvo">Viúvo</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                <div>
                  <Label htmlFor="profession">Profissão/Atividade</Label>
                  <Input
                    id="profession"
                    value={formData.profession}
                    onChange={(e) => handleInputChange('profession', e.target.value)}
                    placeholder="Profissão ou atividade"
                  />
                </div>

                <div>
                  <Label htmlFor="monthly_income">Rendimento Mensal (AOA)</Label>
                  <Input
                    id="monthly_income"
                    type="number"
                    step="0.01"
                    value={formData.monthly_income}
                    onChange={(e) => handleInputChange('monthly_income', e.target.value)}
                    placeholder="0.00"
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleInputChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Ativo</SelectItem>
                      <SelectItem value="inactive">Inativo</SelectItem>
                      <SelectItem value="blocked">Bloqueado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Aba Documentos */}
            <TabsContent value="documents" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="document_type">Tipo de Documento</Label>
                  <Select
                    value={formData.document_type}
                    onValueChange={(value) => handleInputChange('document_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="BI">Bilhete de Identidade</SelectItem>
                      <SelectItem value="Passaporte">Passaporte</SelectItem>
                      <SelectItem value="Carta_Conducao">Carta de Condução</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="document_number">Número do Documento</Label>
                  <Input
                    id="document_number"
                    value={formData.document_number}
                    onChange={(e) => handleInputChange('document_number', e.target.value)}
                    placeholder="Número do documento"
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="nif">NIF (Número de Identificação Fiscal)</Label>
                  <Input
                    id="nif"
                    value={formData.nif}
                    onChange={(e) => handleInputChange('nif', e.target.value)}
                    placeholder="Número de Identificação Fiscal"
                  />
                </div>
              </div>
            </TabsContent>

            {/* Aba Endereço */}
            <TabsContent value="address" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="province">Província</Label>
                  <Input
                    id="province"
                    value={formData.province}
                    onChange={(e) => handleInputChange('province', e.target.value)}
                    placeholder="Província"
                  />
                </div>

                <div>
                  <Label htmlFor="municipality">Município</Label>
                  <Input
                    id="municipality"
                    value={formData.municipality}
                    onChange={(e) => handleInputChange('municipality', e.target.value)}
                    placeholder="Município"
                  />
                </div>

                <div>
                  <Label htmlFor="neighborhood">Bairro</Label>
                  <Input
                    id="neighborhood"
                    value={formData.neighborhood}
                    onChange={(e) => handleInputChange('neighborhood', e.target.value)}
                    placeholder="Bairro"
                  />
                </div>

                <div>
                  <Label htmlFor="street">Rua</Label>
                  <Input
                    id="street"
                    value={formData.street}
                    onChange={(e) => handleInputChange('street', e.target.value)}
                    placeholder="Nome da rua"
                  />
                </div>

                <div>
                  <Label htmlFor="house_number">Número da Casa</Label>
                  <Input
                    id="house_number"
                    value={formData.house_number}
                    onChange={(e) => handleInputChange('house_number', e.target.value)}
                    placeholder="Número da casa"
                  />
                </div>

                <div>
                  <Label htmlFor="postal_code">Código Postal</Label>
                  <Input
                    id="postal_code"
                    value={formData.postal_code}
                    onChange={(e) => handleInputChange('postal_code', e.target.value)}
                    placeholder="Código postal"
                  />
                </div>
              </div>
            </TabsContent>

            {/* Aba Contactos */}
            <TabsContent value="contacts" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone_personal">Telefone Pessoal</Label>
                  <Input
                    id="phone_personal"
                    value={formData.phone_personal}
                    onChange={(e) => handleInputChange('phone_personal', e.target.value)}
                    placeholder="+244 9XX XXX XXX"
                  />
                </div>

                <div>
                  <Label htmlFor="email_personal">Email Pessoal</Label>
                  <Input
                    id="email_personal"
                    type="email"
                    value={formData.email_personal}
                    onChange={(e) => handleInputChange('email_personal', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Informação:</strong> Atualmente, apenas os dados pessoais básicos podem ser atualizados.
              As funcionalidades de edição de documentos, endereços e contactos serão implementadas em versões futuras.
            </p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              Cancelar
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Salvando...' : 'Salvar Alterações'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ClientEditModal;
