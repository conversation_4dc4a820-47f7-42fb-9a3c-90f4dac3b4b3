import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole | UserRole[];
  requiredPermission?: {
    module: string;
    action: string;
  };
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles,
  requiredPermission,
  fallback
}) => {
  const { isAuthenticated, isLoading, user, hasRole, hasPermission } = useAuth();
  const location = useLocation();

  // Mostrar loading enquanto verifica autenticação
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-twins-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Verificando autenticação...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Redirecionar para login se não autenticado
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Verificar roles se especificados
  if (requiredRoles && !hasRole(requiredRoles)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <Shield className="h-4 w-4" />
              <AlertDescription className="mt-2">
                <div className="space-y-2">
                  <p className="font-medium">Acesso Negado</p>
                  <p className="text-sm">
                    Você não possui permissão para acessar esta página.
                  </p>
                  <p className="text-xs text-gray-500">
                    Perfil atual: <span className="font-medium">{user?.perfil}</span>
                  </p>
                  <p className="text-xs text-gray-500">
                    Perfis necessários: <span className="font-medium">
                      {Array.isArray(requiredRoles) ? requiredRoles.join(', ') : requiredRoles}
                    </span>
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Verificar permissões específicas se especificadas
  if (requiredPermission && !hasPermission(requiredPermission.module, requiredPermission.action)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="mt-2">
                <div className="space-y-2">
                  <p className="font-medium">Permissão Insuficiente</p>
                  <p className="text-sm">
                    Você não possui a permissão necessária para esta operação.
                  </p>
                  <p className="text-xs text-gray-500">
                    Módulo: <span className="font-medium">{requiredPermission.module}</span>
                  </p>
                  <p className="text-xs text-gray-500">
                    Ação: <span className="font-medium">{requiredPermission.action}</span>
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Se passou por todas as verificações, renderizar o conteúdo
  return <>{children}</>;
};

export default ProtectedRoute;
