[ ] NAME:Sistema K-Bank - Refatoração Completa DESCRIPTION:Projeto completo de refatoração do sistema bancário K-Bank com implementação de funcionalidades de caixa, fluxo de numerário e melhorias de UX
-[ ] NAME:Implementação do Fluxo de Numerário Completo DESCRIPTION:Desenvolver o fluxo de numerário da agência seguindo a lógica: Cofre Principal → Tesoureiro → Caixas/Balcões/ATMs → Entrega ao Tesoureiro → Entrega ao Cofre.
--[x] NAME:Implementar lógica de filtro por agência na Entrega ao Caixa DESCRIPTION:Garantir que apenas Admin e Gerente visualizem todos os caixas no dropdown, enquanto o Tesoureiro visualiza apenas os caixas da agência em que está registado.
--[ ] NAME:Implementar backend para Entrega ao Cofre DESCRIPTION:Desenvolver o backend para a funcionalidade 'Entrega ao Cofre' seguindo a lógica do fluxo de numerário. Pode necessitar de alterações no layout do frontend.
--[ ] NAME:Implementar backend e melhorar frontend da Entrega ao Tesoureiro DESCRIPTION:Implementar backend para 'Entrega ao Tesoureiro' e melhorar frontend: adicionar coluna de agência/balcão no datatable, campo automático de balcão no modal após seleção do tesoureiro.
--[ ] NAME:Implementar frontend e backend para Entrega ao Balcão DESCRIPTION:Desenvolver completamente a funcionalidade 'Entrega ao Balcão' tanto no frontend quanto no backend, seguindo o fluxo de numerário estabelecido.
--[ ] NAME:Conectar Carregamento do ATM com backend DESCRIPTION:Implementar a conexão entre o frontend de 'Carregamento do ATM' e o backend, desenvolvendo os endpoints necessários para gestão de ATMs.
--[ ] NAME:Criar tabela e modelo de dados para Cofre Principal DESCRIPTION:Implementar estrutura de base de dados para o Cofre Principal, incluindo tabelas para saldos, movimentações e histórico de operações.
-[/] NAME:Criação da Funcionalidade de Saldo Inicial DESCRIPTION:Criar funcionalidade na área de administração que permita a um utilizador com perfil 'Administrador' definir um saldo inicial para a simulação no 'Cofre Principal' da agência.
--[x] NAME:Criar interface de administração para Saldo Inicial DESCRIPTION:Desenvolver interface no frontend para que administradores possam definir o saldo inicial do sistema em Kwanzas (Kz), com validações e confirmação.
--[x] NAME:Implementar backend para Saldo Inicial do Cofre DESCRIPTION:Criar endpoints no backend para permitir que administradores definam o saldo inicial do 'Cofre Principal', incluindo validações de segurança e registro de auditoria.
--[x] NAME:Integrar Saldo Inicial com sistema de auditoria DESCRIPTION:Garantir que todas as operações de definição de saldo inicial sejam registadas no sistema de auditoria com detalhes do utilizador, data/hora e valores.
-[ ] NAME:Testes de integração com Playwright DESCRIPTION:Executar testes completos do sistema usando Playwright para validar todas as funcionalidades implementadas: abertura de caixa, operações, gestão, fluxo de numerário e saldo inicial. Testar com diferentes perfis de utilizador (Admin, Gerente, Tesoureiro, Caixa).
-[/] NAME:Verificar e corrigir sistema de auditoria e logs DESCRIPTION:Investigar e corrigir problemas no sistema de auditoria onde operações como 'forçar fecho de caixa' e outras operações administrativas não estão a ser registadas correctamente nos audit_logs e security_logs.
-[ ] NAME:Integrar Saldo Inicial com sistema de auditoria DESCRIPTION:Garantir que todas as operações de definição de saldo inicial sejam registadas no sistema de auditoria com detalhes do utilizador, data/hora e valores.
-[ ] NAME:Implementar backend para Entrega ao Cofre DESCRIPTION:Desenvolver o backend para a funcionalidade 'Entrega ao Cofre' seguindo a lógica do fluxo de numerário. Pode necessitar de alterações no layout do frontend.