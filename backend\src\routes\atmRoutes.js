const express = require('express');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');
const AppError = require('../utils/appError');
const { catchAsync } = require('../utils/catchAsync');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const logger = require('../utils/logger');
const { getCurrentTimestamp } = require('../utils/dateUtils');

const router = express.Router();

// Schema de validação para carregamento de ATM
const loadATMSchema = Joi.object({
  atm_id: Joi.number().integer().positive().required(),
  amount: Joi.number().positive().max(100000000).required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().max(500).allow('', null)
});

// Schema de validação para criar ATM
const createATMSchema = Joi.object({
  atm_code: Joi.string().min(3).max(50).required(),
  location: Joi.string().min(3).max(200).required(),
  branch_id: Joi.number().integer().positive().required(),
  cash_capacity: Joi.number().positive().max(1000000000).required(),
  status: Joi.string().valid('online', 'offline', 'maintenance').default('offline')
});

// Schema de validação para atualizar ATM
const updateATMSchema = Joi.object({
  atm_code: Joi.string().min(3).max(50),
  location: Joi.string().min(3).max(200),
  branch_id: Joi.number().integer().positive(),
  cash_capacity: Joi.number().positive().max(1000000000),
  status: Joi.string().valid('online', 'offline', 'maintenance')
}).min(1);

// Schema de validação para atualizar status
const updateStatusSchema = Joi.object({
  status: Joi.string().valid('online', 'offline', 'maintenance').required()
});

/**
 * GET /api/atm
 * Listar ATMs com informações detalhadas
 */
router.get('/', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  const atms = await executeQuery(`
    SELECT
      a.id,
      a.atm_code,
      a.location,
      a.branch_id,
      a.cash_capacity,
      a.current_balance,
      a.status,
      a.last_maintenance,
      a.installed_date,
      b.name as branch_name,
      ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
    FROM atms a
    LEFT JOIN branches b ON a.branch_id = b.id
    ORDER BY a.atm_code
  `);

  res.status(200).json({
    status: 'success',
    data: {
      atms,
      total: atms.length
    }
  });
}));

/**
 * GET /api/atm/:id
 * Obter detalhes de um ATM específico
 */
router.get('/:id', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const atm = await executeQuery(`
    SELECT
      a.id,
      a.atm_code,
      a.location,
      a.branch_id,
      a.cash_capacity,
      a.current_balance,
      a.status,
      a.last_maintenance,
      a.installed_date,
      b.name as branch_name,
      ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
    FROM atms a
    LEFT JOIN branches b ON a.branch_id = b.id
    WHERE a.id = ?
  `, [id]);

  if (!atm.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  res.status(200).json({
    status: 'success',
    data: {
      atm: atm[0]
    }
  });
}));

/**
 * GET /api/atm/:id/loadings
 * Obter histórico de carregamentos de um ATM
 */
router.get('/:id/loadings', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { limit = 10, offset = 0 } = req.query;

  // Verificar se ATM existe
  const atmExists = await executeQuery('SELECT id FROM atms WHERE id = ?', [id]);
  if (!atmExists.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  const loadings = await executeQuery(`
    SELECT
      al.id,
      al.loaded_amount,
      al.loading_date,
      al.notes,
      u.full_name as loaded_by_name,
      a.atm_code
    FROM atm_loadings al
    JOIN users u ON al.loaded_by = u.id
    JOIN atms a ON al.atm_id = a.id
    WHERE al.atm_id = ?
    ORDER BY al.loading_date DESC
    LIMIT ? OFFSET ?
  `, [id, parseInt(limit), parseInt(offset)]);

  const totalCount = await executeQuery(
    'SELECT COUNT(*) as total FROM atm_loadings WHERE atm_id = ?',
    [id]
  );

  res.status(200).json({
    status: 'success',
    data: {
      loadings,
      total: totalCount[0].total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
}));

/**
 * POST /api/atm/load
 * Carregar ATM com numerário
 */
router.post('/load', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = loadATMSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { atm_id, amount, denominations, notes } = value;

  // 2. Verificar se ATM existe e está online
  const atm = await executeQuery(`
    SELECT id, atm_code, location, cash_capacity, current_balance, status
    FROM atms
    WHERE id = ?
  `, [atm_id]);

  if (!atm.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  const atmData = atm[0];

  if (atmData.status === 'offline') {
    return next(new AppError('ATM está offline e não pode ser carregado', 400, 'ATM_OFFLINE'));
  }

  // 3. Verificar se o carregamento não excede a capacidade
  const newBalance = parseFloat(atmData.current_balance) + amount;
  if (newBalance > parseFloat(atmData.cash_capacity)) {
    return next(new AppError(
      `Carregamento excede a capacidade do ATM. Capacidade: ${atmData.cash_capacity} AOA, Saldo atual: ${atmData.current_balance} AOA, Tentativa de carregar: ${amount} AOA`,
      400,
      'CAPACITY_EXCEEDED'
    ));
  }

  // 4. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200
  );

  // 5. Verificar se total das denominações confere com o valor informado
  if (Math.abs(calculatedTotal - amount) > 0.01) {
    return next(new AppError('Total das denominações não confere com o valor informado', 400, 'DENOMINATION_MISMATCH'));
  }

  // 6. Verificar saldo do cofre principal (assumindo que o carregamento vem do cofre)
  const vaultBalance = await executeQuery(`
    SELECT current_balance
    FROM main_vaults
    WHERE branch_id = 1 AND vault_type = 'main'
    ORDER BY created_at DESC
    LIMIT 1
  `);

  if (!vaultBalance.length || parseFloat(vaultBalance[0].current_balance) < amount) {
    return next(new AppError('Saldo insuficiente no cofre principal para realizar o carregamento', 400, 'INSUFFICIENT_VAULT_BALANCE'));
  }

  // 7. Iniciar transação
  await executeQuery('START TRANSACTION');

  try {
    // 8. Criar registro de carregamento
    const loadingId = uuidv4();
    await executeQuery(`
      INSERT INTO atm_loadings (id, atm_id, loaded_amount, loaded_by, notes)
      VALUES (?, ?, ?, ?, ?)
    `, [loadingId, atm_id, amount, req.user.id, notes || null]);

    // 9. Atualizar saldo do ATM
    await executeQuery(`
      UPDATE atms
      SET current_balance = current_balance + ?,
          status = CASE
            WHEN status = 'low_balance' AND (current_balance + ?) > (cash_capacity * 0.2) THEN 'online'
            ELSE status
          END
      WHERE id = ?
    `, [amount, amount, atm_id]);

    // 10. Reduzir saldo do cofre principal
    const currentVaultBalance = parseFloat(vaultBalance[0].current_balance);
    const newVaultBalance = currentVaultBalance - amount;

    await executeQuery(`
      UPDATE main_vaults
      SET current_balance = ?
      WHERE branch_id = 1 AND vault_type = 'main'
    `, [newVaultBalance]);

    // 11. Registrar movimento no cofre
    await executeQuery(`
      INSERT INTO vault_movements (id, vault_id, movement_type, amount, description, created_by)
      SELECT ?, mv.id, 'outgoing', ?, CONCAT('Carregamento ATM ', ?), ?
      FROM main_vaults mv
      WHERE mv.branch_id = 1 AND mv.vault_type = 'main'
      LIMIT 1
    `, [uuidv4(), amount, atmData.atm_code, req.user.id]);

    // 12. Registrar auditoria
    await executeQuery(`
      INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, created_at)
      VALUES (?, 'ATM_LOADING', 'atms', ?, ?, ?, ?, ?)
    `, [
      req.user.id,
      atm_id,
      JSON.stringify({ current_balance: atmData.current_balance }),
      JSON.stringify({ current_balance: newBalance, loaded_amount: amount }),
      req.ip || 'unknown',
      getCurrentTimestamp()
    ]);

    // 13. Confirmar transação
    await executeQuery('COMMIT');

    // 14. Buscar dados atualizados do ATM
    const updatedATM = await executeQuery(`
      SELECT
        a.id,
        a.atm_code,
        a.location,
        a.cash_capacity,
        a.current_balance,
        a.status,
        ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
      FROM atms a
      WHERE a.id = ?
    `, [atm_id]);

    logger.info(`Carregamento de ATM realizado: ${amount} AOA para ${atmData.atm_code}`, {
      loadingId,
      atmId: atm_id,
      amount,
      loadedBy: req.user.id,
      newBalance
    });

    res.status(201).json({
      status: 'success',
      message: `Carregamento de ${amount.toLocaleString('pt-AO')} AOA realizado com sucesso no ${atmData.atm_code}`,
      data: {
        loading: {
          id: loadingId,
          atm_id,
          loaded_amount: amount,
          loading_date: new Date(),
          notes
        },
        atm: updatedATM[0]
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao realizar carregamento de ATM:', error);
    return next(new AppError('Erro interno do servidor ao processar carregamento', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * POST /api/atm
 * Criar novo ATM
 */
router.post('/', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = createATMSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { atm_code, location, branch_id, cash_capacity, status } = value;

  // 2. Verificar se código do ATM já existe
  const existingATM = await executeQuery('SELECT id FROM atms WHERE atm_code = ?', [atm_code]);
  if (existingATM.length > 0) {
    return next(new AppError('Código do ATM já existe', 409, 'ATM_CODE_EXISTS'));
  }

  // 3. Verificar se agência existe
  const branch = await executeQuery('SELECT id, name FROM branches WHERE id = ?', [branch_id]);
  if (!branch.length) {
    return next(new AppError('Agência não encontrada', 404, 'BRANCH_NOT_FOUND'));
  }

  // 4. Criar ATM
  const atmId = await executeQuery(`
    INSERT INTO atms (atm_code, location, branch_id, cash_capacity, current_balance, status, installed_date)
    VALUES (?, ?, ?, ?, 0, ?, ?)
  `, [atm_code, location, branch_id, cash_capacity, status, getCurrentTimestamp()]);

  // 5. Buscar ATM criado
  const newATM = await executeQuery(`
    SELECT
      a.id,
      a.atm_code,
      a.location,
      a.branch_id,
      a.cash_capacity,
      a.current_balance,
      a.status,
      a.installed_date,
      b.name as branch_name,
      ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
    FROM atms a
    LEFT JOIN branches b ON a.branch_id = b.id
    WHERE a.id = ?
  `, [atmId.insertId]);

  // 6. Registrar auditoria
  await executeQuery(`
    INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, created_at)
    VALUES (?, 'CREATE_ATM', 'atms', ?, ?, ?, ?)
  `, [
    req.user.id,
    atmId.insertId,
    JSON.stringify(value),
    req.ip || 'unknown',
    getCurrentTimestamp()
  ]);

  logger.info(`ATM criado: ${atm_code}`, {
    atmId: atmId.insertId,
    createdBy: req.user.id
  });

  res.status(201).json({
    status: 'success',
    message: 'ATM criado com sucesso',
    data: {
      atm: newATM[0]
    }
  });
}));

/**
 * PUT /api/atm/:id
 * Atualizar ATM
 */
router.put('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // 1. Validar dados de entrada
  const { error, value } = updateATMSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  // 2. Verificar se ATM existe
  const existingATM = await executeQuery('SELECT * FROM atms WHERE id = ?', [id]);
  if (!existingATM.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  // 3. Se atualizar código, verificar se não existe outro ATM com o mesmo código
  if (value.atm_code && value.atm_code !== existingATM[0].atm_code) {
    const duplicateCode = await executeQuery('SELECT id FROM atms WHERE atm_code = ? AND id != ?', [value.atm_code, id]);
    if (duplicateCode.length > 0) {
      return next(new AppError('Código do ATM já existe', 409, 'ATM_CODE_EXISTS'));
    }
  }

  // 4. Se atualizar agência, verificar se existe
  if (value.branch_id) {
    const branch = await executeQuery('SELECT id FROM branches WHERE id = ?', [value.branch_id]);
    if (!branch.length) {
      return next(new AppError('Agência não encontrada', 404, 'BRANCH_NOT_FOUND'));
    }
  }

  // 5. Construir query de atualização
  const updateFields = [];
  const updateValues = [];

  if (value.atm_code) {
    updateFields.push('atm_code = ?');
    updateValues.push(value.atm_code);
  }
  if (value.location) {
    updateFields.push('location = ?');
    updateValues.push(value.location);
  }
  if (value.branch_id) {
    updateFields.push('branch_id = ?');
    updateValues.push(value.branch_id);
  }
  if (value.cash_capacity) {
    updateFields.push('cash_capacity = ?');
    updateValues.push(value.cash_capacity);
  }
  if (value.status) {
    updateFields.push('status = ?');
    updateValues.push(value.status);
  }

  updateValues.push(id);

  // 6. Atualizar ATM
  await executeQuery(`
    UPDATE atms
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `, updateValues);

  // 7. Buscar ATM atualizado
  const updatedATM = await executeQuery(`
    SELECT
      a.id,
      a.atm_code,
      a.location,
      a.branch_id,
      a.cash_capacity,
      a.current_balance,
      a.status,
      a.installed_date,
      b.name as branch_name,
      ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
    FROM atms a
    LEFT JOIN branches b ON a.branch_id = b.id
    WHERE a.id = ?
  `, [id]);

  // 8. Registrar auditoria
  await executeQuery(`
    INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, created_at)
    VALUES (?, 'UPDATE_ATM', 'atms', ?, ?, ?, ?, ?)
  `, [
    req.user.id,
    id,
    JSON.stringify(existingATM[0]),
    JSON.stringify(value),
    req.ip || 'unknown',
    getCurrentTimestamp()
  ]);

  logger.info(`ATM atualizado: ${updatedATM[0].atm_code}`, {
    atmId: id,
    updatedBy: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'ATM atualizado com sucesso',
    data: {
      atm: updatedATM[0]
    }
  });
}));

/**
 * PATCH /api/atm/:id/status
 * Atualizar apenas o status do ATM
 */
router.patch('/:id/status', authorize('admin', 'gerente', 'tecnico'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // 1. Validar dados de entrada
  const { error, value } = updateStatusSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { status } = value;

  // 2. Verificar se ATM existe
  const existingATM = await executeQuery('SELECT * FROM atms WHERE id = ?', [id]);
  if (!existingATM.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  // 3. Atualizar status
  await executeQuery('UPDATE atms SET status = ? WHERE id = ?', [status, id]);

  // 4. Se colocar em manutenção, atualizar data de última manutenção
  if (status === 'maintenance') {
    await executeQuery('UPDATE atms SET last_maintenance = ? WHERE id = ?', [getCurrentTimestamp(), id]);
  }

  // 5. Buscar ATM atualizado
  const updatedATM = await executeQuery(`
    SELECT
      a.id,
      a.atm_code,
      a.location,
      a.branch_id,
      a.cash_capacity,
      a.current_balance,
      a.status,
      a.last_maintenance,
      a.installed_date,
      b.name as branch_name,
      ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
    FROM atms a
    LEFT JOIN branches b ON a.branch_id = b.id
    WHERE a.id = ?
  `, [id]);

  // 6. Registrar auditoria
  await executeQuery(`
    INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, created_at)
    VALUES (?, 'UPDATE_ATM_STATUS', 'atms', ?, ?, ?, ?, ?)
  `, [
    req.user.id,
    id,
    JSON.stringify({ status: existingATM[0].status }),
    JSON.stringify({ status }),
    req.ip || 'unknown',
    getCurrentTimestamp()
  ]);

  logger.info(`Status do ATM atualizado: ${updatedATM[0].atm_code} -> ${status}`, {
    atmId: id,
    updatedBy: req.user.id,
    oldStatus: existingATM[0].status,
    newStatus: status
  });

  res.status(200).json({
    status: 'success',
    message: `Status do ATM atualizado para ${status}`,
    data: {
      atm: updatedATM[0]
    }
  });
}));

/**
 * DELETE /api/atm/:id
 * Eliminar ATM
 */
router.delete('/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // 1. Verificar se ATM existe
  const existingATM = await executeQuery('SELECT * FROM atms WHERE id = ?', [id]);
  if (!existingATM.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  // 2. Verificar se ATM tem saldo
  if (parseFloat(existingATM[0].current_balance) > 0) {
    return next(new AppError('Não é possível eliminar ATM com saldo. Retire o saldo primeiro.', 400, 'ATM_HAS_BALANCE'));
  }

  // 3. Verificar se ATM tem histórico de carregamentos
  const loadings = await executeQuery('SELECT COUNT(*) as count FROM atm_loadings WHERE atm_id = ?', [id]);
  if (loadings[0].count > 0) {
    return next(new AppError('Não é possível eliminar ATM com histórico de carregamentos. Considere desativar em vez de eliminar.', 400, 'ATM_HAS_HISTORY'));
  }

  // 4. Eliminar ATM
  await executeQuery('DELETE FROM atms WHERE id = ?', [id]);

  // 5. Registrar auditoria
  await executeQuery(`
    INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, created_at)
    VALUES (?, 'DELETE_ATM', 'atms', ?, ?, ?, ?, ?)
  `, [
    req.user.id,
    id,
    JSON.stringify(existingATM[0]),
    JSON.stringify({ deleted: true }),
    req.ip || 'unknown',
    getCurrentTimestamp()
  ]);

  logger.info(`ATM eliminado: ${existingATM[0].atm_code}`, {
    atmId: id,
    deletedBy: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'ATM eliminado com sucesso'
  });
}));

module.exports = router;
