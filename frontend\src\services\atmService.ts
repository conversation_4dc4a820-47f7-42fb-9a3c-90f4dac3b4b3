import { makeRequest } from '../utils/api';

// Interfaces para ATM
export interface ATM {
  id: number;
  atm_code: string;
  location: string;
  branch_id?: number;
  cash_capacity: number;
  current_balance: number;
  status: 'online' | 'offline' | 'low_balance' | 'maintenance';
  last_maintenance?: string;
  installed_date?: string;
  branch_name?: string;
  capacity_percentage: number;
}

export interface ATMLoading {
  id: string;
  atm_id: number;
  loaded_amount: number;
  loading_date: string;
  notes?: string;
  loaded_by_name: string;
  atm_code: string;
}

export interface LoadATMRequest {
  atm_id: number;
  amount: number;
  denominations: {
    notes_10000: number;
    notes_5000: number;
    notes_2000: number;
    notes_1000: number;
    notes_500: number;
    notes_200: number;
    notes_100: number;
    notes_50: number;
    coins_10: number;
    coins_5: number;
    coins_1: number;
  };
  notes?: string;
}

export interface LoadATMResponse {
  loading: {
    id: string;
    atm_id: number;
    loaded_amount: number;
    loading_date: string;
    notes?: string;
  };
  atm: ATM;
}

export interface ATMListResponse {
  atms: ATM[];
  total: number;
}

export interface ATMLoadingsResponse {
  loadings: ATMLoading[];
  total: number;
  limit: number;
  offset: number;
}

export interface CreateATMRequest {
  atm_code: string;
  location: string;
  branch_id: number;
  cash_capacity: number;
  status: 'online' | 'offline' | 'maintenance';
}

export interface UpdateATMRequest {
  atm_code?: string;
  location?: string;
  branch_id?: number;
  cash_capacity?: number;
  status?: 'online' | 'offline' | 'maintenance';
}

class ATMService {
  // Listar todos os ATMs
  async getATMs(): Promise<ATMListResponse> {
    const response = await makeRequest('/atm', {
      method: 'GET'
    });
    return response.data;
  }

  // Obter detalhes de um ATM específico
  async getATM(id: number): Promise<ATM> {
    const response = await makeRequest(`/atm/${id}`, {
      method: 'GET'
    });
    return response.data.atm;
  }

  // Obter histórico de carregamentos de um ATM
  async getATMLoadings(id: number, limit: number = 10, offset: number = 0): Promise<ATMLoadingsResponse> {
    const response = await makeRequest(`/atm/${id}/loadings?limit=${limit}&offset=${offset}`, {
      method: 'GET'
    });
    return response.data;
  }

  // Carregar ATM com numerário
  async loadATM(data: LoadATMRequest): Promise<LoadATMResponse> {
    const response = await makeRequest('/atm/load', {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  // Função auxiliar para calcular status baseado no saldo
  getStatusColor(status: string): string {
    switch (status) {
      case 'online':
        return 'text-green-600';
      case 'low_balance':
        return 'text-yellow-600';
      case 'offline':
        return 'text-red-600';
      case 'maintenance':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  }

  // Função auxiliar para traduzir status
  getStatusText(status: string): string {
    switch (status) {
      case 'online':
        return 'Online';
      case 'low_balance':
        return 'Baixo Saldo';
      case 'offline':
        return 'Offline';
      case 'maintenance':
        return 'Manutenção';
      default:
        return 'Desconhecido';
    }
  }

  // Função auxiliar para formatar valores monetários
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(amount).replace('AOA', 'Kz');
  }

  // Função auxiliar para calcular percentual de capacidade
  getCapacityPercentage(currentBalance: number, capacity: number): number {
    return Math.round((currentBalance / capacity) * 100);
  }

  // Função auxiliar para determinar se ATM precisa de carregamento
  needsLoading(currentBalance: number, capacity: number): boolean {
    const percentage = this.getCapacityPercentage(currentBalance, capacity);
    return percentage < 20; // Menos de 20% da capacidade
  }

  // Criar novo ATM
  async createATM(data: CreateATMRequest): Promise<ATM> {
    const response = await makeRequest('/atm', {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data.atm;
  }

  // Atualizar ATM
  async updateATM(id: number, data: UpdateATMRequest): Promise<ATM> {
    const response = await makeRequest(`/atm/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data.atm;
  }

  // Alterar estado do ATM
  async updateATMStatus(id: number, status: 'online' | 'offline' | 'maintenance'): Promise<ATM> {
    const response = await makeRequest(`/atm/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data.atm;
  }

  // Eliminar ATM
  async deleteATM(id: number): Promise<void> {
    await makeRequest(`/atm/${id}`, {
      method: 'DELETE'
    });
  }
}

export const atmService = new ATMService();
