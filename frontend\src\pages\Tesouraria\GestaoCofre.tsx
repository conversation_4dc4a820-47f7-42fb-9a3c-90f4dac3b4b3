import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Vault, 
  Eye, 
  RefreshCw, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Calendar,
  Filter,
  Download,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { vaultService, Vault as VaultType, VaultMovement } from '@/services/vaultService';

const GestaoCofre = () => {
  // Estados principais
  const [vault, setVault] = useState<VaultType | null>(null);
  const [movements, setMovements] = useState<VaultMovement[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMovements, setIsLoadingMovements] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Estados de filtros
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    movement_type: '',
    date_from: '',
    date_to: ''
  });

  // Estados de paginação
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  const { toast } = useToast();

  // Carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, []);

  // Carregar movimentos quando filtros mudarem
  useEffect(() => {
    if (vault) {
      loadMovements();
    }
  }, [filters, vault]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      
      // Obter primeiro cofre ativo (assumindo que há apenas um cofre principal)
      const vaultsResponse = await vaultService.getAllVaults({ is_active: true });
      
      if (vaultsResponse.vaults.length === 0) {
        throw new Error('Nenhum cofre principal encontrado');
      }

      const mainVault = vaultsResponse.vaults[0];
      setVault(mainVault);

      // Carregar estatísticas
      await loadStatistics(mainVault.id);

    } catch (error: any) {
      console.error('Erro ao carregar dados iniciais:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar dados do cofre",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadStatistics = async (vaultId: number) => {
    try {
      const statsResponse = await vaultService.getVaultStatistics(vaultId, 'month');
      setStatistics(statsResponse.statistics);
    } catch (error: any) {
      console.error('Erro ao carregar estatísticas:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar estatísticas do cofre",
        variant: "destructive"
      });
    }
  };

  const loadMovements = async () => {
    if (!vault) return;

    try {
      setIsLoadingMovements(true);
      const response = await vaultService.getVaultMovements(vault.id, filters);
      setMovements(response.movements);
      setPagination(response.pagination);
    } catch (error: any) {
      console.error('Erro ao carregar movimentos:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar histórico de movimentações",
        variant: "destructive"
      });
    } finally {
      setIsLoadingMovements(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await Promise.all([
      loadInitialData(),
      vault && loadMovements()
    ]);
    setIsRefreshing(false);
    
    toast({
      title: "Atualizado",
      description: "Dados do cofre atualizados com sucesso",
    });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: key === 'movement_type' && value === 'all' ? '' : value,
      page: 1 // Reset para primeira página quando filtrar
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 10,
      movement_type: '',
      date_from: '',
      date_to: ''
    });
  };

  const exportMovements = () => {
    if (movements.length === 0) {
      toast({
        title: "Aviso",
        description: "Não há dados para exportar",
        variant: "destructive"
      });
      return;
    }

    // Preparar dados para exportação
    const exportData = movements.map(movement => ({
      'Origem/Destino': movement.source_type
        ? `${vaultService.formatSourceType(movement.source_type)}${movement.processed_by_name ? ' - ' + movement.processed_by_name : ''}`
        : '-',
      'Tipo': vaultService.formatMovementType(movement.movement_type),
      'Valor': `${movement.movement_type === 'withdrawal' || movement.movement_type === 'transfer_out' ? '-' : '+'}${vaultService.formatCurrency(movement.amount)}`,
      'Saldo Resultante': vaultService.formatCurrency(movement.new_balance),
      'Processado por': movement.processed_by_name || 'Sistema',
      'Data/Hora': vaultService.formatDate(movement.created_at)
    }));

    // Converter para CSV
    const headers = Object.keys(exportData[0]);
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${row[header as keyof typeof row]}"`).join(',')
      )
    ].join('\n');

    // Criar e baixar arquivo
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `movimentos_cofre_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Sucesso",
      description: "Dados exportados com sucesso",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-twins-primary" />
          <p className="text-gray-600">Carregando dados do cofre...</p>
        </div>
      </div>
    );
  }

  if (!vault) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <h3 className="text-lg font-semibold mb-2">Cofre não encontrado</h3>
          <p className="text-gray-600 mb-4">Não foi possível encontrar um cofre principal ativo.</p>
          <Button onClick={loadInitialData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestão do Cofre
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitorização e controlo do cofre principal
          </p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={isRefreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </div>

      {/* Cards de Resumo */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Saldo Atual */}
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-800 flex items-center gap-2">
              <Vault className="h-4 w-4" />
              Saldo Atual
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">
              {vaultService.formatCurrency(vault.current_balance)}
            </div>
            <p className="text-xs text-green-700 mt-1">
              {vault.name} • {vault.branch_name}
            </p>
          </CardContent>
        </Card>

        {/* Total de Movimentos */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Movimentos (30d)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {statistics?.total_movements || 0}
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Operações realizadas nos últimos 30 dias
            </p>
          </CardContent>
        </Card>

        {/* Total Depósitos */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              Depósitos (30d)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {vaultService.formatCurrency(statistics?.total_deposits || 0)}
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Entradas no cofre nos últimos 30 dias
            </p>
          </CardContent>
        </Card>

        {/* Total Retiradas */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 flex items-center gap-2">
              <TrendingDown className="h-4 w-4 text-red-600" />
              Retiradas (30d)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {vaultService.formatCurrency(statistics?.total_withdrawals || 0)}
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Saídas do cofre nos últimos 30 dias
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Histórico de Movimentações */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Histórico de Movimentações
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={clearFilters}>
                <Filter className="h-4 w-4 mr-2" />
                Limpar Filtros
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportMovements}
                disabled={movements.length === 0 || isLoadingMovements}
              >
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filtros */}
          <div className="grid gap-4 md:grid-cols-4">
            <div className="space-y-2">
              <Label htmlFor="movement_type">Tipo de Movimento</Label>
              <Select
                value={filters.movement_type || 'all'}
                onValueChange={(value) => handleFilterChange('movement_type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos os tipos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os tipos</SelectItem>
                  <SelectItem value="deposit">Depósito</SelectItem>
                  <SelectItem value="withdrawal">Retirada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date_from">Data Inicial</Label>
              <Input
                id="date_from"
                type="date"
                value={filters.date_from}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="date_to">Data Final</Label>
              <Input
                id="date_to"
                type="date"
                value={filters.date_to}
                onChange={(e) => handleFilterChange('date_to', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="limit">Itens por página</Label>
              <Select 
                value={filters.limit.toString()} 
                onValueChange={(value) => handleFilterChange('limit', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Tabela de Movimentos */}
          <div className="border rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Origem/Destino
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipo
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valor
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Saldo Resultante
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Processado por
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data/Hora
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  {isLoadingMovements ? (
                    <tr>
                      <td colSpan={6} className="px-4 py-8 text-center">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-twins-primary" />
                        <p className="text-gray-500">Carregando movimentos...</p>
                      </td>
                    </tr>
                  ) : movements.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                        Nenhum movimento encontrado
                      </td>
                    </tr>
                  ) : (
                    movements.map((movement) => (
                      <tr key={movement.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                          {movement.source_type ? (
                            <div className="flex flex-col">
                              <span className="font-medium">{vaultService.formatSourceType(movement.source_type)}</span>
                              {movement.processed_by_name && (
                                <span className="text-xs text-muted-foreground">{movement.processed_by_name}</span>
                              )}
                            </div>
                          ) : '-'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            <span className={`text-lg ${vaultService.getMovementColors(movement.movement_type).iconColor}`}>
                              {vaultService.getMovementIcon(movement.movement_type)}
                            </span>
                            <Badge
                              variant="outline"
                              className={`text-xs ${vaultService.getMovementColors(movement.movement_type).textColor} ${vaultService.getMovementColors(movement.movement_type).bgColor} border-current`}
                            >
                              {vaultService.formatMovementType(movement.movement_type)}
                            </Badge>
                          </div>
                        </td>
                        <td className={`px-4 py-4 whitespace-nowrap text-sm text-right font-medium ${vaultService.getMovementColor(movement.movement_type)}`}>
                          {movement.movement_type === 'withdrawal' || movement.movement_type === 'transfer_out' ? '-' : '+'}
                          {vaultService.formatCurrency(movement.amount)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900 dark:text-gray-100">
                          {vaultService.formatCurrency(movement.new_balance)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                          {movement.processed_by_name || 'Sistema'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {vaultService.formatDate(movement.created_at)}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Paginação */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                Mostrando {((pagination.page - 1) * pagination.limit) + 1} a {Math.min(pagination.page * pagination.limit, pagination.total)} de {pagination.total} resultados
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrev}
                >
                  Anterior
                </Button>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Página {pagination.page} de {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNext}
                >
                  Próxima
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GestaoCofre;
