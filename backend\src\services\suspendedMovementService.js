const { executeQuery, executeTransaction } = require('../config/database');

/**
 * Executa uma query usando uma conexão específica (para transações)
 */
const executeQueryWithConnection = async (query, params = [], connection) => {
  const [result] = await connection.execute(query, params);
  return result;
};
const { AppError } = require('../core/errorHandler');
const { v4: uuidv4 } = require('uuid');
const logger = require('../core/logger');

/**
 * Serviço para operações de movimentos suspensos
 */
class SuspendedMovementService {
  
  /**
   * Listar movimentos suspensos com filtros
   */
  async getSuspendedMovements(filters = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        searchTerm,
        status,
        dateFrom,
        dateTo
      } = filters;

      const offset = (page - 1) * limit;
      let whereConditions = [];
      let queryParams = [];

      // Construir condições WHERE
      if (searchTerm) {
        whereConditions.push(`(
          t.transaction_code LIKE ? OR 
          t.description LIKE ? OR
          u.name LIKE ?
        )`);
        const searchPattern = `%${searchTerm}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern);
      }

      if (status && status !== 'all') {
        whereConditions.push('sm.status = ?');
        queryParams.push(status);
      }

      if (dateFrom) {
        whereConditions.push('DATE(sm.suspended_at) >= ?');
        queryParams.push(dateFrom);
      }

      if (dateTo) {
        whereConditions.push('DATE(sm.suspended_at) <= ?');
        queryParams.push(dateTo);
      }

      const whereClause = whereConditions.length > 0 
        ? `WHERE ${whereConditions.join(' AND ')}` 
        : '';

      // Query principal
      const movementsQuery = `
        SELECT 
          sm.id,
          sm.transaction_id,
          t.transaction_code,
          t.transaction_type,
          t.amount,
          t.description,
          sm.suspension_reason,
          sm.suspension_type,
          sm.status,
          sm.suspended_at,
          sm.resolved_at,
          sm.resolution_notes,
          u_suspended.full_name as suspended_by_name,
          u_resolved.full_name as resolved_by_name,
          curr.code as currency_code,
          curr.symbol as currency_symbol,
          COALESCE(a_source.account_number, 'N/A') as source_account,
          COALESCE(a_dest.account_number, 'N/A') as destination_account
        FROM suspended_movements sm
        JOIN transactions t ON sm.transaction_id = t.id
        JOIN users u_suspended ON sm.suspended_by = u_suspended.id
        LEFT JOIN users u_resolved ON sm.resolved_by = u_resolved.id
        JOIN currencies curr ON t.currency_id = curr.id
        LEFT JOIN accounts a_source ON t.source_account_id = a_source.id
        LEFT JOIN accounts a_dest ON t.destination_account_id = a_dest.id
        ${whereClause}
        ORDER BY sm.suspended_at DESC
        LIMIT ? OFFSET ?
      `;

      // Query para contar total
      const countQuery = `
        SELECT COUNT(*) as total
        FROM suspended_movements sm
        JOIN transactions t ON sm.transaction_id = t.id
        ${whereClause}
      `;

      const [movements, countResult] = await Promise.all([
        executeQuery(movementsQuery, [...queryParams, limit, offset]),
        executeQuery(countQuery, queryParams)
      ]);

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      return {
        movements: movements.map(movement => ({
          id: movement.id,
          transactionId: movement.transaction_id,
          transactionCode: movement.transaction_code,
          type: movement.transaction_type,
          sourceAccount: movement.source_account,
          destinationAccount: movement.destination_account,
          amount: parseFloat(movement.amount),
          currency: {
            code: movement.currency_code,
            symbol: movement.currency_symbol
          },
          description: movement.description,
          suspensionReason: movement.suspension_reason,
          suspensionType: movement.suspension_type,
          status: movement.status,
          suspendedAt: movement.suspended_at,
          resolvedAt: movement.resolved_at,
          resolutionNotes: movement.resolution_notes,
          suspendedBy: movement.suspended_by_name,
          resolvedBy: movement.resolved_by_name
        })),
        total,
        page: parseInt(page),
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };
    } catch (error) {
      logger.error('Erro ao listar movimentos suspensos:', error);
      throw error;
    }
  }

  /**
   * Obter estatísticas de movimentos suspensos
   */
  async getStatistics() {
    try {
      const stats = await executeQuery(`
        SELECT 
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
          COUNT(*) as total
        FROM suspended_movements
      `);

      return {
        pending: parseInt(stats[0].pending) || 0,
        approved: parseInt(stats[0].approved) || 0,
        rejected: parseInt(stats[0].rejected) || 0,
        total: parseInt(stats[0].total) || 0
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas:', error);
      throw error;
    }
  }

  /**
   * Aprovar movimento suspenso
   */
  async approveMovement(movementId, observacoes, userId) {
    try {
      return await executeTransaction(async (connection) => {
        // Verificar se o movimento existe e está pendente
        const movement = await executeQueryWithConnection(`
          SELECT sm.*, t.status as transaction_status
          FROM suspended_movements sm
          JOIN transactions t ON sm.transaction_id = t.id
          WHERE sm.id = ?
        `, [movementId], connection);

        if (!movement || movement.length === 0) {
          throw new AppError('Movimento suspenso não encontrado', 404, 'MOVEMENT_NOT_FOUND');
        }

        if (movement[0].status !== 'pending') {
          throw new AppError('Apenas movimentos pendentes podem ser aprovados', 400, 'INVALID_STATUS');
        }

        // Atualizar status do movimento suspenso
        await executeQueryWithConnection(`
          UPDATE suspended_movements
          SET status = 'approved', resolved_by = ?, resolved_at = NOW(),
              resolution_notes = ?
          WHERE id = ?
        `, [userId, observacoes || 'Movimento aprovado', movementId], connection);

        // Atualizar status da transação para completed
        await executeQueryWithConnection(`
          UPDATE transactions
          SET status = 'completed', processed_at = NOW()
          WHERE id = ?
        `, [movement[0].transaction_id], connection);

        // Log de auditoria
        await executeQueryWithConnection(`
          INSERT INTO audit_logs (
            user_id, action, table_name, record_id,
            old_values, new_values, ip_address
          ) VALUES (?, 'UPDATE', 'suspended_movements', ?,
            ?, ?, '127.0.0.1')
        `, [
          userId, movementId,
          JSON.stringify({ status: 'pending' }),
          JSON.stringify({ status: 'approved', observacoes })
        ], connection);

        return { success: true, message: 'Movimento aprovado com sucesso' };
      });
    } catch (error) {
      logger.error('Erro ao aprovar movimento:', error);
      throw error;
    }
  }

  /**
   * Rejeitar movimento suspenso
   */
  async rejectMovement(movementId, motivo, userId) {
    try {
      return await executeTransaction(async (connection) => {
        // Verificar se o movimento existe e está pendente
        const movement = await executeQueryWithConnection(`
          SELECT sm.*, t.status as transaction_status
          FROM suspended_movements sm
          JOIN transactions t ON sm.transaction_id = t.id
          WHERE sm.id = ?
        `, [movementId], connection);

        if (!movement || movement.length === 0) {
          throw new AppError('Movimento suspenso não encontrado', 404, 'MOVEMENT_NOT_FOUND');
        }

        if (movement[0].status !== 'pending') {
          throw new AppError('Apenas movimentos pendentes podem ser rejeitados', 400, 'INVALID_STATUS');
        }

        // Atualizar status do movimento suspenso
        await executeQueryWithConnection(`
          UPDATE suspended_movements
          SET status = 'rejected', resolved_by = ?, resolved_at = NOW(),
              resolution_notes = ?
          WHERE id = ?
        `, [userId, motivo, movementId], connection);

        // Atualizar status da transação para cancelled
        await executeQueryWithConnection(`
          UPDATE transactions
          SET status = 'cancelled', processed_at = NOW()
          WHERE id = ?
        `, [movement[0].transaction_id], connection);

        // Log de auditoria
        await executeQueryWithConnection(`
          INSERT INTO audit_logs (
            user_id, action, table_name, record_id,
            old_values, new_values, ip_address
          ) VALUES (?, 'UPDATE', 'suspended_movements', ?,
            ?, ?, '127.0.0.1')
        `, [
          userId, movementId,
          JSON.stringify({ status: 'pending' }),
          JSON.stringify({ status: 'rejected', motivo })
        ], connection);

        return { success: true, message: 'Movimento rejeitado com sucesso' };
      });
    } catch (error) {
      logger.error('Erro ao rejeitar movimento:', error);
      throw error;
    }
  }

  /**
   * Criar movimento suspenso (para testes)
   */
  async createSuspendedMovement(transactionData, suspensionReason, userId) {
    try {
      return await executeTransaction(async (connection) => {
        // Criar transação
        const transactionId = uuidv4();
        const transactionCode = `SM${Date.now()}`;

        await executeQueryWithConnection(`
          INSERT INTO transactions (
            id, transaction_code, transaction_type, amount, currency_id,
            description, status, processed_by
          ) VALUES (?, ?, ?, ?, 1, ?, 'suspended', ?)
        `, [
          transactionId, transactionCode, transactionData.type,
          transactionData.amount, transactionData.description, userId
        ], connection);

        // Criar movimento suspenso
        const movementId = uuidv4();
        await executeQueryWithConnection(`
          INSERT INTO suspended_movements (
            id, transaction_id, suspension_reason, suspension_type,
            suspended_by, status
          ) VALUES (?, ?, ?, 'manual', ?, 'pending')
        `, [
          movementId, transactionId, suspensionReason, userId
        ], connection);

        return {
          id: movementId,
          transactionId,
          transactionCode,
          message: 'Movimento suspenso criado com sucesso'
        };
      });
    } catch (error) {
      logger.error('Erro ao criar movimento suspenso:', error);
      throw error;
    }
  }
}

module.exports = new SuspendedMovementService();
