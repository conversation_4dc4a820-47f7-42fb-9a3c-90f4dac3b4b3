const express = require('express');
const { catchAsync, AppError } = require('../core/errorHandler');
const { authorize } = require('../auth/middleware');
const logger = require('../core/logger');
const { 
  uploadClientDocuments, 
  uploadAvatar, 
  processDocumentUploads, 
  processAvatarUpload,
  handleUploadError,
  deleteClientDocuments,
  deleteUserAvatar 
} = require('../core/supabaseUpload');

const router = express.Router();

/**
 * POST /api/upload/client-documents
 * Upload de documentos de cliente para Supabase
 */
router.post('/client-documents', 
  authorize('admin', 'gerente', 'caixa'),
  uploadClientDocuments,
  catchAsync(async (req, res, next) => {
    const { clientId, clientType = 'individual' } = req.body;

    if (!clientId) {
      return next(new AppError('ID do cliente é obrigatório', 400, 'MISSING_CLIENT_ID'));
    }

    if (!req.files || Object.keys(req.files).length === 0) {
      return next(new AppError('Nenhum ficheiro foi enviado', 400, 'NO_FILES'));
    }

    try {
      // Processar upload dos documentos para Supabase
      const uploadedFiles = await processDocumentUploads(req.files, clientId, clientType);

      logger.info(`Documentos carregados para cliente ${clientId}:`, {
        clientId,
        clientType,
        filesCount: Object.keys(uploadedFiles).length,
        files: Object.keys(uploadedFiles),
        uploadedBy: req.user.id
      });

      res.status(200).json({
        status: 'success',
        message: 'Documentos carregados com sucesso',
        data: {
          clientId,
          clientType,
          uploadedFiles,
          filesCount: Object.keys(uploadedFiles).length
        }
      });
    } catch (error) {
      logger.error('Erro no upload de documentos:', error);
      return next(new AppError(`Erro no upload: ${error.message}`, 500, 'UPLOAD_ERROR'));
    }
  })
);

/**
 * POST /api/upload/avatar
 * Upload de avatar de utilizador para Supabase
 */
router.post('/avatar',
  authorize('admin', 'gerente', 'caixa', 'tesoureiro', 'tecnico'),
  uploadAvatar,
  catchAsync(async (req, res, next) => {
    const userId = req.user.id;

    if (!req.file) {
      return next(new AppError('Nenhum ficheiro foi enviado', 400, 'NO_FILE'));
    }

    try {
      // Processar upload do avatar para Supabase
      const uploadedAvatar = await processAvatarUpload(req.file, userId);

      logger.info(`Avatar carregado para utilizador ${userId}:`, {
        userId,
        fileName: uploadedAvatar.fileName,
        size: uploadedAvatar.size,
        publicUrl: uploadedAvatar.publicUrl
      });

      res.status(200).json({
        status: 'success',
        message: 'Avatar carregado com sucesso',
        data: {
          userId,
          avatar: uploadedAvatar
        }
      });
    } catch (error) {
      logger.error('Erro no upload de avatar:', error);
      return next(new AppError(`Erro no upload: ${error.message}`, 500, 'UPLOAD_ERROR'));
    }
  })
);

/**
 * DELETE /api/upload/client-documents/:clientId
 * Eliminar todos os documentos de um cliente do Supabase
 */
router.delete('/client-documents/:clientId',
  authorize('admin', 'gerente'),
  catchAsync(async (req, res, next) => {
    const { clientId } = req.params;
    const { clientType = 'individual' } = req.query;

    if (!clientId) {
      return next(new AppError('ID do cliente é obrigatório', 400, 'MISSING_CLIENT_ID'));
    }

    try {
      // Eliminar documentos do cliente do Supabase
      await deleteClientDocuments(clientId, clientType);

      logger.info(`Documentos eliminados para cliente ${clientId}:`, {
        clientId,
        clientType,
        deletedBy: req.user.id
      });

      res.status(200).json({
        status: 'success',
        message: 'Documentos eliminados com sucesso',
        data: {
          clientId,
          clientType
        }
      });
    } catch (error) {
      logger.error('Erro ao eliminar documentos:', error);
      return next(new AppError(`Erro ao eliminar documentos: ${error.message}`, 500, 'DELETE_ERROR'));
    }
  })
);

/**
 * DELETE /api/upload/avatar/:userId
 * Eliminar avatar de utilizador do Supabase
 */
router.delete('/avatar/:userId',
  authorize('admin'),
  catchAsync(async (req, res, next) => {
    const { userId } = req.params;

    if (!userId) {
      return next(new AppError('ID do utilizador é obrigatório', 400, 'MISSING_USER_ID'));
    }

    try {
      // Eliminar avatar do utilizador do Supabase
      await deleteUserAvatar(userId);

      logger.info(`Avatar eliminado para utilizador ${userId}:`, {
        userId,
        deletedBy: req.user.id
      });

      res.status(200).json({
        status: 'success',
        message: 'Avatar eliminado com sucesso',
        data: {
          userId
        }
      });
    } catch (error) {
      logger.error('Erro ao eliminar avatar:', error);
      return next(new AppError(`Erro ao eliminar avatar: ${error.message}`, 500, 'DELETE_ERROR'));
    }
  })
);

/**
 * GET /api/upload/test
 * Testar conectividade com Supabase
 */
router.get('/test',
  authorize('admin'),
  catchAsync(async (req, res) => {
    const { supabase } = require('../core/supabaseClient');

    try {
      // Testar listagem de buckets
      const { data: buckets, error } = await supabase.storage.listBuckets();

      if (error) {
        throw new Error(`Erro na conexão: ${error.message}`);
      }

      res.status(200).json({
        status: 'success',
        message: 'Conexão com Supabase estabelecida',
        data: {
          bucketsCount: buckets.length,
          buckets: buckets.map(b => ({ name: b.name, public: b.public }))
        }
      });
    } catch (error) {
      logger.error('Erro no teste de conexão Supabase:', error);
      res.status(500).json({
        status: 'error',
        message: `Erro na conexão com Supabase: ${error.message}`
      });
    }
  })
);

// Middleware de tratamento de erros de upload
router.use(handleUploadError);

module.exports = router;
