const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');
const { executeQuery, executeTransaction } = require('../config/database');
const logger = require('../core/logger');
const { getCurrentTimestamp } = require('../utils/dateUtils');

/**
 * Gera um token JWT
 * @param {Object} payload - Dados a incluir no token
 * @param {string} expiresIn - Tempo de expiração
 * @returns {string} Token JWT
 */
const generateToken = (payload, expiresIn = process.env.JWT_EXPIRES_IN || '24h') => {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn,
    issuer: 'k-bank-api',
    audience: 'k-bank-frontend'
  });
};

/**
 * Verifica um token JWT
 * @param {string} token - Token a verificar
 * @returns {Object} Payload decodificado
 */
const verifyToken = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET, {
    issuer: 'k-bank-api',
    audience: 'k-bank-frontend'
  });
};

/**
 * Gera hash de uma senha
 * @param {string} password - Senha em texto plano
 * @returns {string} Hash da senha
 */
const hashPassword = async (password) => {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Compara uma senha com seu hash
 * @param {string} password - Senha em texto plano
 * @param {string} hash - Hash armazenado
 * @returns {boolean} True se a senha corresponder
 */
const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

/**
 * Cria uma nova sessão de utilizador
 * @param {string} userId - ID do utilizador
 * @param {string} token - Token JWT
 * @returns {Object} Dados da sessão
 */
const createUserSession = async (userId, token) => {
  const sessionId = uuidv4();
  const tokenHash = await bcrypt.hash(token, 10);
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + 24); // 24 horas

  await executeQuery(
    `INSERT INTO user_sessions (id, user_id, token_hash, expires_at) 
     VALUES (?, ?, ?, ?)`,
    [sessionId, userId, tokenHash, expiresAt]
  );

  logger.auth(`Nova sessão criada para utilizador ${userId}`, { sessionId });

  return {
    sessionId,
    expiresAt
  };
};

/**
 * Remove uma sessão de utilizador
 * @param {string} userId - ID do utilizador
 * @param {string} token - Token JWT (opcional)
 */
const removeUserSession = async (userId, token = null) => {
  if (token) {
    // Remover sessão específica
    const sessions = await executeQuery(
      'SELECT * FROM user_sessions WHERE user_id = ?',
      [userId]
    );

    for (const session of sessions) {
      const isMatch = await bcrypt.compare(token, session.token_hash);
      if (isMatch) {
        await executeQuery(
          'DELETE FROM user_sessions WHERE id = ?',
          [session.id]
        );
        logger.auth(`Sessão removida para utilizador ${userId}`, { sessionId: session.id });
        break;
      }
    }
  } else {
    // Remover todas as sessões do utilizador
    await executeQuery(
      'DELETE FROM user_sessions WHERE user_id = ?',
      [userId]
    );
    logger.auth(`Todas as sessões removidas para utilizador ${userId}`);
  }
};

/**
 * Limpa sessões expiradas
 */
const cleanExpiredSessions = async () => {
  const result = await executeQuery(
    'DELETE FROM user_sessions WHERE expires_at < ?',
    [getCurrentTimestamp()]
  );

  if (result.affectedRows > 0) {
    logger.info(`${result.affectedRows} sessões expiradas removidas`);
  }
};

/**
 * Gera tokens de acesso e refresh
 * @param {Object} user - Dados do utilizador
 * @returns {Object} Tokens e informações da sessão
 */
const generateTokenPair = async (user) => {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role_name,
    branch_id: user.branch_id
  };

  const accessToken = generateToken(payload, process.env.JWT_EXPIRES_IN || '24h');
  const refreshToken = generateToken(
    { id: user.id, type: 'refresh' }, 
    process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  );

  // Criar sessão
  const session = await createUserSession(user.id, accessToken);

  return {
    accessToken,
    refreshToken,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    sessionId: session.sessionId,
    user: {
      id: user.id,
      full_name: user.full_name,
      email: user.email,
      role: user.role_name,
      branch_id: user.branch_id
    }
  };
};

/**
 * Renova um token de acesso usando refresh token
 * @param {string} refreshToken - Token de refresh
 * @returns {Object} Novo token de acesso
 */
const refreshAccessToken = async (refreshToken) => {
  try {
    const decoded = verifyToken(refreshToken);
    
    if (decoded.type !== 'refresh') {
      throw new Error('Token de refresh inválido');
    }

    // Verificar se o utilizador ainda existe e está ativo
    const user = await executeQuery(
      `SELECT u.*, r.name as role_name 
       FROM users u 
       JOIN roles r ON u.role_id = r.id 
       WHERE u.id = ? AND u.is_active = true`,
      [decoded.id]
    );

    if (!user || user.length === 0) {
      throw new Error('Utilizador não encontrado ou inativo');
    }

    // Gerar novo token de acesso
    const payload = {
      id: user[0].id,
      email: user[0].email,
      role: user[0].role_name,
      branch_id: user[0].branch_id
    };

    const newAccessToken = generateToken(payload);
    
    // Criar nova sessão
    const session = await createUserSession(user[0].id, newAccessToken);

    logger.auth(`Token renovado para utilizador ${user[0].email}`, { userId: user[0].id });

    return {
      accessToken: newAccessToken,
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      sessionId: session.sessionId
    };

  } catch (error) {
    logger.error('Erro ao renovar token:', error);
    throw error;
  }
};

module.exports = {
  generateToken,
  verifyToken,
  hashPassword,
  comparePassword,
  createUserSession,
  removeUserSession,
  cleanExpiredSessions,
  generateTokenPair,
  refreshAccessToken
};
