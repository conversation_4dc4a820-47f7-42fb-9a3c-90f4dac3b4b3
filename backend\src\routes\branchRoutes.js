const express = require('express');
const Joi = require('joi');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const { authorize } = require('../auth/middleware');
const logger = require('../core/logger');

const router = express.Router();

// Esquemas de validação
const createBranchSchema = Joi.object({
  code: Joi.string().min(2).max(10).required().messages({
    'string.min': 'Código deve ter pelo menos 2 caracteres',
    'string.max': 'Código deve ter no máximo 10 caracteres',
    'any.required': 'Código é obrigatório'
  }),
  name: Joi.string().min(2).max(255).required().messages({
    'string.min': 'Nome deve ter pelo menos 2 caracteres',
    'string.max': 'Nome deve ter no máximo 255 caracteres',
    'any.required': 'Nome é obrigatório'
  }),
  address: Joi.string().max(500).optional().allow(''),
  phone: Joi.string().max(20).optional().allow(''),
  email: Joi.string().email().optional().allow('').messages({
    'string.email': 'Email deve ter um formato válido'
  }),
  manager_id: Joi.string().uuid().optional().allow(null)
});

const updateBranchSchema = Joi.object({
  code: Joi.string().min(2).max(10).optional(),
  name: Joi.string().min(2).max(255).optional(),
  address: Joi.string().max(500).optional().allow(''),
  phone: Joi.string().max(20).optional().allow(''),
  email: Joi.string().email().optional().allow('').messages({
    'string.email': 'Email deve ter um formato válido'
  }),
  manager_id: Joi.string().uuid().optional().allow(null),
  is_active: Joi.boolean().optional()
});

/**
 * GET /api/branches
 * Listar balcões
 */
router.get('/', authorize('admin', 'gerente', 'caixa'), catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '', active = '' } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Construir query com filtros
  let whereConditions = [];
  let queryParams = [];
  
  if (search) {
    whereConditions.push('(b.name LIKE ? OR b.code LIKE ? OR b.address LIKE ?)');
    queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
  }
  
  if (active !== '') {
    whereConditions.push('b.is_active = ?');
    queryParams.push(active === 'true');
  }
  
  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
  
  // Query principal
  const branches = await executeQuery(
    `SELECT b.id, b.code, b.name, b.address, b.phone, b.email, 
            b.is_active, b.created_at,
            u.full_name as manager_name, u.email as manager_email
     FROM branches b 
     LEFT JOIN users u ON b.manager_id = u.id 
     ${whereClause}
     ORDER BY b.created_at DESC 
     LIMIT ? OFFSET ?`,
    [...queryParams, parseInt(limit), offset]
  );
  
  // Contar total
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total 
     FROM branches b 
     ${whereClause}`,
    queryParams
  );
  
  const total = totalResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.status(200).json({
    status: 'success',
    data: {
      branches,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * GET /api/branches/:id
 * Obter balcão específico
 */
router.get('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const branches = await executeQuery(
    `SELECT b.id, b.code, b.name, b.address, b.phone, b.email,
            b.manager_id, b.is_active, b.created_at,
            u.full_name as manager_name, u.email as manager_email
     FROM branches b
     LEFT JOIN users u ON b.manager_id = u.id
     WHERE b.id = ?`,
    [id]
  );
  
  if (!branches || branches.length === 0) {
    return next(new AppError('Balcão não encontrado', 404, 'BRANCH_NOT_FOUND'));
  }
  
  res.status(200).json({
    status: 'success',
    data: {
      branch: branches[0]
    }
  });
}));

/**
 * POST /api/branches
 * Criar novo balcão (apenas Admin e Gerente)
 */
router.post('/', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = createBranchSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }
  
  const { code, name, address, phone, email, manager_id } = value;
  
  // 2. Verificar se código já existe
  const existingBranches = await executeQuery(
    'SELECT id FROM branches WHERE code = ?',
    [code]
  );
  
  if (existingBranches && existingBranches.length > 0) {
    return next(new AppError('Código de balcão já está em uso', 409, 'CODE_EXISTS'));
  }
  
  // 3. Se manager_id foi fornecido, verificar se existe e não é gerente de outro balcão
  if (manager_id) {
    const managers = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND role_id = (SELECT id FROM roles WHERE name = "gerente")',
      [manager_id]
    );
    
    if (!managers || managers.length === 0) {
      return next(new AppError('Gerente não encontrado ou não tem perfil de gerente', 404, 'MANAGER_NOT_FOUND'));
    }
    
    const existingManagerBranches = await executeQuery(
      'SELECT id FROM branches WHERE manager_id = ? AND id != ?',
      [manager_id, 0]
    );
    
    if (existingManagerBranches && existingManagerBranches.length > 0) {
      return next(new AppError('Este gerente já está atribuído a outro balcão', 409, 'MANAGER_ALREADY_ASSIGNED'));
    }
  }
  
  // 4. Criar balcão
  const result = await executeQuery(
    `INSERT INTO branches (code, name, address, phone, email, manager_id, is_active) 
     VALUES (?, ?, ?, ?, ?, ?, ?)`,
    [code, name, address || null, phone || null, email || null, manager_id || null, true]
  );
  
  // 5. Obter dados completos do balcão criado
  const newBranches = await executeQuery(
    `SELECT b.id, b.code, b.name, b.address, b.phone, b.email, 
            b.is_active, b.created_at,
            u.full_name as manager_name, u.email as manager_email
     FROM branches b 
     LEFT JOIN users u ON b.manager_id = u.id 
     WHERE b.id = ?`,
    [result.insertId]
  );
  
  logger.info(`Novo balcão criado: ${code} - ${name}`, { 
    branchId: result.insertId, 
    createdBy: req.user.id,
    code,
    name
  });
  
  res.status(201).json({
    status: 'success',
    message: 'Balcão criado com sucesso',
    data: {
      branch: newBranches[0]
    }
  });
}));

/**
 * PUT /api/branches/:id
 * Atualizar balcão (apenas Admin e Gerente)
 */
router.put('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // 1. Validar dados de entrada
  const { error, value } = updateBranchSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }
  
  // 2. Verificar se balcão existe
  const existingBranches = await executeQuery('SELECT id, code FROM branches WHERE id = ?', [id]);
  if (!existingBranches || existingBranches.length === 0) {
    return next(new AppError('Balcão não encontrado', 404, 'BRANCH_NOT_FOUND'));
  }
  
  // 3. Se código está sendo alterado, verificar se não existe
  if (value.code && value.code !== existingBranches[0].code) {
    const codeExists = await executeQuery(
      'SELECT id FROM branches WHERE code = ? AND id != ?',
      [value.code, id]
    );
    
    if (codeExists && codeExists.length > 0) {
      return next(new AppError('Código de balcão já está em uso', 409, 'CODE_EXISTS'));
    }
  }
  
  // 4. Se manager_id está sendo alterado, verificar se existe e não é gerente de outro balcão
  if (value.manager_id) {
    const managers = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND role_id = (SELECT id FROM roles WHERE name = "gerente")',
      [value.manager_id]
    );
    
    if (!managers || managers.length === 0) {
      return next(new AppError('Gerente não encontrado ou não tem perfil de gerente', 404, 'MANAGER_NOT_FOUND'));
    }
    
    const existingManagerBranches = await executeQuery(
      'SELECT id FROM branches WHERE manager_id = ? AND id != ?',
      [value.manager_id, id]
    );
    
    if (existingManagerBranches && existingManagerBranches.length > 0) {
      return next(new AppError('Este gerente já está atribuído a outro balcão', 409, 'MANAGER_ALREADY_ASSIGNED'));
    }
  }
  
  // 5. Construir query de atualização
  const updateFields = [];
  const updateValues = [];
  
  Object.keys(value).forEach(key => {
    updateFields.push(`${key} = ?`);
    updateValues.push(value[key]);
  });
  
  if (updateFields.length === 0) {
    return next(new AppError('Nenhum campo para atualizar', 400, 'NO_FIELDS_TO_UPDATE'));
  }
  
  updateValues.push(id);
  
  // 6. Atualizar balcão
  await executeQuery(
    `UPDATE branches SET ${updateFields.join(', ')} WHERE id = ?`,
    updateValues
  );
  
  // 7. Obter dados atualizados
  const updatedBranches = await executeQuery(
    `SELECT b.id, b.code, b.name, b.address, b.phone, b.email,
            b.is_active, b.created_at,
            u.full_name as manager_name, u.email as manager_email
     FROM branches b
     LEFT JOIN users u ON b.manager_id = u.id
     WHERE b.id = ?`,
    [id]
  );
  
  logger.info(`Balcão atualizado: ${id}`, { 
    branchId: id, 
    updatedBy: req.user.id,
    updatedFields: Object.keys(value)
  });
  
  res.status(200).json({
    status: 'success',
    message: 'Balcão atualizado com sucesso',
    data: {
      branch: updatedBranches[0]
    }
  });
}));

/**
 * DELETE /api/branches/:id
 * Remover balcão (apenas Admin)
 */
router.delete('/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // 1. Verificar se balcão existe
  const existingBranches = await executeQuery('SELECT id, name FROM branches WHERE id = ?', [id]);
  if (!existingBranches || existingBranches.length === 0) {
    return next(new AppError('Balcão não encontrado', 404, 'BRANCH_NOT_FOUND'));
  }
  
  // 2. Verificar se há usuários associados ao balcão
  const associatedUsers = await executeQuery('SELECT COUNT(*) as count FROM users WHERE branch_id = ?', [id]);
  if (associatedUsers[0].count > 0) {
    return next(new AppError('Não é possível remover balcão com usuários associados', 409, 'BRANCH_HAS_USERS'));
  }
  
  // 3. Verificar se há caixas associadas ao balcão
  const associatedCashRegisters = await executeQuery('SELECT COUNT(*) as count FROM cash_registers WHERE branch_id = ?', [id]);
  if (associatedCashRegisters[0].count > 0) {
    return next(new AppError('Não é possível remover balcão com caixas associadas', 409, 'BRANCH_HAS_CASH_REGISTERS'));
  }
  
  // 4. Remover balcão
  await executeQuery('DELETE FROM branches WHERE id = ?', [id]);
  
  logger.info(`Balcão removido: ${existingBranches[0].name}`, { 
    branchId: id, 
    deletedBy: req.user.id,
    branchName: existingBranches[0].name
  });
  
  res.status(200).json({
    status: 'success',
    message: 'Balcão removido com sucesso'
  });
}));

module.exports = router;
