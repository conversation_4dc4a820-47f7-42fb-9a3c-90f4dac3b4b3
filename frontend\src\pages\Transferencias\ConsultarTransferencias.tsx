import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import ActionMenu from '@/components/ui/ActionMenu';
import { Search, Edit, Trash2, FileText, Eye, Loader2 } from 'lucide-react';
import { useEnhancedToast } from '@/hooks/useEnhancedToast';
import { useConfirm } from '@/contexts/ConfirmDialogContext';
import { transferService, type TransferResponse, type TransferFilters } from '@/services/transferService';

// Usar interface do serviço
type Transferencia = TransferResponse;

// Remover dados mock - agora vamos buscar do backend

export default function ConsultarTransferencias() {
  const { success, error, info } = useEnhancedToast();
  const confirm = useConfirm();

  // Estados para dados e paginação
  const [transferencias, setTransferencias] = useState<Transferencia[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Estados para filtros
  const [filtros, setFiltros] = useState({
    search: '',
    natureza: 'all',
    status: 'all',
    itemsPorPagina: '10',
    dateFrom: '',
    dateTo: ''
  });

  // Estados para modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTransferencia, setSelectedTransferencia] = useState<Transferencia | null>(null);

  // Função para carregar transferências do backend
  const loadTransferencias = async (page: number = 1) => {
    setLoading(true);
    try {
      const filters: TransferFilters = {
        page,
        limit: parseInt(filtros.itemsPorPagina),
        searchTerm: filtros.search || undefined,
        status: filtros.status !== 'all' ? filtros.status : undefined,
        natureza: filtros.natureza !== 'all' ? filtros.natureza : undefined,
        dateRange: filtros.dateFrom && filtros.dateTo ? {
          from: filtros.dateFrom,
          to: filtros.dateTo
        } : undefined
      };

      const response = await transferService.getTransfers(filters);

      setTransferencias(response.transfers || []);
      setTotalPages(response.totalPages || 1);
      setCurrentPage(response.page || 1);
      setTotalItems(response.total || 0);

    } catch (err: any) {
      console.error('Erro ao carregar transferências:', err);
      // Garantir que os arrays não fiquem undefined em caso de erro
      setTransferencias([]);
      setTotalPages(1);
      setCurrentPage(1);
      setTotalItems(0);

      error({
        title: 'Erro ao carregar transferências',
        description: err.response?.data?.message || 'Ocorreu um erro ao buscar as transferências. Tente novamente.'
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar dados na inicialização e quando filtros mudarem
  useEffect(() => {
    loadTransferencias(1);
  }, [filtros.natureza, filtros.status, filtros.itemsPorPagina, filtros.dateFrom, filtros.dateTo]);

  // Função para pesquisar (com debounce seria ideal, mas por simplicidade...)
  const handleSearch = () => {
    loadTransferencias(1);
  };

  const formatarMoeda = (valor: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(valor);
  };

  const formatarData = (data: string) => {
    return new Date(data).toLocaleDateString('pt-AO');
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'Processada': 'default',
      'Pendente': 'secondary',
      'Cancelada': 'destructive'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status}
      </Badge>
    );
  };

  const handleVisualizarTransferencia = (transferencia: Transferencia) => {
    setSelectedTransferencia(transferencia);
    setIsModalOpen(true);
  };

  const handleEditarTransferencia = (codTransf: string) => {
    info({
      title: 'Funcionalidade em desenvolvimento',
      description: `Edição da transferência ${codTransf} será implementada em breve.`,
    });
  };

  const handleCancelarTransferencia = async (transferencia: Transferencia) => {
    const confirmed = await confirm({
      title: 'Cancelar Transferência',
      description: `Tem certeza que deseja cancelar a transferência ${transferencia.codTransf}? Esta ação não pode ser desfeita.`,
      confirmText: 'Cancelar Transferência',
      cancelText: 'Voltar',
      variant: 'destructive',
    });

    if (confirmed) {
      try {
        await transferService.cancelTransfer(transferencia.id, 'Cancelamento solicitado pelo usuário');

        success({
          title: 'Transferência cancelada',
          description: `A transferência ${transferencia.codTransf} foi cancelada com sucesso.`,
        });

        // Recarregar a lista para refletir as mudanças
        loadTransferencias(currentPage);
      } catch (err: any) {
        console.error('Erro ao cancelar transferência:', err);
        error({
          title: 'Erro ao cancelar',
          description: err.response?.data?.message || 'Não foi possível cancelar a transferência. Tente novamente.',
        });
      }
    }
  };

  const handleGerarRelatorio = (codTransf: string) => {
    success({
      title: 'Relatório gerado',
      description: `Relatório da transferência ${codTransf} foi gerado e enviado para download.`,
    });
  };

  // Remover filtro local - agora é feito no backend

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight dark:text-gray-100">Consultar Transferências</h1>
          <p className="text-muted-foreground dark:text-gray-400">
            Consulte e gerencie todas as transferências realizadas
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {loading ? '...' : totalItems} transferências
        </Badge>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros de Pesquisa</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="space-y-2 md:col-span-2">
              <label className="text-sm font-medium">Pesquisar</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Código, conta ou destinatário..."
                  value={filtros.search}
                  onChange={(e) => setFiltros(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Data Início</label>
              <Input
                type="date"
                value={filtros.dateFrom}
                onChange={(e) => setFiltros(prev => ({ ...prev, dateFrom: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Data Fim</label>
              <Input
                type="date"
                value={filtros.dateTo}
                onChange={(e) => setFiltros(prev => ({ ...prev, dateTo: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Natureza</label>
              <Select value={filtros.natureza} onValueChange={(value) => setFiltros(prev => ({ ...prev, natureza: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Todas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  <SelectItem value="kwanza-aoa">Kwanza - AOA</SelectItem>
                  <SelectItem value="us-dollar-usd">US Dollar - USD</SelectItem>
                  <SelectItem value="gbp-pound">Libra Esterlina - GBP</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={filtros.status} onValueChange={(value) => setFiltros(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="Processada">Processada</SelectItem>
                  <SelectItem value="Pendente">Pendente</SelectItem>
                  <SelectItem value="Cancelada">Cancelada</SelectItem>
                  <SelectItem value="Rejeitada">Rejeitada</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Itens por página</label>
              <Select value={filtros.itemsPorPagina} onValueChange={(value) => setFiltros(prev => ({ ...prev, itemsPorPagina: value }))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={handleSearch} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Pesquisando...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Pesquisar
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Transferências */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Transferências Encontradas ({transferencias.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cód Transf</TableHead>
                  <TableHead>Nº Conta Origem</TableHead>
                  <TableHead>Natureza</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead>Destinatário</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Ação</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Carregando transferências...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : transferencias.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      Nenhuma transferência encontrada
                    </TableCell>
                  </TableRow>
                ) : (
                  transferencias.map((transferencia) => (
                    <TableRow key={transferencia.id}>
                      <TableCell className="font-medium">{transferencia.codTransf}</TableCell>
                      <TableCell>{transferencia.nrContaOrigem}</TableCell>
                      <TableCell>{transferencia.natureza}</TableCell>
                      <TableCell className="font-medium text-green-600">
                        {formatarMoeda(transferencia.valor)}
                      </TableCell>
                      <TableCell>{formatarData(transferencia.data)}</TableCell>
                      <TableCell>{transferencia.destinatario}</TableCell>
                      <TableCell>{getStatusBadge(transferencia.status)}</TableCell>
                      <TableCell>
                        <ActionMenu
                          items={[
                            {
                              label: 'Visualizar',
                              icon: Eye,
                              onClick: () => handleVisualizarTransferencia(transferencia)
                            },
                            {
                              label: 'Gerar Relatório',
                              icon: FileText,
                              onClick: () => handleGerarRelatorio(transferencia.codTransf),
                              separator: true
                            },
                            {
                              label: 'Editar',
                              icon: Edit,
                              onClick: () => handleEditarTransferencia(transferencia.codTransf),
                              disabled: transferencia.status === 'Processada'
                            },
                            {
                              label: 'Cancelar',
                              icon: Trash2,
                              onClick: () => handleCancelarTransferencia(transferencia),
                              variant: 'destructive',
                              disabled: transferencia.status === 'Processada' || transferencia.status === 'Cancelada'
                            }
                          ]}
                        />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>

        {/* Paginação */}
        {!loading && totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-muted-foreground">
              Mostrando {((currentPage - 1) * parseInt(filtros.itemsPorPagina)) + 1} a {Math.min(currentPage * parseInt(filtros.itemsPorPagina), totalItems)} de {totalItems} transferências
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadTransferencias(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                Anterior
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + Math.max(1, currentPage - 2);
                  if (page > totalPages) return null;
                  return (
                    <Button
                      key={page}
                      variant={page === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => loadTransferencias(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadTransferencias(currentPage + 1)}
                disabled={currentPage >= totalPages}
              >
                Próxima
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Modal de Detalhes da Transferência */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-gray-100">Detalhes da Transferência</DialogTitle>
          </DialogHeader>
          {selectedTransferencia && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Código</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.codTransf}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Status</label>
                  <p className="text-sm">{getStatusBadge(selectedTransferencia.status)}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Conta Origem</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.nrContaOrigem}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Natureza</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.natureza}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Destinatário</label>
                <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.destinatario}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Valor</label>
                  <p className="text-sm font-bold text-green-600">{formatarMoeda(selectedTransferencia.valor)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Data</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{formatarData(selectedTransferencia.data)}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
