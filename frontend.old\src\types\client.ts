// Tipos para gestão de clientes

export interface ClientAddress {
  id?: number;
  address_type: 'residential' | 'commercial' | 'correspondence';
  province: string;
  municipality: string;
  neighborhood?: string;
  street: string;
  house_number?: string;
  postal_code?: string;
  is_primary: boolean;
}

export interface ClientContact {
  id?: number;
  contact_type: 'phone_personal' | 'phone_work' | 'email_personal' | 'email_work';
  contact_value: string;
  is_primary: boolean;
}

export interface ClientDocument {
  id?: number;
  document_type: string;
  file_name: string;
  upload_date: string;
  uploaded_by_name: string;
}

export interface Client {
  id: string;
  client_type: 'individual' | 'company';
  full_name?: string;
  company_name?: string;
  document_type: string;
  document_number: string;
  nif?: string;
  birth_date?: string;
  incorporation_date?: string;
  nationality: string;
  gender?: 'M' | 'F';
  marital_status?: string;
  profession?: string;
  monthly_income?: number;
  status: 'active' | 'inactive' | 'blocked' | 'pending' | 'rejected';
  created_at: string;
  updated_at: string;
  branch_id: number;
  branch_name: string;
  branch_code?: string;
  created_by_name: string;
  addresses?: ClientAddress[];
  contacts?: ClientContact[];
  documents?: ClientDocument[];
  accounts?: any[];
  primary_contact?: {
    phone?: string;
    email?: string;
  };
  primary_address?: {
    province: string;
    municipality: string;
    street: string;
  };
}

export interface ClientListResponse {
  clients: Client[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    records_per_page: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

export interface ClientFilters {
  search?: string;
  client_type?: 'individual' | 'company';
  status?: 'active' | 'inactive' | 'blocked';
  branch_id?: number;
  start_date?: string;
  end_date?: string;
  page: number;
  limit: number;
}

export interface IndividualClientForm {
  full_name: string;
  document_type: 'BI' | 'Passaporte' | 'Cedula';
  document_number: string;
  nif?: string;
  birth_date: string;
  nationality: string;
  gender: 'M' | 'F';
  marital_status?: 'Solteiro' | 'Casado' | 'Divorciado' | 'Viúvo' | 'União de Facto';
  profession?: string;
  monthly_income?: number;
  branch_id: number;
  address: {
    province: string;
    municipality: string;
    neighborhood?: string;
    street: string;
    house_number?: string;
    postal_code?: string;
  };
  contacts: {
    phone_personal?: string;
    phone_work?: string;
    email_personal?: string;
    email_work?: string;
  };
}

export interface CompanyClientForm {
  company_name: string;
  document_type: 'NIF' | 'Alvará';
  document_number: string;
  nif: string;
  incorporation_date: string;
  nationality: string;
  profession?: string;
  monthly_income?: number;
  branch_id: number;
  address: {
    province: string;
    municipality: string;
    neighborhood?: string;
    street: string;
    house_number?: string;
    postal_code?: string;
  };
  contacts: {
    phone_personal?: string;
    phone_work?: string;
    email_personal?: string;
    email_work?: string;
  };
}

export interface ClientStats {
  total_clients: number;
  active_clients: number;
  new_this_month: number;
  individual_clients: number;
  company_clients: number;
}
