import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Building2, ArrowRight } from 'lucide-react';

interface NewClientModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewClientModal: React.FC<NewClientModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [selectedType, setSelectedType] = useState<'individual' | 'company' | null>(null);

  const handleTypeSelection = (type: 'individual' | 'company') => {
    setSelectedType(type);
    // Fechar o modal e redirecionar para a página apropriada
    onClose();
    
    if (type === 'individual') {
      window.location.href = '/clientes/abrir-conta-particular';
    } else {
      window.location.href = '/clientes/abrir-conta-empresa';
    }
  };

  const handleClose = () => {
    setSelectedType(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Novo Cliente</DialogTitle>
        </DialogHeader>

        <div className="py-6">
          <p className="text-gray-600 mb-6 text-center">
            Selecione o tipo de cliente que deseja criar:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Cliente Individual */}
            <Card 
              className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-500"
              onClick={() => handleTypeSelection('individual')}
            >
              <CardHeader className="text-center">
                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <User className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-xl">Cliente Individual</CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-gray-600">
                  Para pessoas físicas que desejam abrir uma conta particular.
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• Conta Poupança</p>
                  <p>• Conta Corrente</p>
                  <p>• Conta Salário</p>
                  <p>• Conta Jovem</p>
                </div>
                <Button className="w-full mt-4">
                  <User className="h-4 w-4 mr-2" />
                  Criar Cliente Individual
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>

            {/* Cliente Empresa */}
            <Card 
              className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-purple-500"
              onClick={() => handleTypeSelection('company')}
            >
              <CardHeader className="text-center">
                <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                  <Building2 className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle className="text-xl">Cliente Empresa</CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-gray-600">
                  Para pessoas jurídicas e empresas que desejam abrir uma conta empresarial.
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• Conta Corrente Empresarial</p>
                  <p>• Conta Poupança Empresarial</p>
                  <p>• Conta de Investimento</p>
                  <p>• Linhas de Crédito</p>
                </div>
                <Button className="w-full mt-4" variant="secondary">
                  <Building2 className="h-4 w-4 mr-2" />
                  Criar Cliente Empresa
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Informação:</strong> Após selecionar o tipo de cliente, você será redirecionado 
              para o formulário completo de cadastro onde poderá inserir todas as informações 
              necessárias, incluindo documentos, endereços e contactos.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancelar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NewClientModal;
