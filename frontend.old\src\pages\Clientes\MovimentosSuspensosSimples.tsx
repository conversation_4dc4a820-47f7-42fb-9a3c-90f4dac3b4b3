import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';

const MovimentosSuspensosSimples = () => {
  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <AlertTriangle className="h-8 w-8 text-yellow-600" />
          Movimentos Suspensos
        </h1>
        <p className="text-gray-600 dark:text-gray-400">Gestão de movimentos bancários em análise</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">2</div>
              <div className="text-sm text-gray-600">Pendentes</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">1</div>
              <div className="text-sm text-gray-600">Em Análise</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">1</div>
              <div className="text-sm text-gray-600">Aprovados</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">1</div>
              <div className="text-sm text-gray-600">Rejeitados</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Lista de Movimentos Suspensos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">João Manuel Silva</h3>
                  <p className="text-sm text-gray-600">Conta: 0001 2345 6789 0123</p>
                  <p className="text-sm text-gray-600">Transferência - 2.500.000 Kz</p>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Pendente
                  </span>
                  <p className="text-sm text-gray-600 mt-1">Valor acima do limite diário</p>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Maria Fernanda Santos</h3>
                  <p className="text-sm text-gray-600">Conta: 0001 2345 6789 0456</p>
                  <p className="text-sm text-gray-600">Levantamento - 1.800.000 Kz</p>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Em Análise
                  </span>
                  <p className="text-sm text-gray-600 mt-1">Documentação em falta</p>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Pedro António Gomes</h3>
                  <p className="text-sm text-gray-600">Conta: 0001 2345 6789 0789</p>
                  <p className="text-sm text-gray-600">Depósito - 5.000.000 Kz</p>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Rejeitado
                  </span>
                  <p className="text-sm text-gray-600 mt-1">Origem dos fundos não comprovada</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MovimentosSuspensosSimples;
