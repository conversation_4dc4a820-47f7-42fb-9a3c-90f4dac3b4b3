const express = require('express');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');
const { catchAsync } = require('../core/errorHandler');
const { AppError } = require('../core/errorHandler');
const logger = require('../core/logger');
const vaultService = require('../services/vaultService');

const router = express.Router();

// Schema de validação para saldo inicial
const initialBalanceSchema = Joi.object({
  amount: Joi.number().min(0).max(999999999.99).required(),
  notes: Joi.string().min(10).max(500).optional().allow('')
});

/**
 * GET /api/system/vault/balance
 * Obter saldo atual do cofre principal
 */
router.get('/vault/balance', authorize('admin'), catchAsync(async (req, res, next) => {
  try {
    // Buscar saldo atual do cofre principal na tabela main_vaults
    const vaults = await executeQuery(
      'SELECT current_balance, updated_at FROM main_vaults WHERE is_active = 1 LIMIT 1'
    );

    if (!vaults || vaults.length === 0) {
      return next(new AppError('Cofre principal não encontrado', 404, 'VAULT_NOT_FOUND'));
    }

    const vault = vaults[0];
    const currentBalance = parseFloat(vault.current_balance) || 0;

    res.status(200).json({
      status: 'success',
      data: {
        balance: currentBalance,
        currency: 'AOA',
        last_updated: vault.updated_at
      }
    });

  } catch (error) {
    logger.error('Erro ao obter saldo do cofre:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * POST /api/system/vault/initial-balance
 * Definir saldo inicial do cofre principal (apenas Admin)
 * COMPORTAMENTO: Operação SET - substitui completamente o saldo atual
 */
router.post('/vault/initial-balance', authorize('admin'), catchAsync(async (req, res, next) => {
  try {
    // Validar dados de entrada
    const { error, value } = initialBalanceSchema.validate(req.body);
    if (error) {
      return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
    }

    const { amount, notes } = value;
    const userId = req.user.id;
    const userName = req.user.full_name || req.user.name;

    // 1. Verificar se existe cofre principal ativo
    const vaults = await executeQuery(
      'SELECT id, vault_code, vault_name, current_balance FROM main_vaults WHERE is_active = 1 LIMIT 1'
    );

    if (!vaults || vaults.length === 0) {
      return next(new AppError('Cofre principal não encontrado', 404, 'VAULT_NOT_FOUND'));
    }

    const vault = vaults[0];
    const previousBalance = parseFloat(vault.current_balance);
    const newBalance = amount; // Operação SET - substitui completamente
    const referenceNumber = `INIT-${Date.now()}`;

    // 2. Verificar configuração existente para auditoria
    const existingBalance = await executeQuery(
      'SELECT * FROM system_settings WHERE setting_key = ?',
      ['vault_initial_balance']
    );

    try {
      // 3. Iniciar transação
      await executeQuery('START TRANSACTION');

      // 4. Atualizar saldo do cofre principal (operação SET)
      await executeQuery(
        'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
        [newBalance, vault.id]
      );

      // 5. Registar movimento no cofre
      await executeQuery(
        `INSERT INTO vault_movements (
          vault_id, movement_type, amount, previous_balance, new_balance,
          source_type, source_id, reference_number, description, notes,
          denominations, user_id, processed_by
        ) VALUES (?, 'deposit', ?, ?, ?, 'system', ?, ?, ?, ?, ?, ?, ?)`,
        [
          vault.id,
          newBalance, // Para saldo inicial, o amount é o valor total
          previousBalance,
          newBalance,
          'ADMIN-INITIAL-BALANCE',
          referenceNumber,
          `Definição de saldo inicial do cofre por ${userName}`,
          notes || null,
          null, // denominations - não aplicável para saldo inicial
          userId, // user_id
          userId  // processed_by
        ]
      );

      // 6. Atualizar/criar configuração no system_settings
      let query, params;
      if (existingBalance.length > 0) {
        query = `
          UPDATE system_settings
          SET setting_value = ?, updated_at = NOW(), updated_by = ?
          WHERE setting_key = ?
        `;
        params = [amount.toString(), userId, 'vault_initial_balance'];
      } else {
        query = `
          INSERT INTO system_settings (setting_key, setting_value, updated_by, updated_at)
          VALUES (?, ?, ?, NOW())
        `;
        params = ['vault_initial_balance', amount.toString(), userId];
      }
      await executeQuery(query, params);

      // 7. Registar na auditoria
      await executeQuery(`
        INSERT INTO audit_logs (
          user_id, action, table_name, record_id,
          old_values, new_values, ip_address, user_agent, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        userId,
        'VAULT_INITIAL_BALANCE_SET',
        'main_vaults',
        vault.id.toString(),
        JSON.stringify({
          previous_balance: previousBalance,
          vault_code: vault.vault_code,
          vault_name: vault.vault_name
        }),
        JSON.stringify({
          new_balance: newBalance,
          notes: notes || null,
          reference_number: referenceNumber
        }),
        req.ip,
        req.get('User-Agent')
      ]);

      // 8. Confirmar transação
      await executeQuery('COMMIT');

      logger.info(`Saldo inicial do cofre definido: ${previousBalance} AOA → ${newBalance} AOA por ${userName} (ID: ${userId})`);

      res.status(200).json({
        status: 'success',
        message: 'Saldo inicial definido com sucesso',
        data: {
          vault: {
            id: vault.id,
            code: vault.vault_code,
            name: vault.vault_name
          },
          previous_balance: previousBalance,
          new_balance: newBalance,
          currency: 'AOA',
          notes: notes || null,
          set_by: userName,
          reference_number: referenceNumber,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      // Rollback em caso de erro
      await executeQuery('ROLLBACK');
      logger.error('Erro ao processar definição de saldo inicial:', error);
      throw error;
    }

  } catch (error) {
    logger.error('Erro ao definir saldo inicial:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/settings
 * Obter configurações do sistema (Admin, Gerente)
 */
router.get('/settings', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  try {
    const settings = await executeQuery(
      'SELECT setting_key, setting_value, updated_at FROM system_settings ORDER BY setting_key'
    );

    const settingsMap = {};
    settings.forEach(setting => {
      settingsMap[setting.setting_key] = {
        value: setting.setting_value,
        updated_at: setting.updated_at
      };
    });

    res.status(200).json({
      status: 'success',
      data: { settings: settingsMap }
    });

  } catch (error) {
    logger.error('Erro ao obter configurações do sistema:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults
 * Obter todos os cofres principais (Admin, Gerente)
 */
router.get('/vaults', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  try {
    const { branch_id, is_active } = req.query;
    
    const filters = {};
    if (branch_id) filters.branch_id = branch_id;
    if (is_active !== undefined) filters.is_active = is_active === 'true';

    const vaults = await vaultService.getAllVaults(filters);

    res.status(200).json({
      status: 'success',
      data: {
        vaults,
        total: vaults.length
      }
    });

  } catch (error) {
    logger.error('Erro ao obter cofres principais:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults/:id
 * Obter cofre específico por ID (Admin, Gerente)
 */
router.get('/vaults/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const vault = await vaultService.getVaultById(parseInt(id));
    
    if (!vault) {
      return next(new AppError('Cofre não encontrado', 404, 'VAULT_NOT_FOUND'));
    }

    res.status(200).json({
      status: 'success',
      data: { vault }
    });

  } catch (error) {
    logger.error('Erro ao obter cofre por ID:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults/:id/movements
 * Obter movimentos de um cofre específico (Admin, Gerente)
 */
router.get('/vaults/:id/movements', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  try {
    const { id } = req.params;
    const { page, limit, movement_type, date_from, date_to } = req.query;
    
    const filters = {
      page: page ? parseInt(page) : 1,
      limit: limit ? parseInt(limit) : 20,
      movementType: movement_type,
      dateFrom: date_from,
      dateTo: date_to
    };

    const result = await vaultService.getVaultMovements(parseInt(id), filters);

    res.status(200).json({
      status: 'success',
      data: result
    });

  } catch (error) {
    logger.error('Erro ao obter movimentos do cofre:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults/:id/statistics
 * Obter estatísticas de um cofre específico (Admin, Gerente, Tesoureiro)
 */
router.get('/vaults/:id/statistics', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  try {
    const { id } = req.params;
    const { period = 'month' } = req.query;
    
    const vault = await vaultService.getVaultById(parseInt(id));
    if (!vault) {
      return next(new AppError('Cofre não encontrado', 404, 'VAULT_NOT_FOUND'));
    }

    const statistics = await vaultService.getVaultStatistics(parseInt(id), period);

    res.status(200).json({
      status: 'success',
      data: {
        vault: {
          id: vault.id,
          name: vault.vault_name,
          current_balance: vault.current_balance,
          branch_name: vault.branch_name
        },
        statistics,
        period
      }
    });

  } catch (error) {
    logger.error('Erro ao obter estatísticas do cofre:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

module.exports = router;
