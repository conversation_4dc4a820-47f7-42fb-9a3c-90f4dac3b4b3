{"name": "twins-bank-monorepo", "version": "1.0.0", "description": "Sistema Bancário Twins_Bank - Monorepo com Frontend e Backend", "private": true, "type": "module", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules", "lint": "cd frontend && npm run lint", "test": "cd backend && npm test"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "author": "Twins_Bank Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/twins-bank.git"}, "keywords": ["banking", "financial", "react", "nodejs", "typescript", "mysql"]}