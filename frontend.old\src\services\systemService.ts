import { makeRequest } from '@/utils/api';

// Interfaces para o sistema
export interface VaultBalance {
  balance: number;
  currency: string;
  last_updated: string | null;
}

export interface InitialBalanceRequest {
  amount: number;
  notes?: string;
}

export interface InitialBalanceResponse {
  balance: number;
  currency: string;
  notes: string | null;
  updated_by: string;
  updated_at: string;
}

export interface VaultHistoryEntry {
  id: string;
  action: string;
  previous_balance: number;
  new_balance: number;
  notes: string | null;
  updated_by: string;
  updated_by_email: string;
  created_at: string;
}

export interface VaultHistoryResponse {
  history: VaultHistoryEntry[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SystemSetting {
  value: string;
  description: string;
  updated_at: string;
}

export interface SystemSettingsResponse {
  settings: Record<string, SystemSetting>;
}

class SystemService {
  // Obter saldo atual do cofre
  async getVaultBalance(): Promise<VaultBalance> {
    const response = await makeRequest<{ balance: number; currency: string; last_updated: string | null }>(
      '/system/vault/balance'
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao obter saldo do cofre');
  }

  // Definir saldo inicial do cofre
  async setInitialBalance(data: InitialBalanceRequest): Promise<InitialBalanceResponse> {
    const response = await makeRequest<InitialBalanceResponse>('/system/vault/initial-balance', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao definir saldo inicial');
  }

  // Obter histórico de alterações do cofre
  async getVaultHistory(filters: {
    page?: number;
    limit?: number;
  } = {}): Promise<VaultHistoryResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await makeRequest<VaultHistoryResponse>(
      `/system/vault/history?${queryParams.toString()}`
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao obter histórico do cofre');
  }

  // Obter configurações do sistema
  async getSystemSettings(): Promise<SystemSettingsResponse> {
    const response = await makeRequest<SystemSettingsResponse>('/system/settings');

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao obter configurações do sistema');
  }

  // Formatação de moeda
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  }

  // Formatação de data
  formatDate(dateString: string): string {
    return new Intl.DateTimeFormat('pt-AO', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(new Date(dateString));
  }

  // Validação de valor
  validateAmount(amount: number): { isValid: boolean; error?: string } {
    if (isNaN(amount) || amount < 0) {
      return { isValid: false, error: 'Valor deve ser um número positivo' };
    }

    if (amount > 999999999.99) {
      return { isValid: false, error: 'Valor não pode exceder 999.999.999,99 Kz' };
    }

    return { isValid: true };
  }

  // Validação de observações
  validateNotes(notes: string): { isValid: boolean; error?: string } {
    if (notes && notes.trim().length > 0 && notes.trim().length < 10) {
      return { isValid: false, error: 'Observações devem ter pelo menos 10 caracteres' };
    }

    if (notes && notes.length > 500) {
      return { isValid: false, error: 'Observações não podem exceder 500 caracteres' };
    }

    return { isValid: true };
  }

  // Calcular diferença entre saldos
  calculateBalanceDifference(oldBalance: number, newBalance: number): {
    difference: number;
    percentage: number;
    type: 'increase' | 'decrease' | 'same';
  } {
    const difference = newBalance - oldBalance;
    const percentage = oldBalance > 0 ? (difference / oldBalance) * 100 : 0;
    
    let type: 'increase' | 'decrease' | 'same' = 'same';
    if (difference > 0) type = 'increase';
    else if (difference < 0) type = 'decrease';

    return {
      difference: Math.abs(difference),
      percentage: Math.abs(percentage),
      type
    };
  }

  // Obter estatísticas do cofre
  async getVaultStatistics(): Promise<{
    currentBalance: number;
    totalOperations: number;
    lastUpdate: string | null;
    averageOperation: number;
  }> {
    try {
      const [balance, history] = await Promise.all([
        this.getVaultBalance(),
        this.getVaultHistory({ limit: 100 })
      ]);

      const totalOperations = history.pagination.total;
      const averageOperation = totalOperations > 0 
        ? history.history.reduce((sum, entry) => sum + entry.new_balance, 0) / totalOperations
        : 0;

      return {
        currentBalance: balance.balance,
        totalOperations,
        lastUpdate: balance.last_updated,
        averageOperation
      };
    } catch (error) {
      throw new Error('Erro ao obter estatísticas do cofre');
    }
  }
}

// Instância singleton do serviço
export const systemService = new SystemService();
export default systemService;
