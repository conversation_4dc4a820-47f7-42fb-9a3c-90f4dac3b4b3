import React, { useState } from 'react';
import { Filter, X, Search, Calendar, DollarSign, User, FileText, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

interface FilterCriteria {
  searchTerm: string;
  dateRange: {
    from: string;
    to: string;
  };
  transactionType: string;
  amountRange: {
    min: string;
    max: string;
  };
  user: string;
  status: string;
  accountType: string;
}

interface AdvancedSearchModalProps {
  onSearch: (criteria: FilterCriteria) => void;
  activeFilters: number;
}

const AdvancedSearchModal: React.FC<AdvancedSearchModalProps> = ({ onSearch, activeFilters }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<FilterCriteria>({
    searchTerm: '',
    dateRange: { from: '', to: '' },
    transactionType: '',
    amountRange: { min: '', max: '' },
    user: '',
    status: '',
    accountType: '',
  });

  const handleFilterChange = (key: keyof FilterCriteria, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSearch = () => {
    onSearch(filters);
    setIsOpen(false);
  };

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      dateRange: { from: '', to: '' },
      transactionType: '',
      amountRange: { min: '', max: '' },
      user: '',
      status: '',
      accountType: '',
    });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.dateRange.from || filters.dateRange.to) count++;
    if (filters.transactionType) count++;
    if (filters.amountRange.min || filters.amountRange.max) count++;
    if (filters.user) count++;
    if (filters.status) count++;
    if (filters.accountType) count++;
    return count;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className="relative h-8 w-8 p-0 hover:bg-twins-accent/50"
          aria-label="Filtros avançados"
        >
          <Filter className="h-4 w-4" />
          {activeFilters > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {activeFilters > 9 ? '9+' : activeFilters}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto dark:bg-gray-900 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 dark:text-gray-100">
            <Filter className="h-5 w-5 text-twins-primary" />
            Pesquisa Avançada
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Termo de Pesquisa */}
          <div className="space-y-2">
            <Label htmlFor="searchTerm" className="flex items-center gap-2 dark:text-gray-200">
              <Search className="h-4 w-4" />
              Termo de Pesquisa
            </Label>
            <Input
              id="searchTerm"
              placeholder="Pesquisar por nome, número de conta, descrição..."
              value={filters.searchTerm}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
              className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 dark:placeholder-gray-400"
            />
          </div>

          {/* Intervalo de Datas */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 dark:text-gray-200">
              <Calendar className="h-4 w-4" />
              Intervalo de Datas
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dateFrom" className="text-sm text-gray-600 dark:text-gray-400">De</Label>
                <Input
                  id="dateFrom"
                  type="date"
                  value={filters.dateRange.from}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, from: e.target.value })}
                  className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100"
                />
              </div>
              <div>
                <Label htmlFor="dateTo" className="text-sm text-gray-600 dark:text-gray-400">Até</Label>
                <Input
                  id="dateTo"
                  type="date"
                  value={filters.dateRange.to}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, to: e.target.value })}
                  className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100"
                />
              </div>
            </div>
          </div>

          {/* Tipo de Transação */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 dark:text-gray-200">
              <FileText className="h-4 w-4" />
              Tipo de Transação
            </Label>
            <Select value={filters.transactionType} onValueChange={(value) => handleFilterChange('transactionType', value)}>
              <SelectTrigger className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100">
                <SelectValue placeholder="Selecionar tipo de transação" />
              </SelectTrigger>
              <SelectContent className="dark:bg-gray-800 dark:border-gray-600">
                <SelectItem value="transferencia" className="dark:text-gray-100 dark:hover:bg-gray-700">Transferência</SelectItem>
                <SelectItem value="deposito" className="dark:text-gray-100 dark:hover:bg-gray-700">Depósito</SelectItem>
                <SelectItem value="levantamento" className="dark:text-gray-100 dark:hover:bg-gray-700">Levantamento</SelectItem>
                <SelectItem value="pagamento" className="dark:text-gray-100 dark:hover:bg-gray-700">Pagamento</SelectItem>
                <SelectItem value="cambio" className="dark:text-gray-100 dark:hover:bg-gray-700">Câmbio</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Intervalo de Valores */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 dark:text-gray-200">
              <DollarSign className="h-4 w-4" />
              Intervalo de Valores (Kz)
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="amountMin" className="text-sm text-gray-600 dark:text-gray-400">Valor Mínimo</Label>
                <Input
                  id="amountMin"
                  type="number"
                  placeholder="0"
                  value={filters.amountRange.min}
                  onChange={(e) => handleFilterChange('amountRange', { ...filters.amountRange, min: e.target.value })}
                  className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 dark:placeholder-gray-400"
                />
              </div>
              <div>
                <Label htmlFor="amountMax" className="text-sm text-gray-600 dark:text-gray-400">Valor Máximo</Label>
                <Input
                  id="amountMax"
                  type="number"
                  placeholder="Sem limite"
                  value={filters.amountRange.max}
                  onChange={(e) => handleFilterChange('amountRange', { ...filters.amountRange, max: e.target.value })}
                  className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 dark:placeholder-gray-400"
                />
              </div>
            </div>
          </div>

          {/* Usuário */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 dark:text-gray-200">
              <User className="h-4 w-4" />
              Usuário/Cliente
            </Label>
            <Input
              placeholder="Nome do usuário ou cliente"
              value={filters.user}
              onChange={(e) => handleFilterChange('user', e.target.value)}
              className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 dark:placeholder-gray-400"
            />
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 dark:text-gray-200">
              <CheckCircle className="h-4 w-4" />
              Status
            </Label>
            <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100">
                <SelectValue placeholder="Selecionar status" />
              </SelectTrigger>
              <SelectContent className="dark:bg-gray-800 dark:border-gray-600">
                <SelectItem value="pendente" className="dark:text-gray-100 dark:hover:bg-gray-700">Pendente</SelectItem>
                <SelectItem value="processado" className="dark:text-gray-100 dark:hover:bg-gray-700">Processado</SelectItem>
                <SelectItem value="cancelado" className="dark:text-gray-100 dark:hover:bg-gray-700">Cancelado</SelectItem>
                <SelectItem value="aprovado" className="dark:text-gray-100 dark:hover:bg-gray-700">Aprovado</SelectItem>
                <SelectItem value="rejeitado" className="dark:text-gray-100 dark:hover:bg-gray-700">Rejeitado</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tipo de Conta */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 dark:text-gray-200">
              <FileText className="h-4 w-4" />
              Tipo de Conta
            </Label>
            <Select value={filters.accountType} onValueChange={(value) => handleFilterChange('accountType', value)}>
              <SelectTrigger className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100">
                <SelectValue placeholder="Selecionar tipo de conta" />
              </SelectTrigger>
              <SelectContent className="dark:bg-gray-800 dark:border-gray-600">
                <SelectItem value="particular" className="dark:text-gray-100 dark:hover:bg-gray-700">Particular</SelectItem>
                <SelectItem value="empresa" className="dark:text-gray-100 dark:hover:bg-gray-700">Empresa</SelectItem>
                <SelectItem value="prazo" className="dark:text-gray-100 dark:hover:bg-gray-700">Prazo</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Ações */}
          <div className="flex justify-between pt-4 border-t dark:border-gray-700">
            <Button variant="outline" onClick={clearFilters} className="dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-800">
              <X className="h-4 w-4 mr-2" />
              Limpar Filtros
            </Button>
            <div className="flex gap-2">
              <Button variant="ghost" onClick={() => setIsOpen(false)} className="dark:text-gray-200 dark:hover:bg-gray-800">
                Cancelar
              </Button>
              <Button onClick={handleSearch} className="bg-twins-primary hover:bg-twins-primary/90 text-white">
                <Search className="h-4 w-4 mr-2" />
                Aplicar Filtros
                {getActiveFilterCount() > 0 && (
                  <Badge variant="secondary" className="ml-2 dark:bg-gray-700 dark:text-gray-200">
                    {getActiveFilterCount()}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedSearchModal;
