# Frontend Environment Variables - K-Bank
# Configuração de ambiente para o frontend React/Vite

# URL da API do Backend
# Em desenvolvimento: usar URL local ou deixar vazio para usar proxy do Vite
# Em produção: usar URL completa do backend na Vercel
VITE_API_URL=http://localhost:3001

# Ambiente de execução
VITE_NODE_ENV=development

# Debug mode (para logs adicionais)
VITE_DEBUG=true

# Configurações de produção (comentadas por padrão)
# VITE_API_URL=https://k-bank-backend.vercel.app
# VITE_NODE_ENV=production
# VITE_DEBUG=false
