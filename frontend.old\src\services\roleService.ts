import { API_CONFIG, API_ENDPOINTS, ApiResponse, getAuthHeaders } from '@/config/api';

// Tipos para gestão de roles
export interface Role {
  id: number;
  name: string;
  description: string;
  user_count: number;
  permissions?: string[];
  created_at: string;
  updated_at?: string;
}

export interface CreateRoleRequest {
  name: string;
  description?: string;
  permissions?: string[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];
}

export interface RoleListResponse {
  roles: Role[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface RoleFilters {
  page?: number;
  limit?: number;
  search?: string;
}

// Classe de erro específica para roles
export class RoleError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'RoleError';
  }
}

// Helper para fazer requisições HTTP
const makeRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retries: number = 3,
  delay: number = 1000
): Promise<T> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;

  const config: RequestInit = {
    ...options,
    headers: {
      ...getAuthHeaders(),
      ...options.headers,
    },
  };

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        throw new RoleError(
          errorData.message || `Erro HTTP ${response.status}`,
          errorData.code || 'HTTP_ERROR',
          response.status
        );
      }

      const data = await response.json();
      
      if (data.status === 'error') {
        throw new RoleError(data.message || 'Erro na resposta da API', data.code);
      }

      return data;
    } catch (error) {
      if (attempt === retries) {
        if (error instanceof RoleError) {
          throw error;
        }
        throw new RoleError(
          'Erro de conexão com o servidor',
          'CONNECTION_ERROR'
        );
      }
      
      // Aguardar antes de tentar novamente
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw new RoleError('Falha após múltiplas tentativas', 'MAX_RETRIES_EXCEEDED');
};

/**
 * Serviço para gestão de roles
 */
export class RoleService {
  /**
   * Listar roles com filtros e paginação
   */
  async listRoles(filters: RoleFilters = {}): Promise<RoleListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);

    const queryString = params.toString();
    const endpoint = `${API_ENDPOINTS.ROLES.LIST}${queryString ? `?${queryString}` : ''}`;

    const response = await makeRequest<ApiResponse<RoleListResponse>>(endpoint);
    return response.data!;
  }

  /**
   * Obter role específico por ID
   */
  async getRole(id: number): Promise<Role> {
    const response = await makeRequest<ApiResponse<{ role: Role }>>(
      API_ENDPOINTS.ROLES.GET(id.toString())
    );
    return response.data!.role;
  }

  /**
   * Criar novo role
   */
  async createRole(roleData: CreateRoleRequest): Promise<Role> {
    const response = await makeRequest<ApiResponse<{ role: Role }>>(
      API_ENDPOINTS.ROLES.CREATE,
      {
        method: 'POST',
        body: JSON.stringify(roleData),
      }
    );
    return response.data!.role;
  }

  /**
   * Atualizar role existente
   */
  async updateRole(id: number, roleData: UpdateRoleRequest): Promise<Role> {
    const response = await makeRequest<ApiResponse<{ role: Role }>>(
      API_ENDPOINTS.ROLES.UPDATE(id.toString()),
      {
        method: 'PUT',
        body: JSON.stringify(roleData),
      }
    );
    return response.data!.role;
  }

  /**
   * Remover role
   */
  async deleteRole(id: number): Promise<void> {
    await makeRequest<ApiResponse>(
      API_ENDPOINTS.ROLES.DELETE(id.toString()),
      {
        method: 'DELETE',
      }
    );
  }

  /**
   * Buscar todos os roles (para dropdowns)
   */
  async getAllRoles(): Promise<Role[]> {
    const response = await this.listRoles({ 
      limit: 100 // Buscar todos os roles
    });
    return response.roles;
  }

  /**
   * Obter permissões disponíveis
   */
  async getAvailablePermissions(): Promise<string[]> {
    // Por enquanto, retorna permissões fixas
    // TODO: Implementar API de permissões
    return [
      'users.create',
      'users.read',
      'users.update',
      'users.delete',
      'branches.create',
      'branches.read',
      'branches.update',
      'branches.delete',
      'roles.create',
      'roles.read',
      'roles.update',
      'roles.delete',
      'clients.create',
      'clients.read',
      'clients.update',
      'clients.delete',
      'accounts.create',
      'accounts.read',
      'accounts.update',
      'accounts.delete',
      'transactions.create',
      'transactions.read',
      'transactions.update',
      'transactions.delete',
      'reports.read',
      'system.admin'
    ];
  }

  /**
   * Obter matriz de permissões por role
   */
  getDefaultRolePermissions(): Record<string, string[]> {
    return {
      admin: [
        'users.create', 'users.read', 'users.update', 'users.delete',
        'branches.create', 'branches.read', 'branches.update', 'branches.delete',
        'roles.create', 'roles.read', 'roles.update', 'roles.delete',
        'clients.create', 'clients.read', 'clients.update', 'clients.delete',
        'accounts.create', 'accounts.read', 'accounts.update', 'accounts.delete',
        'transactions.create', 'transactions.read', 'transactions.update', 'transactions.delete',
        'reports.read', 'system.admin'
      ],
      gerente: [
        'users.read', 'users.update',
        'branches.read',
        'clients.create', 'clients.read', 'clients.update',
        'accounts.create', 'accounts.read', 'accounts.update',
        'transactions.read', 'transactions.update',
        'reports.read'
      ],
      caixa: [
        'clients.read',
        'accounts.read',
        'transactions.create', 'transactions.read'
      ],
      tesoureiro: [
        'clients.read',
        'accounts.read', 'accounts.update',
        'transactions.read', 'transactions.update',
        'reports.read'
      ]
    };
  }
}

// Instância singleton do serviço
export const roleService = new RoleService();

// Hook personalizado para React Query (se estiver usando)
export const useRoleQueries = () => {
  return {
    // Query keys para cache
    keys: {
      all: ['roles'] as const,
      lists: () => [...useRoleQueries().keys.all, 'list'] as const,
      list: (filters: RoleFilters) => [...useRoleQueries().keys.lists(), filters] as const,
      details: () => [...useRoleQueries().keys.all, 'detail'] as const,
      detail: (id: number) => [...useRoleQueries().keys.details(), id] as const,
      permissions: () => [...useRoleQueries().keys.all, 'permissions'] as const,
    }
  };
};

export default roleService;
