import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { systemService } from '@/services/systemService';
import { Vault, DollarSign, Shield, AlertTriangle, CheckCircle, Loader2, Info } from 'lucide-react';

interface FormData {
  amount: string;
  notes: string;
}

interface FormErrors {
  amount?: string;
  notes?: string;
}

const SaldoInicial = () => {
  const [formData, setFormData] = useState<FormData>({
    amount: '',
    notes: ''
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentBalance, setCurrentBalance] = useState<number | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState(true);
  
  const { toast } = useToast();

  // Carregar saldo atual ao montar componente
  useEffect(() => {
    loadCurrentBalance();
  }, []);

  const loadCurrentBalance = async () => {
    try {
      setIsLoadingBalance(true);
      const balanceData = await systemService.getVaultBalance();
      setCurrentBalance(balanceData.balance);
    } catch (error: any) {
      console.error('Erro ao carregar saldo atual:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar saldo atual do cofre",
        variant: "destructive"
      });
      setCurrentBalance(0); // Valor padrão em caso de erro
    } finally {
      setIsLoadingBalance(false);
    }
  };

  const formatCurrency = (value: number): string => {
    return systemService.formatCurrency(value);
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpar erro do campo
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validar valor
    if (!formData.amount) {
      newErrors.amount = 'Valor é obrigatório';
    } else {
      const amount = parseFloat(formData.amount);
      const amountValidation = systemService.validateAmount(amount);
      if (!amountValidation.isValid) {
        newErrors.amount = amountValidation.error!;
      }
    }

    // Validar observações
    const notesValidation = systemService.validateNotes(formData.notes);
    if (!notesValidation.isValid) {
      newErrors.notes = notesValidation.error!;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const amount = parseFloat(formData.amount);
      const notes = formData.notes.trim() || undefined;

      const result = await systemService.setInitialBalance({
        amount,
        notes
      });

      toast({
        title: "Sucesso",
        description: `Saldo inicial de ${formatCurrency(result.balance)} definido com sucesso`,
      });

      // Atualizar saldo atual
      setCurrentBalance(result.balance);

      // Limpar formulário
      setFormData({ amount: '', notes: '' });
      setErrors({});

    } catch (error: any) {
      console.error('Erro ao definir saldo inicial:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao definir saldo inicial. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = formData.amount && !Object.keys(errors).length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Saldo Inicial do Cofre</h1>
          <p className="text-gray-600 dark:text-gray-400">Definir saldo inicial para simulação do cofre principal</p>
        </div>
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid || isSubmitting}
          className="flex items-center gap-2"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <CheckCircle className="h-4 w-4" />
          )}
          {isSubmitting ? 'Processando...' : 'Definir Saldo'}
        </Button>
      </div>

      {/* Alerta de Segurança */}
      <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
        <Shield className="h-4 w-4 text-amber-600" />
        <AlertDescription className="text-amber-800 dark:text-amber-200">
          <strong>Atenção:</strong> Esta operação define o saldo inicial do cofre principal para simulação. 
          Apenas administradores podem realizar esta ação e ela será registada no sistema de auditoria.
        </AlertDescription>
      </Alert>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Saldo Atual */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Vault className="h-5 w-5" />
              Saldo Atual do Cofre
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoadingBalance ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-600">Carregando saldo...</span>
              </div>
            ) : (
              <div className="text-center py-4">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {currentBalance !== null ? formatCurrency(currentBalance) : 'N/A'}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                  Saldo disponível no cofre principal
                </p>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg dark:bg-blue-950/20 dark:border-blue-800">
              <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 mb-2">
                <Info className="h-4 w-4" />
                <span className="font-medium">Informação</span>
              </div>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                O saldo inicial é usado para simulações e testes do sistema. 
                Esta funcionalidade permite definir um valor base para o cofre principal.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Formulário */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Definir Novo Saldo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Valor */}
              <div className="space-y-2">
                <Label htmlFor="amount">
                  Valor Inicial (Kz) <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0"
                  max="999999999.99"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  placeholder="0,00"
                  className={errors.amount ? 'border-red-500' : ''}
                />
                {errors.amount && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertTriangle className="h-4 w-4" />
                    {errors.amount}
                  </p>
                )}
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="notes">Observações</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Motivo para definição do saldo inicial..."
                  rows={3}
                  className={errors.notes ? 'border-red-500' : ''}
                />
                {errors.notes && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertTriangle className="h-4 w-4" />
                    {errors.notes}
                  </p>
                )}
                <p className="text-xs text-gray-500">
                  Opcional. Descreva o motivo para esta operação (mínimo 10 caracteres se preenchido).
                </p>
              </div>

              {/* Alerta de Confirmação */}
              <Alert className="border-red-200 bg-red-50 dark:bg-red-950/20">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800 dark:text-red-200">
                  <strong>Confirmação:</strong> Esta ação irá alterar o saldo do cofre principal. 
                  Certifique-se de que o valor está correto antes de confirmar.
                </AlertDescription>
              </Alert>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SaldoInicial;
