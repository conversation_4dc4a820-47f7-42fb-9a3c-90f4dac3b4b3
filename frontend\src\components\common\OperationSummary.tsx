import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calculator, AlertTriangle, CheckCircle } from 'lucide-react';

interface OperationSummaryProps {
  currentBalance: number;
  deliveryAmount: number;
  currency?: string;
  title?: string;
  formatCurrency?: (amount: number) => string;
  showValidation?: boolean;
  className?: string;
}

/**
 * Componente universal "Resumo da Operação"
 * Mostra saldo atual, valor a entregar e saldo restante
 * Inclui validação visual para valores inválidos
 */
export const OperationSummary: React.FC<OperationSummaryProps> = ({
  currentBalance,
  deliveryAmount,
  currency = 'AOA',
  title = 'Resumo da Operação',
  formatCurrency,
  showValidation = true,
  className = ''
}) => {
  // Função padrão para formatação de moeda
  const defaultFormatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount).replace('AOA', 'Kz');
  };

  const formatAmount = formatCurrency || defaultFormatCurrency;
  const remainingBalance = currentBalance - deliveryAmount;
  const isValidAmount = deliveryAmount > 0 && deliveryAmount <= currentBalance;
  const hasInsufficientBalance = deliveryAmount > currentBalance;

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Calculator className="h-5 w-5 text-blue-600" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Resumo dos valores */}
        <div className="grid grid-cols-1 gap-3">
          {/* Saldo Atual */}
          <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg dark:bg-blue-900/20">
            <span className="font-medium text-blue-700 dark:text-blue-300">
              Saldo Atual:
            </span>
            <span className="font-bold text-blue-800 dark:text-blue-200">
              {formatAmount(currentBalance)}
            </span>
          </div>

          {/* Valor a Entregar */}
          <div className={`flex justify-between items-center p-3 rounded-lg ${
            deliveryAmount > 0 
              ? hasInsufficientBalance 
                ? 'bg-red-50 dark:bg-red-900/20' 
                : 'bg-orange-50 dark:bg-orange-900/20'
              : 'bg-gray-50 dark:bg-gray-800'
          }`}>
            <span className={`font-medium ${
              deliveryAmount > 0 
                ? hasInsufficientBalance 
                  ? 'text-red-700 dark:text-red-300' 
                  : 'text-orange-700 dark:text-orange-300'
                : 'text-gray-600 dark:text-gray-400'
            }`}>
              Valor a Entregar:
            </span>
            <span className={`font-bold ${
              deliveryAmount > 0 
                ? hasInsufficientBalance 
                  ? 'text-red-800 dark:text-red-200' 
                  : 'text-orange-800 dark:text-orange-200'
                : 'text-gray-700 dark:text-gray-300'
            }`}>
              {deliveryAmount > 0 ? formatAmount(deliveryAmount) : '0,00 Kz'}
            </span>
          </div>

          {/* Saldo Restante */}
          <div className={`flex justify-between items-center p-3 rounded-lg border-2 ${
            deliveryAmount > 0
              ? hasInsufficientBalance
                ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                : 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
              : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
          }`}>
            <span className={`font-medium ${
              deliveryAmount > 0
                ? hasInsufficientBalance
                  ? 'text-red-700 dark:text-red-300'
                  : 'text-green-700 dark:text-green-300'
                : 'text-gray-600 dark:text-gray-400'
            }`}>
              Saldo Restante:
            </span>
            <span className={`font-bold ${
              deliveryAmount > 0
                ? hasInsufficientBalance
                  ? 'text-red-800 dark:text-red-200'
                  : 'text-green-800 dark:text-green-200'
                : 'text-gray-700 dark:text-gray-300'
            }`}>
              {formatAmount(remainingBalance)}
            </span>
          </div>
        </div>

        {/* Validação Visual */}
        {showValidation && deliveryAmount > 0 && (
          <div className="mt-4">
            {hasInsufficientBalance ? (
              <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-700 dark:text-red-300">
                  <strong>Atenção:</strong> O valor a entregar excede o saldo disponível. 
                  Operação não pode ser realizada.
                </AlertDescription>
              </Alert>
            ) : (
              <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-700 dark:text-green-300">
                  <strong>Validação OK:</strong> Saldo suficiente para realizar a operação.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Informação adicional quando não há valor */}
        {deliveryAmount === 0 && showValidation && (
          <div className="text-center text-gray-500 dark:text-gray-400 text-sm py-2">
            Informe o valor da entrega para ver o resumo da operação
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default OperationSummary;
