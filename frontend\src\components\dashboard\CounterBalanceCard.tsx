import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Wallet, TrendingUp, TrendingDown, RefreshCw, History } from 'lucide-react';
import { counterService, CounterBalance, CounterMovement } from '@/services/counterService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface CounterBalanceCardProps {
  className?: string;
}

export const CounterBalanceCard: React.FC<CounterBalanceCardProps> = ({ className }) => {
  const { user } = useAuth();
  const [balance, setBalance] = useState<CounterBalance | null>(null);
  const [recentMovements, setRecentMovements] = useState<CounterMovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadData = async () => {
    if (!user || user.role !== 'balcao') return;

    try {
      setLoading(true);

      // Carregar saldo e movimentos em paralelo
      const [balanceData, movementsData] = await Promise.all([
        counterService.getBalance(),
        counterService.getMovements(1, 5) // Últimos 5 movimentos
      ]);

      setBalance(balanceData);
      setRecentMovements(movementsData?.movements || []);
    } catch (error: any) {
      console.error('Erro ao carregar dados do balcão:', error);
      toast.error('Erro ao carregar saldo do balcão');
      
      // Definir valores padrão em caso de erro
      setBalance({
        balance: 0,
        user_id: user.id,
        user_name: user.full_name
      });
      setRecentMovements([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
    toast.success('Dados atualizados');
  };

  useEffect(() => {
    loadData();
  }, [user]);

  // Não mostrar o card se o usuário não for do perfil balcão
  if (!user || user.role !== 'balcao') {
    return null;
  }

  if (loading) {
    return (
      <Card className={`hover:shadow-lg transition-shadow duration-200 border-t-4 border-t-green-500 dark:bg-gray-800 ${className}`}>
        <CardHeader className="bg-gradient-to-r from-green-50 to-transparent dark:from-green-900/20">
          <CardTitle className="text-lg font-semibold text-green-700 dark:text-green-400 flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Meu Saldo de Balcão
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 pt-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentBalance = balance?.balance || 0;
  const hasPositiveBalance = currentBalance > 0;

  return (
    <Card className={`hover:shadow-lg transition-shadow duration-200 border-t-4 border-t-green-500 dark:bg-gray-800 ${className}`}>
      <CardHeader className="bg-gradient-to-r from-green-50 to-transparent dark:from-green-900/20">
        <CardTitle className="text-lg font-semibold text-green-700 dark:text-green-400 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Meu Saldo de Balcão
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 pt-6">
        {/* Saldo Atual */}
        <div className="text-center p-4 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Saldo Disponível</p>
          <p className={`text-2xl font-bold ${hasPositiveBalance ? 'text-green-600' : 'text-gray-500'}`}>
            {counterService.formatCurrency(currentBalance)}
          </p>
        </div>

        {/* Movimentos Recentes */}
        {recentMovements.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <History className="h-4 w-4" />
              Últimos Movimentos
            </div>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {recentMovements.map((movement) => (
                <div
                  key={movement.id}
                  className="flex justify-between items-center p-2 rounded bg-gray-50 dark:bg-gray-700 text-xs"
                >
                  <div className="flex items-center gap-2">
                    {movement.movement_type === 'credit' ? (
                      <TrendingUp className="h-3 w-3 text-green-500" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-500" />
                    )}
                    <span className="text-gray-600 dark:text-gray-400 truncate max-w-24">
                      {movement.description.split(' - ')[0]}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className={`font-medium ${
                      movement.movement_type === 'credit' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {movement.movement_type === 'credit' ? '+' : '-'}
                      {counterService.formatCurrency(movement.amount)}
                    </p>
                    <p className="text-gray-500 text-xs">
                      {counterService.formatDate(movement.created_at)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Mensagem quando não há movimentos */}
        {recentMovements.length === 0 && (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Nenhum movimento registado</p>
          </div>
        )}

        {/* Informações do Utilizador */}
        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Operador: {balance?.user_name || user.full_name}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default CounterBalanceCard;
