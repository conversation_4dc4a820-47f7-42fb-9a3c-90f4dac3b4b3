// Authentication Service
// Serviço de autenticação para comunicação com o backend

import { API_CONFIG, API_ENDPOINTS, ApiResponse, ApiError, getAuthHeaders } from '@/config/api';
import { tokenManager } from '@/utils/tokenManager';

// Tipos para autenticação
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: {
    id: string;
    full_name: string;
    email: string;
    role: string;
    branch: {
      id: number;
      name: string;
      code?: string;
    } | null;
    is_active: boolean;
    last_login: string | null;
    created_at: string;
  };
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
  sessionId: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: string;
  sessionId: string;
}

export interface UserProfileResponse {
  user: {
    id: string;
    full_name: string;
    email: string;
    role: string;
    branch: {
      id: number;
      name: string;
      code?: string;
    } | null;
    is_active: boolean;
    last_login: string | null;
    created_at: string;
  };
}

// Classe de erro personalizada para autenticação
export class AuthError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'AuthError';
  }
}

// Helper para fazer requisições HTTP com retry
const makeRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retries: number = 3,
  delay: number = 1000
): Promise<T> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;

  const config: RequestInit = {
    ...options,
    headers: {
      ...API_CONFIG.headers,
      ...options.headers,
    },
  };

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, config);

      // Verificar se a resposta é JSON válida
      let data: ApiResponse<T> | ApiError;
      try {
        data = await response.json();
      } catch (parseError) {
        if (attempt === retries) {
          throw new AuthError(
            'Resposta inválida do servidor',
            'INVALID_RESPONSE',
            response.status
          );
        }
        // Tentar novamente se não for a última tentativa
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
        continue;
      }

      if (!response.ok || data.status === 'error') {
        const errorData = data as ApiError;

        // Não tentar novamente para erros de autenticação
        if (response.status === 401 || response.status === 403) {
          throw new AuthError(
            errorData.message || 'Erro de autenticação',
            errorData.code,
            response.status
          );
        }

        // Tentar novamente para outros erros se não for a última tentativa
        if (attempt < retries && (response.status >= 500 || response.status === 0)) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
          continue;
        }

        throw new AuthError(
          errorData.message || 'Erro na requisição',
          errorData.code,
          response.status
        );
      }

      return (data as ApiResponse<T>).data as T;
    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }

      // Erro de rede - tentar novamente se não for a última tentativa
      if (attempt < retries) {
        console.warn(`Tentativa ${attempt} falhou, tentando novamente em ${delay * attempt}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
        continue;
      }

      // Última tentativa falhou
      throw new AuthError(
        'Erro de conexão com o servidor. Verifique sua conexão e tente novamente.',
        'NETWORK_ERROR'
      );
    }
  }

  // Nunca deve chegar aqui, mas TypeScript precisa
  throw new AuthError('Erro inesperado', 'UNKNOWN_ERROR');
};

// Serviço de autenticação
export const authService = {
  /**
   * Verificar se o backend está disponível
   */
  async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_CONFIG.baseURL}/health`, {
        method: 'GET',
        headers: API_CONFIG.headers,
        signal: AbortSignal.timeout(5000) // 5 segundos timeout
      });
      return response.ok;
    } catch (error) {
      console.warn('Backend health check failed:', error);
      return false;
    }
  },

  /**
   * Realizar login com verificação de saúde do backend
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // Verificar se o backend está disponível antes de tentar login
    const isBackendHealthy = await this.checkBackendHealth();
    if (!isBackendHealthy) {
      throw new AuthError(
        'Servidor temporariamente indisponível. Tente novamente em alguns segundos.',
        'BACKEND_UNAVAILABLE'
      );
    }

    const response = await makeRequest<LoginResponse>(
      API_ENDPOINTS.AUTH.LOGIN,
      {
        method: 'POST',
        body: JSON.stringify(credentials),
      }
    );

    // Armazenar tokens usando o token manager
    if (response.accessToken) {
      tokenManager.storeTokenData({
        accessToken: response.accessToken,
        refreshToken: response.refreshToken,
        sessionId: response.sessionId,
        expiresIn: response.expiresIn
      });
    }

    return response;
  },

  /**
   * Realizar logout
   */
  async logout(): Promise<void> {
    try {
      await makeRequest(
        API_ENDPOINTS.AUTH.LOGOUT,
        {
          method: 'POST',
          headers: getAuthHeaders(),
        }
      );
    } catch (error) {
      // Mesmo se o logout falhar no servidor, limpar dados locais
      console.warn('Erro ao fazer logout no servidor:', error);
    } finally {
      // Limpar dados de autenticação usando o token manager
      tokenManager.clearTokens();
      localStorage.removeItem('twins-bank-user');
    }
  },

  /**
   * Renovar token de acesso
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = tokenManager.getRefreshToken();

    if (!refreshToken) {
      throw new AuthError('Token de refresh não encontrado', 'NO_REFRESH_TOKEN');
    }

    const response = await makeRequest<RefreshTokenResponse>(
      API_ENDPOINTS.AUTH.REFRESH,
      {
        method: 'POST',
        body: JSON.stringify({ refreshToken }),
      }
    );

    // Atualizar token usando o token manager
    if (response.accessToken) {
      tokenManager.storeTokenData({
        accessToken: response.accessToken,
        refreshToken: refreshToken, // Manter o refresh token atual
        sessionId: response.sessionId,
        expiresIn: response.expiresIn
      });
    }

    return response;
  },

  /**
   * Obter perfil do utilizador atual
   */
  async getUserProfile(): Promise<UserProfileResponse> {
    return await makeRequest<UserProfileResponse>(
      API_ENDPOINTS.AUTH.ME,
      {
        method: 'GET',
        headers: getAuthHeaders(),
      }
    );
  },

  /**
   * Verificar se o token é válido
   */
  async checkToken(): Promise<boolean> {
    try {
      await makeRequest(
        API_ENDPOINTS.AUTH.CHECK,
        {
          method: 'GET',
          headers: getAuthHeaders(),
        }
      );
      return true;
    } catch (error) {
      return false;
    }
  },

  /**
   * Verificar se o utilizador está autenticado
   */
  isAuthenticated(): boolean {
    return tokenManager.hasValidToken();
  },

  /**
   * Obter token de acesso atual
   */
  getAccessToken(): string | null {
    return tokenManager.getAccessToken();
  },

  /**
   * Obter token de refresh atual
   */
  getRefreshToken(): string | null {
    return tokenManager.getRefreshToken();
  },

  /**
   * Renovar token se necessário
   */
  async ensureValidToken(): Promise<boolean> {
    return await tokenManager.refreshTokenIfNeeded();
  },
};

export default authService;
