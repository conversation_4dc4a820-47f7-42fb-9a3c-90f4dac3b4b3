-- =============================================
-- TABELAS PARA GESTÃO DE SALDOS DOS TESOUREIROS
-- =============================================
-- Criado em: 02/10/2025
-- Descrição: Adiciona tabelas para controlar o saldo individual de cada tesoureiro
--            e o histórico de movimentações (entradas e saídas)

-- Tabela de saldos dos tesoureiros
CREATE TABLE IF NOT EXISTS `treasurer_balances` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` CHAR(36) NOT NULL UNIQUE,
    `current_balance` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `branch_id` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`),
    INDEX `idx_treasurer_balances_user` (`user_id`),
    INDEX `idx_treasurer_balances_branch` (`branch_id`),
    CONSTRAINT `chk_treasurer_balance_positive` CHECK (`current_balance` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de histórico de movimentações dos tesoureiros
CREATE TABLE IF NOT EXISTS `treasurer_movements` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `treasurer_id` CHAR(36) NOT NULL,
    `movement_type` ENUM('credit', 'debit') NOT NULL,
    `amount` DECIMAL(15,2) NOT NULL,
    `balance_before` DECIMAL(15,2) NOT NULL,
    `balance_after` DECIMAL(15,2) NOT NULL,
    `source_type` ENUM('vault', 'cash_register', 'treasurer', 'system') NOT NULL,
    `source_id` VARCHAR(50) NULL,
    `reference_id` INT NULL COMMENT 'ID da entrega relacionada (treasury_deliveries, etc)',
    `description` TEXT NULL,
    `created_by` CHAR(36) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`treasurer_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
    INDEX `idx_treasurer_movements_treasurer` (`treasurer_id`),
    INDEX `idx_treasurer_movements_type` (`movement_type`),
    INDEX `idx_treasurer_movements_date` (`created_at`),
    INDEX `idx_treasurer_movements_reference` (`reference_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Criar saldos iniciais para tesoureiros existentes
INSERT INTO `treasurer_balances` (`user_id`, `current_balance`, `branch_id`)
SELECT u.id, 0.00, u.branch_id
FROM users u
JOIN roles r ON u.role_id = r.id
WHERE r.name = 'tesoureiro' 
  AND u.is_active = 1
  AND u.branch_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM treasurer_balances tb WHERE tb.user_id = u.id
  );

