import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  // Configuração específica para build de produção
  build: {
    // Otimizações para Vercel
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions: {
      // Configuração específica para resolver problemas de dependências opcionais
      external: [],
      output: {
        manualChunks: {
          // Separar dependências grandes em chunks separados
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
        },
      },
    },
    // Aumentar limite de chunk para evitar warnings
    chunkSizeWarningLimit: 1000,
  },

  // Configuração de otimização de dependências
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-select',
    ],
  },

  server: {
    host: "::",
    port: 8080,
    // Configuração de proxy apenas para desenvolvimento
    ...(mode === 'development' && {
      proxy: {
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false,
          // Log das requisições proxy para debugging
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('Proxy error:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('Proxying request:', req.method, req.url, '→', proxyReq.path);
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Proxy response:', req.method, req.url, '→', proxyRes.statusCode);
            });
          },
        },
      },
    }),
  },

  plugins: [
    react(),
  ],

  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },

  // Configuração específica para evitar problemas com dependências opcionais
  define: {
    // Garantir que process.env está disponível no build
    'process.env.NODE_ENV': JSON.stringify(mode === 'production' ? 'production' : 'development'),
  },
}));
