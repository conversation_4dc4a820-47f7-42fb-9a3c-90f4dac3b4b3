import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Calendar, Clock } from 'lucide-react';

const DataSistema = () => {
  const [dataAtual, setDataAtual] = useState(new Date());
  const [fusoHorario] = useState('Africa/Luanda');

  useEffect(() => {
    const timer = setInterval(() => {
      setDataAtual(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatarDataHora = (data: Date) => {
    return {
      data: data.toLocaleDateString('pt-AO', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      hora: data.toLocaleTimeString('pt-AO', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      iso: data.toISOString().split('T')[0],
      horaIso: data.toTimeString().split(' ')[0].substring(0, 5)
    };
  };

  const dataFormatada = formatarDataHora(dataAtual);



  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Calendar className="h-8 w-8" />
          Data do Sistema
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">Visualizar data e hora atual do sistema</p>
      </div>

      {/* Data e Hora Atual */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Data e Hora Atual
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="text-center p-6 bg-blue-50 rounded-lg border">
                <div className="text-3xl font-bold text-blue-900 mb-2">
                  {dataFormatada.hora}
                </div>
                <div className="text-lg text-blue-700">
                  {dataFormatada.data}
                </div>
                <div className="text-sm text-blue-600 mt-2">
                  Fuso Horário: {fusoHorario}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {dataAtual.getDate()}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Dia</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {dataAtual.getMonth() + 1}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Mês</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {dataAtual.getFullYear()}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Ano</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {Math.floor((Date.now() - new Date(dataAtual.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24))}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Dia do Ano</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>



      {/* Informações do Sistema */}
      <Card>
        <CardHeader>
          <CardTitle>Informações do Sistema</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="dark:text-gray-100">Servidor de Tempo</Label>
              <div className="p-2 bg-gray-50 dark:bg-gray-700 dark:text-gray-100 rounded border">
                time.nist.gov
              </div>
            </div>

            <div className="space-y-2">
              <Label className="dark:text-gray-100">Última Sincronização</Label>
              <div className="p-2 bg-gray-50 dark:bg-gray-700 dark:text-gray-100 rounded border">
                {new Date().toLocaleString('pt-AO')}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="dark:text-gray-100">Formato de Data</Label>
              <div className="p-2 bg-gray-50 dark:bg-gray-700 dark:text-gray-100 rounded border">
                DD/MM/AAAA
              </div>
            </div>

            <div className="space-y-2">
              <Label className="dark:text-gray-100">Formato de Hora</Label>
              <div className="p-2 bg-gray-50 dark:bg-gray-700 dark:text-gray-100 rounded border">
                24 horas (HH:MM:SS)
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DataSistema;
