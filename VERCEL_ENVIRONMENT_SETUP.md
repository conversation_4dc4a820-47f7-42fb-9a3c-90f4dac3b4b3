# Configuração de Variáveis de Ambiente na Vercel - K-Bank

## 🚨 Problemas Identificados

### Frontend (twins-bank-frontend.vercel.app)
- **Status**: 404 DEPLOYMENT_NOT_FOUND
- **Causa**: Projeto não configurado corretamente ou build falhando

### Backend (k-bank-backend.vercel.app)  
- **Status**: 500 FUNCTION_INVOCATION_FAILED
- **Causa**: Serverless function crashando na inicialização (provavelmente variáveis de ambiente em falta)

## 🔧 Configuração Necessária na Vercel

### 1. Backend (k-bank-backend)

**Variáveis de Ambiente Obrigatórias:**

```env
# Base de Dados (AlwaysData)
DB_HOST=mysql-doublec.alwaysdata.net
DB_PORT=3306
DB_NAME=doublec_twins_bank
DB_USER=doublec
DB_PASSWORD=CarlosCesar@2022

# JWT (Chave segura para produção)
JWT_SECRET=ad042f36a25c3488c6582a47622021deb039f04e8191a32c340010dd737412bf12688964f438c356b11c3a47c3a6a27d7c07551a8c72cc90df749555eb74f6e1
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Servidor
NODE_ENV=production
PORT=3000

# CORS (URL do frontend)
CORS_ORIGIN=https://twins-bank-frontend.vercel.app

# Rate Limiting (mais restritivo em produção)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Logs
LOG_LEVEL=info

# Segurança
BCRYPT_ROUNDS=12

# Supabase (para uploads - se necessário)
SUPABASE_URL=https://sktvllynmbwamqgehoru.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej4zOGJhZGE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4ZjE4
SUPABASE_SERVICE_ROLE_KEY=[CHAVE_SERVICE_ROLE_SUPABASE]
```

### 2. Frontend (twins-bank-frontend)

**Variáveis de Ambiente Obrigatórias:**

```env
# URL da API do Backend
VITE_API_URL=https://k-bank-backend.vercel.app

# Ambiente
VITE_NODE_ENV=production

# Debug (desabilitado em produção)
VITE_DEBUG=false
```

## 📋 Passos para Configuração

### 1. Configurar Backend na Vercel

1. Aceder ao projeto `k-bank-backend` na Vercel Dashboard
2. Ir para Settings → Environment Variables
3. Adicionar todas as variáveis listadas acima
4. Fazer redeploy do projeto

### 2. Configurar Frontend na Vercel

1. Aceder ao projeto `twins-bank-frontend` na Vercel Dashboard
2. Ir para Settings → Environment Variables  
3. Adicionar as variáveis do frontend listadas acima
4. Fazer redeploy do projeto

### 3. Verificar Configurações de Build

**Backend:**
- Build Command: `npm install`
- Output Directory: (deixar vazio)
- Install Command: `npm install`
- Root Directory: `backend`

**Frontend:**
- Build Command: `npm run build`
- Output Directory: `dist`
- Install Command: `npm install`
- Root Directory: `frontend`

## 🧪 Testes Após Configuração

1. **Backend Health Check**: https://k-bank-backend.vercel.app/api/health
2. **Frontend**: https://twins-bank-frontend.vercel.app/
3. **Login Test**: Usar credenciais fornecidas para testar autenticação

## 🔍 Resolução de Problemas

### Se o backend continuar com 500:
1. Verificar logs na Vercel Dashboard
2. Confirmar que todas as variáveis de ambiente estão definidas
3. Testar conexão com base de dados usando health check detalhado

### Se o frontend continuar com 404:
1. Verificar se o projeto está configurado para a pasta `frontend/`
2. Limpar cache de build na Vercel
3. Verificar se o build está a completar com sucesso

## 📝 Notas Importantes

- **JWT_SECRET**: Chave criptográfica segura - NÃO alterar após produção
- **Base de Dados**: Configuração AlwaysData já existente
- **CORS**: Deve corresponder exatamente à URL do frontend
- **Supabase**: Necessário apenas se uploads de ficheiros forem usados
