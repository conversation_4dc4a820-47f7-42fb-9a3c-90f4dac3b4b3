import { useState, useEffect } from 'react';
import { cashRegisterSessionService, CashRegisterSession } from '@/services/cashRegisterSessionService';

interface UseActiveSessionsReturn {
  activeSessions: CashRegisterSession[];
  activeSessionsCount: number;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook personalizado para carregar e gerenciar sessões ativas de caixa
 * Atualiza automaticamente a cada 30 segundos
 */
export const useActiveSessions = (): UseActiveSessionsReturn => {
  const [activeSessions, setActiveSessions] = useState<CashRegisterSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadActiveSessions = async () => {
    try {
      setError(null);
      const data = await cashRegisterSessionService.getSessions({ status: 'open' });
      setActiveSessions(data.sessions);
    } catch (error: any) {
      console.error('Erro ao carregar sessões ativas:', error);
      setError(error.message || 'Erro ao carregar sessões ativas');
      setActiveSessions([]); // Limpar dados em caso de erro
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = async () => {
    setIsLoading(true);
    await loadActiveSessions();
  };

  useEffect(() => {
    // Carregar dados inicialmente
    loadActiveSessions();

    // Configurar atualização automática a cada 30 segundos
    const interval = setInterval(loadActiveSessions, 30000);

    // Cleanup do interval quando o componente for desmontado
    return () => clearInterval(interval);
  }, []);

  return {
    activeSessions,
    activeSessionsCount: activeSessions.length,
    isLoading,
    error,
    refetch
  };
};
