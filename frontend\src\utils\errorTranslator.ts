/**
 * Sistema de tradução de mensagens de erro do backend para português
 * Converte mensagens técnicas em inglês para mensagens amigáveis em português
 */

// Mapeamento de mensagens de erro comuns do Joi e backend
const ERROR_TRANSLATIONS: Record<string, string> = {
  // Mensagens de validação Joi - Campos obrigatórios
  '"full_name" is required': 'O nome completo é obrigatório',
  '"document_number" is required': 'O número do documento é obrigatório',
  '"document_type" is required': 'O tipo de documento é obrigatório',
  '"birth_date" is required': 'A data de nascimento é obrigatória',
  '"nationality" is required': 'A nacionalidade é obrigatória',
  '"gender" is required': 'O sexo é obrigatório',
  '"marital_status" is required': 'O estado civil é obrigatório',
  '"profession" is required': 'A profissão é obrigatória',
  '"monthly_income" is required': 'O rendimento mensal é obrigatório',
  '"nif" is required': 'O NIF é obrigatório',
  '"branch_id" is required': 'O balcão é obrigatório',
  '"account_type" is required': 'O tipo de conta é obrigatório',
  '"client_id" is required': 'O cliente é obrigatório',

  // Mensagens de validação Joi - Comprimento
  '"document_number" length must be at least 5 characters long': 'O número do documento deve ter pelo menos 5 caracteres',
  '"document_number" length must be less than or equal to 20 characters long': 'O número do documento deve ter no máximo 20 caracteres',
  '"full_name" length must be at least 2 characters long': 'O nome deve ter pelo menos 2 caracteres',
  '"full_name" length must be less than or equal to 100 characters long': 'O nome deve ter no máximo 100 caracteres',
  '"nif" length must be at least 9 characters long': 'O NIF deve ter pelo menos 9 caracteres',
  '"nif" length must be less than or equal to 14 characters long': 'O NIF deve ter no máximo 14 caracteres',

  // Mensagens de validação Joi - Tipos e formatos
  '"birth_date" must be a valid date': 'A data de nascimento deve ser uma data válida',
  '"birth_date" must be less than or equal to "now"': 'A data de nascimento não pode ser no futuro',
  '"monthly_income" must be a number': 'O rendimento mensal deve ser um número',
  '"monthly_income" must be a positive number': 'O rendimento mensal deve ser um valor positivo',
  '"profession" must be a string': 'A profissão é obrigatória',
  '"gender" must be one of [M, F]': 'O sexo deve ser Masculino ou Feminino',
  '"document_type" must be one of [BI, Passaporte]': 'O tipo de documento deve ser BI ou Passaporte',
  '"marital_status" must be one of [Solteiro, Casado, Divorciado, Viúvo]': 'O estado civil deve ser Solteiro(a), Casado(a), Divorciado(a) ou Viúvo(a)',

  // Mensagens de validação Joi - Contactos
  '"contacts.phone_personal" is required': 'O telefone pessoal é obrigatório',
  '"contacts.phone_personal" is not allowed to be empty': 'O telefone pessoal não pode estar vazio',
  '"contacts.email_personal" is required': 'O email pessoal é obrigatório',
  '"contacts.email_personal" is not allowed to be empty': 'O email pessoal não pode estar vazio',
  '"contacts.email_personal" must be a valid email': 'O email deve ter um formato válido',
  '"contacts.phone_personal" must be a string': 'O telefone deve ser um texto válido',

  // Mensagens de validação Joi - Endereço
  '"address.street" is required': 'A rua é obrigatória',
  '"address.municipality" is required': 'O município é obrigatório',
  '"address.province" is required': 'A província é obrigatória',
  '"address.postal_code" is required': 'O código postal é obrigatório',
  '"address.street" is not allowed to be empty': 'A rua não pode estar vazia',
  '"address.municipality" is not allowed to be empty': 'O município não pode estar vazio',
  '"address.province" is not allowed to be empty': 'A província não pode estar vazia',

  // Mensagens de erro do backend - Duplicação
  'Já existe um registo com este document_number': 'Já existe um cliente com este número de documento',
  'Já existe um registo com este nif': 'Já existe um cliente com este NIF',
  'Já existe um registo com este email': 'Já existe um utilizador com este email',
  'Número de documento já está em uso': 'Já existe um cliente com este número de documento',
  'NIF já está em uso': 'Já existe um cliente com este NIF',
  'Email já está em uso': 'Já existe um utilizador com este email',

  // Mensagens de erro do backend - Referências
  'Balcão não encontrado ou inativo': 'O balcão selecionado não está disponível. Contacte o administrador.',
  'Cliente não encontrado': 'Cliente não encontrado no sistema',
  'Utilizador não encontrado': 'Utilizador não encontrado no sistema',
  'Referência inválida. Verifique os dados fornecidos.': 'Dados inválidos. Verifique as informações fornecidas.',

  // Mensagens de erro do backend - Autenticação
  'Credenciais inválidas': 'Credenciais inválidas. Verifique o email e senha.',
  'Conta inativa. Contacte o administrador.': 'Conta inativa. Contacte o administrador.',

  // Mensagens de erro do backend - Conexão e sistema
  'Erro de conexão com a base de dados': 'Erro de conexão. Tente novamente em alguns instantes.',
  'Erro interno do servidor': 'Ocorreu um erro interno. Contacte o suporte técnico.',
  'Token inválido': 'Sessão inválida. Faça login novamente.',
  'Token expirado': 'Sessão expirada. Faça login novamente.',

  // Mensagens de erro do backend - Ficheiros
  'Ficheiro muito grande. Tamanho máximo: 5MB': 'O ficheiro é muito grande. O tamanho máximo permitido é 5MB.',
  'Tipo de ficheiro não permitido': 'Tipo de ficheiro não permitido. Use apenas imagens (JPG, PNG, PDF).',

  // Mensagens de erro específicas do sistema bancário
  'Conta não encontrada': 'Conta bancária não encontrada',
  'Saldo insuficiente': 'Saldo insuficiente para realizar a operação',
  'Operação não permitida': 'Esta operação não é permitida para este tipo de conta',
  'Limite de levantamento excedido': 'Limite diário de levantamento excedido',
  'Conta bloqueada': 'Esta conta está bloqueada. Contacte o seu balcão.',

  // Mensagens genéricas
  'Algo correu mal. Tente novamente mais tarde.': 'Ocorreu um erro inesperado. Tente novamente mais tarde.',
  'Dados obrigatórios não fornecidos': 'Alguns campos obrigatórios não foram preenchidos',
  'Dados inválidos': 'Os dados fornecidos são inválidos',
  'Operação não autorizada': 'Não tem permissão para realizar esta operação',
  'Rota não encontrada': 'Página não encontrada'
};

// Padrões regex para mensagens dinâmicas
const DYNAMIC_ERROR_PATTERNS: Array<{pattern: RegExp, replacement: string}> = [
  {
    pattern: /"(.+)" is required/,
    replacement: 'O campo "$1" é obrigatório'
  },
  {
    pattern: /"(.+)" must be a string/,
    replacement: 'O campo "$1" deve ser um texto válido'
  },
  {
    pattern: /"(.+)" must be a number/,
    replacement: 'O campo "$1" deve ser um número'
  },
  {
    pattern: /"(.+)" must be a valid email/,
    replacement: 'O campo "$1" deve ter um formato de email válido'
  },
  {
    pattern: /"(.+)" is not allowed to be empty/,
    replacement: 'O campo "$1" não pode estar vazio'
  },
  {
    pattern: /"(.+)" length must be at least (\d+) characters long/,
    replacement: 'O campo "$1" deve ter pelo menos $2 caracteres'
  },
  {
    pattern: /"(.+)" length must be less than or equal to (\d+) characters long/,
    replacement: 'O campo "$1" deve ter no máximo $2 caracteres'
  },
  {
    pattern: /Já existe um registo com este (.+)/,
    replacement: 'Já existe um registo com este $1'
  },
  {
    pattern: /Erro ao criar conta: Error: (.+)/,
    replacement: '$1'
  },
  {
    pattern: /Erro ao (.+): Error: (.+)/,
    replacement: '$2'
  },
  {
    pattern: /Error: (.+)/,
    replacement: '$1'
  }
];

/**
 * Traduz uma mensagem de erro do inglês para português
 * @param errorMessage - Mensagem de erro original
 * @returns Mensagem traduzida e amigável em português
 */
export function translateErrorMessage(errorMessage: string): string {
  if (!errorMessage) return 'Ocorreu um erro inesperado';

  // Limpar a mensagem (remover aspas extras, espaços, etc.)
  let cleanMessage = errorMessage.trim();

  // Remover prefixos comuns de erro que não agregam valor
  cleanMessage = cleanMessage.replace(/^(Erro ao .+?: )?Error: /, '');
  cleanMessage = cleanMessage.replace(/^Error: /, '');
  cleanMessage = cleanMessage.trim();

  // Verificar se há tradução direta
  if (ERROR_TRANSLATIONS[cleanMessage]) {
    return ERROR_TRANSLATIONS[cleanMessage];
  }

  // Tentar padrões dinâmicos
  for (const {pattern, replacement} of DYNAMIC_ERROR_PATTERNS) {
    const match = cleanMessage.match(pattern);
    if (match) {
      let translatedMessage = replacement;
      // Substituir placeholders $1, $2, etc.
      for (let i = 1; i < match.length; i++) {
        translatedMessage = translatedMessage.replace(`$${i}`, match[i]);
      }
      return translatedMessage;
    }
  }

  // Se não encontrou tradução, tentar algumas heurísticas
  if (cleanMessage.includes('is required')) {
    return 'Um campo obrigatório não foi preenchido';
  }
  
  if (cleanMessage.includes('must be')) {
    return 'Formato de dados inválido';
  }
  
  if (cleanMessage.includes('not found')) {
    return 'Registo não encontrado';
  }
  
  if (cleanMessage.includes('already exists') || cleanMessage.includes('duplicate')) {
    return 'Este registo já existe no sistema';
  }

  // Se nada funcionou, registar para debugging e retornar mensagem genérica
  if (process.env.NODE_ENV === 'development') {
    console.warn('Mensagem de erro não traduzida:', cleanMessage);
  }

  return 'Ocorreu um erro. Verifique os dados e tente novamente.';
}

/**
 * Traduz múltiplas mensagens de erro (útil para erros de validação múltiplos)
 * @param errorMessages - Array de mensagens de erro
 * @returns Array de mensagens traduzidas
 */
export function translateErrorMessages(errorMessages: string[]): string[] {
  return errorMessages.map(translateErrorMessage);
}

/**
 * Extrai e traduz mensagens de erro de um objeto de erro do backend
 * @param error - Objeto de erro do backend
 * @returns Mensagem de erro traduzida
 */
export function extractAndTranslateError(error: any): string {
  // Se é um erro simples com mensagem
  if (error?.message) {
    return translateErrorMessage(error.message);
  }

  // Se é um erro de resposta HTTP
  if (error?.response?.data?.message) {
    return translateErrorMessage(error.response.data.message);
  }

  // Se é um erro de fetch com resposta JSON
  if (error?.data?.message) {
    return translateErrorMessage(error.data.message);
  }

  // Se é um erro de validação com múltiplos detalhes
  if (error?.response?.data?.errors) {
    const errors = error.response.data.errors;
    if (Array.isArray(errors)) {
      const translatedErrors = translateErrorMessages(errors);
      // Retornar apenas a primeira mensagem mais importante para o toast
      // Mensagens completas ficam disponíveis no console para debugging
      return translatedErrors.length > 1
        ? `${translatedErrors[0]} (e mais ${translatedErrors.length - 1} erro${translatedErrors.length > 2 ? 's' : ''})`
        : translatedErrors[0] || 'Erro de validação';
    }
    if (typeof errors === 'object') {
      const errorMessages = Object.values(errors).filter(Boolean) as string[];
      const translatedErrors = translateErrorMessages(errorMessages);
      // Retornar apenas a primeira mensagem mais importante para o toast
      return translatedErrors.length > 1
        ? `${translatedErrors[0]} (e mais ${translatedErrors.length - 1} erro${translatedErrors.length > 2 ? 's' : ''})`
        : translatedErrors[0] || 'Erro de validação';
    }
  }

  // Se é um erro de rede
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }

  // Erro genérico
  return 'Ocorreu um erro inesperado. Tente novamente mais tarde.';
}
