/**
 * Script para testar conexão com a base de dados
 * Útil para debugging de problemas de deploy na Vercel
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

// Configuração da base de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'twins_bank',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4',
  timezone: '+00:00'
};

async function testDatabaseConnection() {
  console.log('🔍 Testando conexão com a base de dados...');
  console.log('Configuração:', {
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    database: dbConfig.database,
    password: dbConfig.password ? '***' : 'NÃO DEFINIDA'
  });

  try {
    // Criar pool de conexões
    const pool = mysql.createPool(dbConfig);

    // Testar conexão
    const connection = await pool.getConnection();
    console.log('✅ Conexão estabelecida com sucesso!');

    // Testar query simples
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ Query de teste executada:', rows);

    // Testar se tabelas existem
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`✅ Base de dados contém ${tables.length} tabelas`);

    // Testar tabela de utilizadores
    try {
      const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
      console.log(`✅ Tabela users contém ${users[0].count} registos`);
    } catch (error) {
      console.log('⚠️  Tabela users não encontrada ou erro:', error.message);
    }

    connection.release();
    await pool.end();

    console.log('🎉 Teste de conexão completado com sucesso!');
    return true;

  } catch (error) {
    console.error('❌ Erro na conexão com a base de dados:');
    console.error('Código:', error.code);
    console.error('Mensagem:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testDatabaseConnection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Erro inesperado:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnection };
