const express = require('express');
const Joi = require('joi');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const { authorize } = require('../auth/middleware');
const logger = require('../core/logger');

const router = express.Router();

// Esquemas de validação
const createRoleSchema = Joi.object({
  name: Joi.string().min(2).max(50).required().messages({
    'string.min': 'Nome deve ter pelo menos 2 caracteres',
    'string.max': 'Nome deve ter no máximo 50 caracteres',
    'any.required': 'Nome é obrigatório'
  }),
  description: Joi.string().max(255).optional().allow(''),
  permissions: Joi.array().items(Joi.string()).optional()
});

const updateRoleSchema = Joi.object({
  name: Joi.string().min(2).max(50).optional(),
  description: Joi.string().max(255).optional().allow(''),
  permissions: Joi.array().items(Joi.string()).optional()
});

/**
 * GET /api/roles
 * Listar roles
 */
router.get('/', authorize('admin'), catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = '' } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Construir query com filtros
  let whereConditions = [];
  let queryParams = [];
  
  if (search) {
    whereConditions.push('(r.name LIKE ? OR r.description LIKE ?)');
    queryParams.push(`%${search}%`, `%${search}%`);
  }
  
  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
  
  // Query principal
  const roles = await executeQuery(
    `SELECT r.id, r.name, r.description, r.created_at,
            COUNT(u.id) as user_count
     FROM roles r
     LEFT JOIN users u ON r.id = u.role_id
     ${whereClause}
     GROUP BY r.id, r.name, r.description, r.created_at
     ORDER BY r.created_at DESC
     LIMIT ? OFFSET ?`,
    [...queryParams, parseInt(limit), offset]
  );
  
  // Contar total
  const totalResult = await executeQuery(
    `SELECT COUNT(DISTINCT r.id) as total 
     FROM roles r 
     ${whereClause}`,
    queryParams
  );
  
  const total = totalResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.status(200).json({
    status: 'success',
    data: {
      roles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * GET /api/roles/:id
 * Obter role específico com permissões
 */
router.get('/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const roles = await executeQuery(
    `SELECT r.id, r.name, r.description, r.created_at,
            COUNT(u.id) as user_count
     FROM roles r
     LEFT JOIN users u ON r.id = u.role_id
     WHERE r.id = ?
     GROUP BY r.id, r.name, r.description, r.created_at`,
    [id]
  );
  
  if (!roles || roles.length === 0) {
    return next(new AppError('Role não encontrado', 404, 'ROLE_NOT_FOUND'));
  }
  
  // Buscar permissões do role (se implementado)
  const permissions = await executeQuery(
    `SELECT p.name, p.description 
     FROM role_permissions rp 
     JOIN permissions p ON rp.permission_id = p.id 
     WHERE rp.role_id = ?`,
    [id]
  ).catch(() => []); // Se tabela não existir, retorna array vazio
  
  const role = {
    ...roles[0],
    permissions: permissions.map(p => p.name)
  };
  
  res.status(200).json({
    status: 'success',
    data: {
      role
    }
  });
}));

/**
 * POST /api/roles
 * Criar novo role (apenas Admin)
 * DESABILITADO: Roles devem ser criados diretamente no código/base de dados
 */
/*
router.post('/', authorize('admin'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = createRoleSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }
  
  const { name, description, permissions = [] } = value;
  
  // 2. Verificar se nome já existe
  const existingRoles = await executeQuery(
    'SELECT id FROM roles WHERE name = ?',
    [name]
  );
  
  if (existingRoles && existingRoles.length > 0) {
    return next(new AppError('Nome de role já está em uso', 409, 'ROLE_EXISTS'));
  }
  
  // 3. Criar role
  const result = await executeQuery(
    `INSERT INTO roles (name, description) VALUES (?, ?)`,
    [name, description || null]
  );
  
  // 4. Adicionar permissões (se implementado)
  if (permissions.length > 0) {
    // TODO: Implementar sistema de permissões
    logger.info(`Permissões para role ${name}: ${permissions.join(', ')}`);
  }
  
  // 5. Obter dados completos do role criado
  const newRoles = await executeQuery(
    `SELECT r.id, r.name, r.description, r.created_at,
            COUNT(u.id) as user_count
     FROM roles r 
     LEFT JOIN users u ON r.id = u.role_id 
     WHERE r.id = ?
     GROUP BY r.id, r.name, r.description, r.created_at`,
    [result.insertId]
  );
  
  logger.info(`Novo role criado: ${name}`, { 
    roleId: result.insertId, 
    createdBy: req.user.id,
    name,
    description
  });
  
  res.status(201).json({
    status: 'success',
    message: 'Role criado com sucesso',
    data: {
      role: {
        ...newRoles[0],
        permissions
      }
    }
  });
}));
*/

/**
 * PUT /api/roles/:id
 * Atualizar role (apenas Admin)
 * DESABILITADO: Roles devem ser criados diretamente no código/base de dados
 */
/*
router.put('/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // 1. Validar dados de entrada
  const { error, value } = updateRoleSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }
  
  // 2. Verificar se role existe
  const existingRoles = await executeQuery('SELECT id, name FROM roles WHERE id = ?', [id]);
  if (!existingRoles || existingRoles.length === 0) {
    return next(new AppError('Role não encontrado', 404, 'ROLE_NOT_FOUND'));
  }
  
  // 3. Se nome está sendo alterado, verificar se não existe
  if (value.name && value.name !== existingRoles[0].name) {
    const nameExists = await executeQuery(
      'SELECT id FROM roles WHERE name = ? AND id != ?',
      [value.name, id]
    );
    
    if (nameExists && nameExists.length > 0) {
      return next(new AppError('Nome de role já está em uso', 409, 'ROLE_EXISTS'));
    }
  }
  
  // 4. Construir query de atualização
  const updateFields = [];
  const updateValues = [];
  
  Object.keys(value).forEach(key => {
    if (key !== 'permissions') {
      updateFields.push(`${key} = ?`);
      updateValues.push(value[key]);
    }
  });
  
  if (updateFields.length === 0 && !value.permissions) {
    return next(new AppError('Nenhum campo para atualizar', 400, 'NO_FIELDS_TO_UPDATE'));
  }
  
  // 5. Atualizar role
  if (updateFields.length > 0) {
    updateValues.push(id);

    await executeQuery(
      `UPDATE roles SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
  }
  
  // 6. Atualizar permissões (se implementado)
  if (value.permissions) {
    // TODO: Implementar sistema de permissões
    logger.info(`Permissões atualizadas para role ${id}: ${value.permissions.join(', ')}`);
  }
  
  // 7. Obter dados atualizados
  const updatedRoles = await executeQuery(
    `SELECT r.id, r.name, r.description, r.created_at,
            COUNT(u.id) as user_count
     FROM roles r
     LEFT JOIN users u ON r.id = u.role_id
     WHERE r.id = ?
     GROUP BY r.id, r.name, r.description, r.created_at`,
    [id]
  );
  
  logger.info(`Role atualizado: ${id}`, { 
    roleId: id, 
    updatedBy: req.user.id,
    updatedFields: Object.keys(value)
  });
  
  res.status(200).json({
    status: 'success',
    message: 'Role atualizado com sucesso',
    data: {
      role: {
        ...updatedRoles[0],
        permissions: value.permissions || []
      }
    }
  });
}));
*/

/**
 * DELETE /api/roles/:id
 * Remover role (apenas Admin)
 * DESABILITADO: Roles devem ser criados diretamente no código/base de dados
 */
/*
router.delete('/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // 1. Verificar se role existe
  const existingRoles = await executeQuery('SELECT id, name FROM roles WHERE id = ?', [id]);
  if (!existingRoles || existingRoles.length === 0) {
    return next(new AppError('Role não encontrado', 404, 'ROLE_NOT_FOUND'));
  }
  
  // 2. Verificar se há usuários associados ao role
  const associatedUsers = await executeQuery('SELECT COUNT(*) as count FROM users WHERE role_id = ?', [id]);
  if (associatedUsers[0].count > 0) {
    return next(new AppError('Não é possível remover role com usuários associados', 409, 'ROLE_HAS_USERS'));
  }
  
  // 3. Remover role
  await executeQuery('DELETE FROM roles WHERE id = ?', [id]);
  
  logger.info(`Role removido: ${existingRoles[0].name}`, { 
    roleId: id, 
    deletedBy: req.user.id,
    roleName: existingRoles[0].name
  });
  
  res.status(200).json({
    status: 'success',
    message: 'Role removido com sucesso'
  });
}));
*/

module.exports = router;
