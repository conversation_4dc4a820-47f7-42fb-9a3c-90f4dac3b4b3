// Hook melhorado para notificações com mais funcionalidades
// Adiciona métodos de conveniência e melhor tipagem

import { useToast } from '@/hooks/use-toast';
import { CheckCircle, AlertTriangle, XCircle, Info, Loader2 } from 'lucide-react';

export interface EnhancedToastOptions {
  title: string;
  description?: string;
  duration?: number;
  action?: React.ReactElement;
}

export const useEnhancedToast = () => {
  const { toast, dismiss } = useToast();

  // Toast de sucesso com ícone verde
  const success = (options: EnhancedToastOptions) => {
    return toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 4000,
      action: options.action,
      className: 'border-l-4 border-l-green-500',
    });
  };

  // Toast de erro com ícone vermelho
  const error = (options: EnhancedToastOptions) => {
    return toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 6000, // Erros ficam mais tempo
      variant: 'destructive',
      action: options.action,
      className: 'border-l-4 border-l-red-500',
    });
  };

  // Toast de aviso com ícone amarelo
  const warning = (options: EnhancedToastOptions) => {
    return toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 5000,
      action: options.action,
      className: 'border-l-4 border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20',
    });
  };

  // Toast de informação com ícone azul
  const info = (options: EnhancedToastOptions) => {
    return toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 4000,
      action: options.action,
      className: 'border-l-4 border-l-blue-500 bg-blue-50 dark:bg-blue-900/20',
    });
  };

  // Toast de loading que pode ser atualizado
  const loading = (options: EnhancedToastOptions) => {
    return toast({
      title: options.title,
      description: options.description,
      duration: Infinity, // Não remove automaticamente
      action: options.action,
      className: 'border-l-4 border-l-gray-500',
    });
  };

  // Métodos de conveniência para operações comuns
  const operationSuccess = (operation: string, details?: string) => {
    return success({
      title: `${operation} realizada com sucesso`,
      description: details,
    });
  };

  const operationError = (operation: string, error: string) => {
    return error({
      title: `Erro ao ${operation.toLowerCase()}`,
      description: error,
    });
  };

  const operationLoading = (operation: string) => {
    return loading({
      title: `${operation}...`,
      description: 'Por favor, aguarde',
    });
  };

  return {
    // Métodos básicos
    toast,
    dismiss,
    
    // Métodos tipados
    success,
    error,
    warning,
    info,
    loading,
    
    // Métodos de conveniência
    operationSuccess,
    operationError,
    operationLoading,
  };
};

export default useEnhancedToast;
