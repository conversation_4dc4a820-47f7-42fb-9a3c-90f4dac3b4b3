# Setup do Backend Twins_Bank

## ✅ Status da Implementação

### Base de Dados
- ✅ **Schema Completo**: 24 tabelas criadas na base `doublec_twins_bank`
- ✅ **Dados Iniciais**: Roles, utilizadores, balcões e configurações inseridos
- ✅ **Super Admin**: Configurado e pronto para uso

### Backend
- ✅ **Estrutura Modular**: 11 módulos implementados
- ✅ **Autenticação JWT**: Sistema completo implementado
- ✅ **API Routes**: Endpoints estruturados e funcionais

## 🚀 Passos para Configuração

### 1. Instalar Dependências
```bash
cd backend
npm install
```

### 2. Configurar Variáveis de Ambiente
```bash
# Copiar ficheiro de exemplo
cp .env.example .env

# Editar .env com as suas configurações
```

**Configuração .env necessária:**
```env
# Base de Dados (já configurada no twins_db)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=doublec_twins_bank
DB_USER=seu_usuario_mysql
DB_PASSWORD=sua_senha_mysql

# JWT (IMPORTANTE: Alterar em produção)
JWT_SECRET=sua_chave_secreta_jwt_muito_segura_aqui
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Servidor
PORT=3001
NODE_ENV=development

# CORS (URL do frontend)
CORS_ORIGIN=http://localhost:5173
```

### 3. Iniciar o Servidor
```bash
# Desenvolvimento (com auto-reload)
npm run dev

# Produção
npm start
```

### 4. Testar a API

**Health Check:**
```bash
curl http://localhost:3001/api/health
```

**Login do Super Admin:**
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "*********"
  }'
```

**Resposta esperada:**
```json
{
  "status": "success",
  "message": "Login realizado com sucesso",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "full_name": "Super Administrador",
      "email": "<EMAIL>",
      "role": "admin",
      "branch": "Agência Central"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": "24h"
    }
  }
}
```

## 👥 Utilizadores de Teste

| Email | Senha | Role | Descrição |
|-------|-------|------|-----------|
| <EMAIL> | ********* | admin | Super Administrador |
| <EMAIL> | ********* | gerente | Gerente |
| <EMAIL> | ********* | tesoureiro | Tesoureiro |
| <EMAIL> | ********* | caixa | Operador de Caixa |
| <EMAIL> | ********* | tecnico | Técnico |

## 🔧 Endpoints Principais

### Autenticação
- `POST /api/auth/login` - Login
- `POST /api/auth/logout` - Logout
- `POST /api/auth/refresh` - Renovar token
- `GET /api/auth/me` - Dados do utilizador

### Utilizadores (Admin apenas)
- `GET /api/users` - Listar utilizadores
- `POST /api/users` - Criar utilizador
- `GET /api/users/:id` - Obter utilizador
- `PUT /api/users/:id` - Actualizar utilizador

### Caixa
- `GET /api/cash-registers/available` - Caixas disponíveis
- `POST /api/cash-registers/sessions/open` - Abrir sessão
- `POST /api/cash-registers/sessions/close` - Fechar sessão

### Health Check
- `GET /api/health` - Status do sistema

## 🗄️ Base de Dados

### Conexão
A base de dados `doublec_twins_bank` já está configurada com:
- 24 tabelas criadas
- Dados iniciais inseridos
- Relacionamentos configurados
- Índices optimizados

### Caixas Disponíveis
```sql
-- Verificar caixas disponíveis
SELECT cr.register_number, cr.description, b.name as branch
FROM cash_registers cr
JOIN branches b ON cr.branch_id = b.id
WHERE cr.status = 'available';
```

## 🔒 Segurança

### Configurações Implementadas
- ✅ **Helmet**: Headers de segurança
- ✅ **CORS**: Controlo de origem
- ✅ **Rate Limiting**: 100 requests/15min
- ✅ **bcrypt**: Hash de senhas (12 rounds)
- ✅ **JWT**: Tokens seguros com refresh
- ✅ **Joi**: Validação de entrada

### Recomendações de Produção
1. **Alterar JWT_SECRET** para uma chave forte
2. **Configurar HTTPS** no servidor
3. **Alterar senhas padrão** dos utilizadores de teste
4. **Configurar backup** da base de dados
5. **Monitorizar logs** de segurança

## 📝 Logs

Os logs são armazenados em:
- `logs/app.log` - Todos os logs
- `logs/error.log` - Apenas erros

Níveis disponíveis: error, warn, info, http, debug

## 🧪 Testes

```bash
# Executar testes (quando implementados)
npm test

# Cobertura de código
npm run test:coverage
```

## 🚨 Resolução de Problemas

### Erro de Conexão com a Base de Dados
1. Verificar credenciais no `.env`
2. Confirmar que o MySQL está a correr
3. Verificar se a base `doublec_twins_bank` existe

### Erro de Token JWT
1. Verificar se `JWT_SECRET` está definido no `.env`
2. Confirmar formato do token no header: `Bearer <token>`

### Erro de CORS
1. Verificar `CORS_ORIGIN` no `.env`
2. Confirmar URL do frontend

## ✅ Próximos Passos

1. **Configurar .env** com as suas credenciais
2. **Instalar dependências** (`npm install`)
3. **Iniciar servidor** (`npm run dev`)
4. **Testar login** do super admin
5. **Integrar com frontend** existente
6. **Implementar módulos** restantes conforme necessário

## 📞 Suporte

Para questões técnicas ou problemas de configuração, consulte:
- `README.md` - Documentação completa
- `etapas.md` - Plano de implementação
- `actualizacoes.md` - Histórico de alterações
