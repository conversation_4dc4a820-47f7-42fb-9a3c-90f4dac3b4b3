import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';

interface PermissionGateProps {
  children: React.ReactNode;
  roles?: UserRole | UserRole[];
  permission?: {
    module: string;
    action: string;
  };
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * Componente para controlar a renderização baseada em permissões
 * Usado para mostrar/ocultar elementos da UI baseado no role ou permissão do usuário
 */
const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  roles,
  permission,
  fallback = null,
  showFallback = false
}) => {
  const { hasRole, hasPermission } = useAuth();

  // Verificar roles se especificados
  if (roles && !hasRole(roles)) {
    return showFallback ? <>{fallback}</> : null;
  }

  // Verificar permissões se especificadas
  if (permission && !hasPermission(permission.module, permission.action)) {
    return showFallback ? <>{fallback}</> : null;
  }

  // Se passou por todas as verificações, renderizar o conteúdo
  return <>{children}</>;
};

export default PermissionGate;
