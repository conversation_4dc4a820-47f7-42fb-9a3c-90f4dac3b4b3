import { makeRequest } from '@/utils/api';

// Interfaces para gestão de caixas
export interface CashRegister {
  id: number;
  register_number: string;
  description?: string;
  branch_id: number;
  branch_name: string;
  branch_code: string;
  status: 'available' | 'in_use' | 'closed' | 'maintenance';
  created_at: string;
}

export interface CreateCashRegisterRequest {
  register_number: string;
  description?: string;
  branch_id: number;
  status?: 'available' | 'in_use' | 'closed' | 'maintenance';
}

export interface UpdateCashRegisterRequest {
  register_number?: string;
  description?: string;
  branch_id?: number;
  status?: 'available' | 'in_use' | 'closed' | 'maintenance';
}

export interface ForceCloseResponse {
  cash_register: {
    id: number;
    register_number: string;
    status: string;
    branch_name: string;
  };
  session: {
    id: string;
    operator: string;
    opening_balance: number;
    closing_balance: number;
    difference: number;
    forced_by: string;
    forced_at: string;
  };
}

export interface CashRegisterListResponse {
  cash_registers: CashRegister[];
}

class CashRegisterManagementService {
  // Listar todos os caixas
  async getCashRegisters(): Promise<CashRegister[]> {
    const response = await makeRequest<CashRegisterListResponse>('/cash-registers');

    if (response.data) {
      return response.data.cash_registers;
    }
    throw new Error(response.message || 'Erro ao carregar caixas');
  }

  // Listar caixas disponíveis
  async getAvailableCashRegisters(): Promise<CashRegister[]> {
    const response = await makeRequest<CashRegisterListResponse>('/cash-registers/available');

    if (response.data) {
      return response.data.cash_registers;
    }
    throw new Error(response.message || 'Erro ao carregar caixas disponíveis');
  }

  // Obter caixa específico
  async getCashRegister(id: number): Promise<CashRegister> {
    const response = await makeRequest<{ cash_register: CashRegister }>(`/cash-registers/${id}`);

    if (response.data) {
      return response.data.cash_register;
    }
    throw new Error(response.message || 'Erro ao carregar caixa');
  }

  // Criar novo caixa
  async createCashRegister(data: CreateCashRegisterRequest): Promise<CashRegister> {
    const response = await makeRequest<{ cash_register: CashRegister }>('/cash-registers', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.cash_register;
    }
    throw new Error(response.message || 'Erro ao criar caixa');
  }

  // Atualizar caixa
  async updateCashRegister(id: number, data: UpdateCashRegisterRequest): Promise<CashRegister> {
    const response = await makeRequest<{ cash_register: CashRegister }>(`/cash-registers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.cash_register;
    }
    throw new Error(response.message || 'Erro ao atualizar caixa');
  }

  // Remover caixa
  async deleteCashRegister(id: number): Promise<void> {
    const response = await makeRequest(`/cash-registers/${id}`, {
      method: 'DELETE',
    });

    if (!response.success) {
      throw new Error(response.message || 'Erro ao remover caixa');
    }
  }

  // Forçar fecho de caixa
  async forceCloseCashRegister(id: number): Promise<ForceCloseResponse> {
    const response = await makeRequest<ForceCloseResponse>(`/cash-registers/${id}/force-close`, {
      method: 'POST',
    });

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao forçar fecho do caixa');
  }

  // Obter status badge para exibição
  getStatusBadge(status: string): { text: string; className: string } {
    switch (status) {
      case 'available':
        return { text: 'Disponível', className: 'bg-green-100 text-green-800 hover:bg-green-100' };
      case 'in_use':
        return { text: 'Em Uso', className: 'bg-blue-100 text-blue-800 hover:bg-blue-100' };
      case 'closed':
        return { text: 'Fechado', className: 'bg-red-100 text-red-800 hover:bg-red-100' };
      case 'maintenance':
        return { text: 'Manutenção', className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100' };
      default:
        return { text: status, className: 'bg-gray-100 text-gray-800 hover:bg-gray-100' };
    }
  }

  // Validar número de caixa
  validateRegisterNumber(registerNumber: string): { isValid: boolean; error?: string } {
    if (!registerNumber || registerNumber.trim().length === 0) {
      return { isValid: false, error: 'Número do caixa é obrigatório' };
    }
    
    if (registerNumber.length > 10) {
      return { isValid: false, error: 'Número do caixa deve ter no máximo 10 caracteres' };
    }
    
    // Verificar se contém apenas caracteres alfanuméricos
    const alphanumericRegex = /^[A-Za-z0-9]+$/;
    if (!alphanumericRegex.test(registerNumber)) {
      return { isValid: false, error: 'Número do caixa deve conter apenas letras e números' };
    }
    
    return { isValid: true };
  }

  // Validar descrição
  validateDescription(description: string): { isValid: boolean; error?: string } {
    if (description && description.length > 500) {
      return { isValid: false, error: 'Descrição deve ter no máximo 500 caracteres' };
    }
    
    return { isValid: true };
  }
}

export const cashRegisterManagementService = new CashRegisterManagementService();
