import { API_CONFIG } from '@/utils/api';
import { tokenManager } from '@/utils/tokenManager';

export interface DocumentUploadResponse {
  status: string;
  message: string;
  data: {
    clientId: string;
    clientType: string;
    uploadedFiles: Record<string, {
      originalName: string;
      fileName: string;
      path: string;
      publicUrl: string;
      size: number;
      mimetype: string;
    }>;
    filesCount: number;
  };
}

class DocumentService {
  private getAuthHeaders(): Record<string, string> {
    const token = tokenManager.getAccessToken();
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }

  /**
   * Upload client documents to Supabase storage
   * @param clientId - ID of the client
   * @param files - Object containing the files to upload
   * @param clientType - Type of client ('individual' or 'company')
   * @returns Promise with upload results
   */
  async uploadClientDocuments(
    clientId: string, 
    files: Record<string, File>, 
    clientType: 'individual' | 'company' = 'individual'
  ): Promise<DocumentUploadResponse> {
    const url = `${API_CONFIG.baseURL}/upload/client-documents`;
    
    // Create FormData with files
    const formData = new FormData();
    formData.append('clientId', clientId);
    formData.append('clientType', clientType);
    
    // Add each file to FormData
    Object.entries(files).forEach(([fieldName, file]) => {
      if (file) {
        formData.append(fieldName, file);
      }
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: formData
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || `Erro no upload de documentos: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Delete client documents from Supabase storage
   * @param clientId - ID of the client
   * @param clientType - Type of client ('individual' or 'company')
   * @returns Promise indicating success
   */
  async deleteClientDocuments(
    clientId: string, 
    clientType: 'individual' | 'company' = 'individual'
  ): Promise<void> {
    const url = `${API_CONFIG.baseURL}/upload/client-documents/${clientId}`;
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ clientType })
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || `Erro ao eliminar documentos: ${response.statusText}`);
    }
  }

  /**
   * Validate file before upload
   * @param file - File to validate
   * @returns Object with validation result and error message if any
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `Tipo de ficheiro não permitido: ${file.type}. Tipos permitidos: ${allowedTypes.join(', ')}`
      };
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `Ficheiro muito grande: ${(file.size / 1024 / 1024).toFixed(2)}MB. Tamanho máximo: 5MB`
      };
    }

    return { isValid: true };
  }

  /**
   * Validate multiple files before upload
   * @param files - Object containing files to validate
   * @returns Object with validation result and error messages if any
   */
  validateFiles(files: Record<string, File>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    Object.entries(files).forEach(([fieldName, file]) => {
      if (file) {
        const validation = this.validateFile(file);
        if (!validation.isValid) {
          errors.push(`${fieldName}: ${validation.error}`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const documentService = new DocumentService();
