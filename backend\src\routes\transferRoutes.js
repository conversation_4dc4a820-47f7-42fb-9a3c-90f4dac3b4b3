const express = require('express');
const { authorize } = require('../auth/middleware');
const { catchAsync } = require('../core/errorHandler');
const transferService = require('../services/transferService');
const suspendedMovementService = require('../services/suspendedMovementService');

const router = express.Router();

/**
 * GET /api/transfers/verify-account/:accountNumber
 * Verificar informações de uma conta
 */
router.get('/verify-account/:accountNumber',
  authorize('admin', 'gerente', 'caixa'),
  catchAsync(async (req, res) => {
    const { accountNumber } = req.params;

    const accountInfo = await transferService.verifyAccount(accountNumber);

    res.status(200).json({
      status: 'success',
      data: accountInfo
    });
  })
);

/**
 * POST /api/transfers/internal
 * Criar transferência interna
 */
router.post('/internal',
  authorize('admin', 'gerente', 'caixa'),
  catchAsync(async (req, res) => {
    const { sourceAccount, destinationAccount, amount, description, natureza } = req.body;
    const userId = req.user.id;

    // Validações básicas
    if (!sourceAccount || !destinationAccount || !amount) {
      return res.status(400).json({
        status: 'error',
        message: 'Dados obrigatórios: conta origem, conta destino e valor'
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        status: 'error',
        message: 'O valor deve ser maior que zero'
      });
    }

    if (sourceAccount === destinationAccount) {
      return res.status(400).json({
        status: 'error',
        message: 'Conta origem e destino não podem ser iguais'
      });
    }

    const transfer = await transferService.createInternalTransfer({
      sourceAccount,
      destinationAccount,
      amount: parseFloat(amount),
      description,
      natureza
    }, userId);

    res.status(201).json({
      status: 'success',
      message: 'Transferência interna realizada com sucesso',
      data: transfer
    });
  })
);

/**
 * GET /api/transfers/natures
 * Obter naturezas de transferência disponíveis
 */
router.get('/natures',
  authorize('admin', 'gerente', 'caixa'),
  catchAsync(async (req, res) => {
    const natures = await transferService.getTransferNatures();

    res.status(200).json({
      status: 'success',
      data: natures
    });
  })
);

/**
 * GET /api/transfers
 * Listar transferências com filtros
 */
router.get('/',
  authorize('admin', 'gerente', 'caixa'),
  catchAsync(async (req, res) => {
    const filters = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10,
      searchTerm: req.query.search,
      status: req.query.status,
      natureza: req.query.natureza,
      dateFrom: req.query.dateFrom,
      dateTo: req.query.dateTo
    };

    const result = await transferService.getTransfers(filters);

    res.status(200).json({
      status: 'success',
      data: result
    });
  })
);

/**
 * GET /api/transfers/:id
 * Obter detalhes de uma transferência
 */
router.get('/:id',
  authorize('admin', 'gerente', 'caixa'),
  catchAsync(async (req, res) => {
    const { id } = req.params;

    // Para simplicidade, vamos usar o método de listagem com filtro por ID
    const result = await transferService.getTransfers({ searchTerm: id, limit: 1 });

    if (!result.transfers || result.transfers.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Transferência não encontrada'
      });
    }

    res.status(200).json({
      status: 'success',
      data: result.transfers[0]
    });
  })
);

/**
 * PATCH /api/transfers/:id/cancel
 * Cancelar transferência
 */
router.patch('/:id/cancel',
  authorize('admin', 'gerente'),
  catchAsync(async (req, res) => {
    const { id } = req.params;
    const { motivo } = req.body;
    const userId = req.user.id;

    if (!motivo) {
      return res.status(400).json({
        status: 'error',
        message: 'Motivo do cancelamento é obrigatório'
      });
    }

    const result = await transferService.cancelTransfer(id, motivo, userId);

    res.status(200).json({
      status: 'success',
      message: result.message
    });
  })
);

/**
 * POST /api/transfers/sptr
 * Transferência SPTR (placeholder)
 */
router.post('/sptr', authorize('admin', 'gerente', 'caixa'), (req, res) => {
  res.status(501).json({
    status: 'error',
    message: 'Transferência SPTR - Funcionalidade não implementada'
  });
});

/**
 * POST /api/transfers/stc
 * Transferência STC (placeholder)
 */
router.post('/stc', authorize('admin', 'gerente', 'caixa'), (req, res) => {
  res.status(501).json({
    status: 'error',
    message: 'Transferência STC - Funcionalidade não implementada'
  });
});

module.exports = router;
