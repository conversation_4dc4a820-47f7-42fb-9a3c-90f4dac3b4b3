-- Seeds da Base de Dados Twins_Bank
-- Dados iniciais para configuração do sistema
-- Criado em: 03/09/2025

-- =============================================
-- SEED DOS ROLES/PERFIS
-- =============================================

INSERT INTO `roles` (`name`, `description`) VALUES
('admin', 'Super Administrador - Acesso total ao sistema'),
('gerente', 'Gerente - Gestão operacional e relatórios'),
('tesoureiro', 'Tesoureiro - Gestão do cofre e operações de tesouraria'),
('caixa', 'Operador de Caixa - Operações de atendimento ao cliente'),
('tecnico', 'Técnico - Suporte técnico e manutenção do sistema');

-- =============================================
-- SEED DAS MOEDAS
-- =============================================

INSERT INTO `currencies` (`code`, `name`, `symbol`, `is_active`) VALUES
('AOA', 'Kwanza Angolano', 'Kz', TRUE),
('USD', 'Dólar Americano', '$', TRUE),
('EUR', 'Euro', '€', TRUE),
('GBP', 'Libra Esterlina', '£', TRUE);

-- =============================================
-- SEED DOS BALCÕES
-- =============================================

INSERT INTO `branches` (`code`, `name`, `address`, `phone`, `email`, `is_active`) VALUES
('001', 'Agência Central', 'Rua Rainha Ginga, 123, Luanda', '+244 222 123 456', '<EMAIL>', TRUE),
('002', 'Agência Talatona', 'Rua da Samba, 456, Talatona', '+244 222 789 012', '<EMAIL>', TRUE),
('003', 'Agência Benguela', 'Avenida Norton de Matos, 789, Benguela', '+244 272 345 678', '<EMAIL>', TRUE),
('004', 'Agência Huambo', 'Largo da Independência, 321, Huambo', '+244 241 567 890', '<EMAIL>', TRUE);

-- =============================================
-- SEED DO SUPER ADMIN
-- =============================================

-- Inserir o super admin conforme especificado no backend.md
-- Email: <EMAIL>
-- Password: 123456789 (será hasheada pela aplicação)
-- Nota: O hash bcrypt de '123456789' é gerado pela aplicação

INSERT INTO `users` (`id`, `full_name`, `email`, `password_hash`, `role_id`, `branch_id`, `is_active`) VALUES
('550e8400-e29b-41d4-a716-************', 'Super Administrador', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 1, 1, TRUE);

-- =============================================
-- SEED DE UTILIZADORES DE DEMONSTRAÇÃO
-- =============================================

-- Gerente (senha: 123456789)
INSERT INTO `users` (`id`, `full_name`, `email`, `password_hash`, `role_id`, `branch_id`, `is_active`) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'João Manuel Silva', '<EMAIL>', '$2a$12$GbHSAYzwroa7jMTvcc2rcO762/IJQoMZzCGE.p78MopGMmJavInLG', 2, 1, TRUE);

-- Tesoureiro (senha: 123456789)
INSERT INTO `users` (`id`, `full_name`, `email`, `password_hash`, `role_id`, `branch_id`, `is_active`) VALUES
('550e8400-e29b-41d4-a716-446655440002', 'Carlos Alberto Mendes', '<EMAIL>', '$2a$12$GbHSAYzwroa7jMTvcc2rcO762/IJQoMZzCGE.p78MopGMmJavInLG', 3, 1, TRUE);

-- Operador de Caixa (senha: 123456789)
INSERT INTO `users` (`id`, `full_name`, `email`, `password_hash`, `role_id`, `branch_id`, `is_active`) VALUES
('550e8400-e29b-41d4-a716-446655440003', 'Maria Fernanda Santos', '<EMAIL>', '$2a$12$GbHSAYzwroa7jMTvcc2rcO762/IJQoMZzCGE.p78MopGMmJavInLG', 4, 1, TRUE);

-- Técnico (senha: 123456789)
INSERT INTO `users` (`id`, `full_name`, `email`, `password_hash`, `role_id`, `branch_id`, `is_active`) VALUES
('550e8400-e29b-41d4-a716-446655440004', 'António José Pereira', '<EMAIL>', '$2a$12$GbHSAYzwroa7jMTvcc2rcO762/IJQoMZzCGE.p78MopGMmJavInLG', 5, 1, TRUE);

-- =============================================
-- SEED DOS CAIXAS FÍSICOS
-- =============================================

INSERT INTO `cash_registers` (`register_number`, `description`, `branch_id`, `status`) VALUES
('CAIXA001', 'Caixa Principal - Agência Central', 1, 'available'),
('CAIXA002', 'Caixa Secundário - Agência Central', 1, 'available'),
('CAIXA003', 'Caixa Empresarial - Agência Central', 1, 'available'),
('CAIXA004', 'Caixa Principal - Agência Talatona', 2, 'available'),
('CAIXA005', 'Caixa Principal - Agência Benguela', 3, 'available'),
('CAIXA006', 'Caixa Principal - Agência Huambo', 4, 'available');

-- =============================================
-- SEED DAS TAXAS DE CÂMBIO INICIAIS
-- =============================================

-- Taxas de câmbio para AOA (Kwanza)
INSERT INTO `exchange_rates` (`from_currency_id`, `to_currency_id`, `rate`, `effective_date`, `created_by`) VALUES
-- USD para AOA
(2, 1, 825.50, CURDATE(), '550e8400-e29b-41d4-a716-************'),
-- EUR para AOA
(3, 1, 900.75, CURDATE(), '550e8400-e29b-41d4-a716-************'),
-- GBP para AOA
(4, 1, 1050.25, CURDATE(), '550e8400-e29b-41d4-a716-************'),
-- AOA para USD
(1, 2, 0.00121, CURDATE(), '550e8400-e29b-41d4-a716-************'),
-- AOA para EUR
(1, 3, 0.00111, CURDATE(), '550e8400-e29b-41d4-a716-************'),
-- AOA para GBP
(1, 4, 0.00095, CURDATE(), '550e8400-e29b-41d4-a716-************');

-- =============================================
-- SEED DE CONFIGURAÇÕES DO SISTEMA
-- =============================================

INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`, `updated_by`) VALUES
('bank_name', 'Twins_Bank', 'Nome do banco', '550e8400-e29b-41d4-a716-************'),
('bank_code', 'TWINSBANK', 'Código do banco', '550e8400-e29b-41d4-a716-************'),
('daily_transfer_limit', '5000000', 'Limite diário de transferências em AOA', '550e8400-e29b-41d4-a716-************'),
('monthly_transfer_limit', '********', 'Limite mensal de transferências em AOA', '550e8400-e29b-41d4-a716-************'),
('cash_register_limit', '********', 'Limite máximo por caixa em AOA', '550e8400-e29b-41d4-a716-************'),
('session_timeout', '1440', 'Timeout de sessão em minutos', '550e8400-e29b-41d4-a716-************'),
('backup_retention_days', '30', 'Dias de retenção de backups', '550e8400-e29b-41d4-a716-************'),
('maintenance_mode', 'false', 'Modo de manutenção do sistema', '550e8400-e29b-41d4-a716-************');

-- =============================================
-- SEED DOS COFRES PRINCIPAIS
-- =============================================

INSERT INTO `main_vaults` (`branch_id`, `vault_code`, `name`, `description`, `current_balance`, `max_capacity`) VALUES
(1, 'VAULT-SEDE-001', 'Cofre Principal - Sede', 'Cofre principal da agência central (sede)', 0.00, *********.99),
(2, 'VAULT-TALATONA-001', 'Cofre Principal - Talatona', 'Cofre principal da agência de Talatona', 0.00, *********.99),
(3, 'VAULT-BENGUELA-001', 'Cofre Principal - Benguela', 'Cofre principal da agência de Benguela', 0.00, *********.99),
(4, 'VAULT-HUAMBO-001', 'Cofre Principal - Huambo', 'Cofre principal da agência de Huambo', 0.00, *********.99);

-- =============================================
-- NOTAS IMPORTANTES
-- =============================================

-- 1. Os hashes de senha são exemplos e devem ser gerados pela aplicação
-- 2. Os UUIDs dos utilizadores são fixos para facilitar testes
-- 3. As taxas de câmbio devem ser atualizadas regularmente
-- 4. O super admin deve alterar a senha no primeiro login
-- 5. Os utilizadores de demonstração devem ser removidos em produção
-- 6. Os cofres principais são criados automaticamente para cada agência
-- 7. O saldo inicial dos cofres deve ser definido através da interface de administração
