import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, XCircle } from 'lucide-react';

interface RejectApplicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  isLoading?: boolean;
  applicationId?: string;
  clientName?: string;
}

const RejectApplicationModal: React.FC<RejectApplicationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
  applicationId,
  clientName
}) => {
  const [reason, setReason] = useState('');
  const [error, setError] = useState('');

  // Validar formulário
  const validateForm = (): boolean => {
    if (!reason.trim()) {
      setError('O motivo da rejeição é obrigatório');
      return false;
    }
    if (reason.trim().length < 10) {
      setError('O motivo deve ter pelo menos 10 caracteres');
      return false;
    }
    setError('');
    return true;
  };

  // Submeter formulário
  const handleSubmit = () => {
    if (!validateForm()) return;
    onConfirm(reason.trim());
  };

  // Resetar formulário ao fechar
  const handleClose = () => {
    if (!isLoading) {
      setReason('');
      setError('');
      onClose();
    }
  };

  // Motivos pré-definidos para facilitar a seleção
  const predefinedReasons = [
    'Documentação incompleta ou inválida',
    'Dados do cliente inconsistentes',
    'Cliente não atende aos critérios mínimos',
    'Informações financeiras insuficientes',
    'Documentos com validade expirada',
    'Assinatura não confere com documento',
    'Cliente já possui conta do mesmo tipo'
  ];

  const handlePredefinedReason = (predefinedReason: string) => {
    setReason(predefinedReason);
    setError('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[95vw] w-full sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            Rejeitar Solicitação de Conta
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 px-1">
          {/* Informações da Solicitação - Removido o ID do cliente */}
          {clientName && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm font-medium text-red-800 break-words">Cliente: {clientName}</p>
            </div>
          )}

          {/* Motivos Pré-definidos */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Motivos Comuns (clique para selecionar):</Label>
            <div className="grid grid-cols-1 gap-2 max-h-48 overflow-y-auto">
              {predefinedReasons.map((predefinedReason, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="justify-start text-left h-auto py-2 px-3 text-xs whitespace-normal break-words"
                  onClick={() => handlePredefinedReason(predefinedReason)}
                  disabled={isLoading}
                >
                  {predefinedReason}
                </Button>
              ))}
            </div>
          </div>

          {/* Campo de Motivo */}
          <div className="space-y-2">
            <Label htmlFor="rejection-reason">
              Motivo da Rejeição <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="rejection-reason"
              placeholder="Descreva detalhadamente o motivo da rejeição..."
              value={reason}
              onChange={(e) => {
                setReason(e.target.value);
                if (error) setError('');
              }}
              className={`min-h-[100px] resize-none ${error ? 'border-red-500' : ''}`}
              disabled={isLoading}
            />
            {error && (
              <p className="text-sm text-red-500 break-words">{error}</p>
            )}
            <p className="text-xs text-gray-500 break-words">
              Mínimo de 10 caracteres. Seja específico para ajudar o cliente a entender a rejeição.
            </p>
          </div>

          {/* Alerta de Confirmação */}
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <AlertDescription className="break-words">
              <strong>Atenção:</strong> Esta ação não pode ser desfeita. O cliente será notificado sobre a rejeição e o motivo informado.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2 pt-4">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
            className="w-full sm:flex-1"
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleSubmit}
            disabled={isLoading || !reason.trim()}
            className="w-full sm:flex-1"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Confirmar Rejeição
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RejectApplicationModal;
