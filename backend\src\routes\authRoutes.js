const express = require('express');
const Joi = require('joi');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const {
  hashPassword,
  comparePassword,
  generateTokenPair,
  refreshAccessToken,
  removeUserSession
} = require('../auth/jwtUtils');
const logger = require('../core/logger');
const { getCurrentTimestamp } = require('../utils/dateUtils');

const router = express.Router();

// Validação de esquemas
const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Email deve ter um formato válido',
    'any.required': 'Email é obrigatório'
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Senha deve ter pelo menos 6 caracteres',
    'any.required': '<PERSON>ha é obrigatória'
  })
});

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required().messages({
    'any.required': 'Token de refresh é obrigatório'
  })
});

/**
 * POST /api/auth/login
 * Autenticação de utilizador
 */
router.post('/login', catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = loginSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { email, password } = value;

  // 2. Verificar se o utilizador existe
  const users = await executeQuery(
    `SELECT u.*, r.name as role_name, b.name as branch_name 
     FROM users u 
     JOIN roles r ON u.role_id = r.id 
     LEFT JOIN branches b ON u.branch_id = b.id 
     WHERE u.email = ?`,
    [email]
  );

  if (!users || users.length === 0) {
    logger.security(`Tentativa de login com email inexistente: ${email}`, { ip: req.ip });

    // Log de auditoria para tentativa de login falhada
    await executeQuery(`
      INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent, created_at)
      VALUES (NULL, 'LOGIN_FAILED', 'users', NULL, ?, ?, ?, ?)
    `, [
      JSON.stringify({ email, reason: 'email_not_found' }),
      req.ip || 'unknown',
      req.get('User-Agent') || 'unknown',
      getCurrentTimestamp()
    ]);

    return next(new AppError('Credenciais inválidas', 401, 'INVALID_CREDENTIALS'));
  }

  const user = users[0];

  // 3. Verificar se o utilizador está ativo
  if (!user.is_active) {
    logger.security(`Tentativa de login com conta inativa: ${email}`, { ip: req.ip });

    // Log de auditoria para tentativa de login com conta inativa
    await executeQuery(`
      INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent, created_at)
      VALUES (?, 'LOGIN_FAILED', 'users', ?, ?, ?, ?, ?)
    `, [
      user.id,
      user.id,
      JSON.stringify({ email, reason: 'account_inactive' }),
      req.ip || 'unknown',
      req.get('User-Agent') || 'unknown',
      getCurrentTimestamp()
    ]);

    return next(new AppError('Conta inativa. Contacte o administrador.', 401, 'ACCOUNT_INACTIVE'));
  }

  // 4. Verificar senha
  const isPasswordValid = await comparePassword(password, user.password_hash);
  if (!isPasswordValid) {
    logger.security(`Tentativa de login com senha incorreta: ${email}`, { ip: req.ip });

    // Log de auditoria para tentativa de login com senha incorreta
    await executeQuery(`
      INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent, created_at)
      VALUES (?, 'LOGIN_FAILED', 'users', ?, ?, ?, ?, ?)
    `, [
      user.id,
      user.id,
      JSON.stringify({ email, reason: 'invalid_password' }),
      req.ip || 'unknown',
      req.get('User-Agent') || 'unknown',
      getCurrentTimestamp()
    ]);

    return next(new AppError('Credenciais inválidas', 401, 'INVALID_CREDENTIALS'));
  }

  // 5. Atualizar último login
  await executeQuery(
    'UPDATE users SET last_login = ? WHERE id = ?',
    [getCurrentTimestamp(), user.id]
  );

  // 6. Gerar tokens
  const tokenData = await generateTokenPair(user);

  logger.auth(`Login realizado com sucesso: ${email}`, {
    userId: user.id,
    role: user.role_name,
    ip: req.ip
  });

  // Log de auditoria para login bem-sucedido
  await executeQuery(`
    INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent, created_at)
    VALUES (?, 'LOGIN_SUCCESS', 'users', ?, ?, ?, ?, ?)
  `, [
    user.id,
    user.id,
    JSON.stringify({ email, role: user.role_name, branch: user.branch_name }),
    req.ip || 'unknown',
    req.get('User-Agent') || 'unknown',
    getCurrentTimestamp()
  ]);

  // 7. Resposta de sucesso
  res.status(200).json({
    status: 'success',
    message: 'Login realizado com sucesso',
    data: {
      user: {
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role_name,
        branch: user.branch_name ? {
          id: user.branch_id,
          name: user.branch_name,
          code: user.branch_code
        } : null,
        is_active: user.is_active,
        last_login: user.last_login,
        created_at: user.created_at
      },
      accessToken: tokenData.accessToken,
      refreshToken: tokenData.refreshToken,
      expiresIn: tokenData.expiresIn,
      sessionId: tokenData.sessionId
    }
  });
}));

/**
 * POST /api/auth/refresh
 * Renovar token de acesso
 */
router.post('/refresh', catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = refreshTokenSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { refreshToken } = value;

  try {
    // 2. Renovar token
    const tokenData = await refreshAccessToken(refreshToken);

    res.status(200).json({
      status: 'success',
      message: 'Token renovado com sucesso',
      data: {
        accessToken: tokenData.accessToken,
        expiresIn: tokenData.expiresIn
      }
    });

  } catch (error) {
    logger.security(`Tentativa de renovação com token inválido`, { 
      error: error.message,
      ip: req.ip 
    });
    return next(new AppError('Token de refresh inválido', 401, 'INVALID_REFRESH_TOKEN'));
  }
}));

/**
 * POST /api/auth/logout
 * Logout de utilizador
 */
router.post('/logout', catchAsync(async (req, res, next) => {
  let token;
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    return next(new AppError('Token de acesso necessário', 401, 'NO_TOKEN'));
  }

  try {
    // Decodificar token para obter ID do utilizador
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Remover sessão
    await removeUserSession(decoded.id, token);

    logger.auth(`Logout realizado: ${decoded.email}`, {
      userId: decoded.id,
      ip: req.ip
    });

    // Log de auditoria para logout
    await executeQuery(`
      INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent, created_at)
      VALUES (?, 'LOGOUT', 'users', ?, ?, ?, ?, ?)
    `, [
      decoded.id,
      decoded.id,
      JSON.stringify({ email: decoded.email }),
      req.ip || 'unknown',
      req.get('User-Agent') || 'unknown',
      getCurrentTimestamp()
    ]);

    res.status(200).json({
      status: 'success',
      message: 'Logout realizado com sucesso'
    });

  } catch (error) {
    // Mesmo com token inválido, considerar logout bem-sucedido
    res.status(200).json({
      status: 'success',
      message: 'Logout realizado com sucesso'
    });
  }
}));

/**
 * GET /api/auth/me
 * Obter dados do utilizador autenticado
 */
router.get('/me', require('../auth/middleware').authenticate, catchAsync(async (req, res) => {
  // Obter dados completos do utilizador
  const users = await executeQuery(
    `SELECT u.*, r.name as role_name, b.name as branch_name, b.code as branch_code
     FROM users u 
     JOIN roles r ON u.role_id = r.id 
     LEFT JOIN branches b ON u.branch_id = b.id 
     WHERE u.id = ?`,
    [req.user.id]
  );

  const user = users[0];

  res.status(200).json({
    status: 'success',
    data: {
      user: {
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role_name,
        branch: {
          id: user.branch_id,
          name: user.branch_name,
          code: user.branch_code
        },
        is_active: user.is_active,
        last_login: user.last_login,
        created_at: user.created_at
      }
    }
  });
}));

/**
 * GET /api/auth/check
 * Verificar se o token é válido
 */
router.get('/check', require('../auth/middleware').authenticate, (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Token válido',
    data: {
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role_name
      }
    }
  });
});

module.exports = router;
