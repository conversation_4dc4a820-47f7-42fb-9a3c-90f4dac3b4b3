# Correções para Deploy na Vercel - K-Bank

## 🔍 Problemas Identificados

### Backend
1. **Erro de Filesystem**: Tentativa de criar diretórios em ambiente serverless (read-only)
   - Erro: `ENOENT: no such file or directory, mkdir '/var/task/backend/uploads'`
2. **Inicialização Incorreta**: `app.listen()` não funciona em ambiente serverless
3. **Estrutura Inadequada**: Faltava handler serverless apropriado para Vercel
4. **Ficheiros Estáticos**: Tentativa de servir uploads de filesystem local

### Frontend
- Ficheiro `.npmrc` vazio (necessário para dependências opcionais do Rollup)

## ✅ Correções Implementadas

### 1. Backend - uploadConfig.js
**Alteração**: Detecção de ambiente serverless e prevenção de criação de diretórios

```javascript
// Verificar se estamos em ambiente serverless
const isServerless = process.env.VERCEL || process.env.AWS_LAMBDA_FUNCTION_NAME || process.env.FUNCTIONS_WORKER_RUNTIME;

// Criar diretórios apenas em desenvolvimento
const createUploadDirs = () => {
  if (isServerless) {
    logger.info('Ambiente serverless detectado - uploads serão geridos pelo Supabase');
    return;
  }
  // ... resto do código
};
```

**Resultado**: Uploads são geridos pelo Supabase Storage em produção, filesystem local apenas em desenvolvimento.

### 2. Backend - server.js
**Alterações**:
- Removida linha que servia ficheiros estáticos de `backend/uploads`
- Separada configuração da app de inicialização do servidor
- Criado middleware de inicialização lazy da base de dados
- `app.listen()` só é chamado em ambiente não-serverless

```javascript
// Middleware para garantir que a DB está inicializada
app.use(async (req, res, next) => {
  if (req.path === '/api/health') {
    return next();
  }
  
  try {
    await ensureDatabaseInitialized();
    next();
  } catch (error) {
    res.status(503).json({
      error: 'Serviço temporariamente indisponível',
      message: 'Erro ao conectar à base de dados'
    });
  }
});

// Iniciar servidor apenas se NÃO estivermos em ambiente serverless
if (!isServerless) {
  startServer();
}

// Exportar a app configurada (para uso em serverless functions)
module.exports = app;
```

**Resultado**: App Express funciona tanto em modo tradicional (desenvolvimento) quanto em serverless (produção).

### 3. Backend - api/index.js (NOVO)
**Criado**: Handler serverless para Vercel

```javascript
// Importar a aplicação Express já configurada
const app = require('../src/server');

// Exportar a app para a Vercel
module.exports = app;
```

**Resultado**: Ponto de entrada correto para Vercel Serverless Functions.

### 4. Backend - vercel.json
**Alteração**: Atualizado para apontar para o novo handler

```json
{
  "builds": [
    {
      "src": "api/index.js",  // ← Alterado de "src/server.js"
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/api/index.js"  // ← Alterado de "/src/server.js"
    }
  ]
}
```

**Resultado**: Vercel usa o handler correto para processar requests.

### 5. Frontend - .npmrc
**Alteração**: Adicionado conteúdo ao ficheiro

```
# Garantir que dependências opcionais são instaladas
# Necessário para o Rollup funcionar corretamente na Vercel
optional=true
```

**Resultado**: Dependências opcionais do Rollup são instaladas corretamente durante o build.

## 🚀 Próximos Passos para Deploy

### 1. Commit e Push das Alterações
```bash
git add .
git commit -m "fix: corrigir deploy na Vercel (serverless compatibility)"
git push origin main
```

### 2. Verificar Variáveis de Ambiente na Vercel

#### Backend (twins-bank-backend)
Confirmar que estas variáveis estão configuradas:
```
DB_HOST=mysql-doublec.alwaysdata.net
DB_PORT=3306
DB_NAME=doublec_twins_bank
DB_USER=doublec
DB_PASSWORD=CarlosCesar@2022

JWT_SECRET=ad042f36a25c3488c6582a47622021deb039f04e8191a32c340010dd737412bf12688964f438c356b11c3a47c3a6a27d7c07551a8c72cc90df749555eb74f6e1
JWT_EXPIRES_IN=24h

NODE_ENV=production
PORT=3000

CORS_ORIGIN=https://twins-bank-frontend.vercel.app

# Supabase (para uploads)
SUPABASE_URL=<sua_url>
SUPABASE_KEY=<sua_chave>
```

#### Frontend (twins-bank-frontend)
```
VITE_API_URL=https://twins-bank-backend.vercel.app
VITE_NODE_ENV=production
```

### 3. Re-deploy
- A Vercel fará deploy automático após o push
- Ou force um novo deploy no dashboard da Vercel

### 4. Testar Endpoints

#### Backend
```bash
# Health check básico
curl https://twins-bank-backend.vercel.app/api/health

# Health check detalhado (com DB)
curl https://twins-bank-backend.vercel.app/api/health/detailed
```

#### Frontend
Aceder a: `https://twins-bank-frontend.vercel.app`

## 📊 Verificações Pós-Deploy

### Backend
- [ ] Endpoint `/api/health` retorna status 200
- [ ] Endpoint `/api/health/detailed` mostra conexão com DB
- [ ] Logs da Vercel não mostram erros de filesystem
- [ ] Logs da Vercel não mostram erros de `app.listen()`

### Frontend
- [ ] Página carrega sem erros
- [ ] Build completa sem erros de Rollup
- [ ] Aplicação conecta ao backend

## 🔧 Resolução de Problemas

### Se o backend ainda falhar:
1. Verificar logs na Vercel Dashboard
2. Confirmar que todas as variáveis de ambiente estão configuradas
3. Testar conexão com base de dados (endpoint `/api/health/detailed`)
4. Verificar se o ficheiro `api/index.js` foi criado corretamente

### Se o frontend falhar no build:
1. Verificar se `.npmrc` tem o conteúdo correto
2. Limpar cache: Settings → General → Clear Build Cache
3. Re-deploy

### Se houver erros de CORS:
1. Verificar `CORS_ORIGIN` no backend
2. Confirmar URLs exatas (sem trailing slash)
3. Re-deploy backend após alteração

## 📝 Notas Importantes

- **Uploads**: Todos os uploads são geridos pelo Supabase Storage em produção
- **Base de Dados**: Inicialização lazy - DB é inicializada no primeiro request
- **Logs**: Em produção, logs vão apenas para console (visíveis na Vercel Dashboard)
- **Filesystem**: Backend não tenta criar diretórios em ambiente serverless
- **Servidor**: `app.listen()` não é chamado em ambiente serverless

## ✨ Melhorias Implementadas

1. **Compatibilidade Serverless**: Backend totalmente compatível com ambiente serverless
2. **Inicialização Inteligente**: DB inicializada apenas quando necessário
3. **Gestão de Uploads**: Supabase Storage em produção, filesystem local em desenvolvimento
4. **Logs Otimizados**: Logs estruturados para melhor debugging na Vercel
5. **Health Checks**: Endpoints de saúde para monitorização

