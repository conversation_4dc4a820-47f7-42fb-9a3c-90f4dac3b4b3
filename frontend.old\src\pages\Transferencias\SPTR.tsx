
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, AlertTriangle } from 'lucide-react';

const TransferenciaSPTR = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Transferências SPTR</h1>
          <p className="text-gray-600">Sistema de Pagamentos em Tempo Real</p>
        </div>
      </div>

      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-700">
            <AlertTriangle className="h-5 w-5" />
            Funcionalidade em Desenvolvimento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-gray-400" />
              <div>
                <h3 className="font-semibold text-gray-700">Sistema SPTR</h3>
                <p className="text-gray-600">
                  O módulo de transferências SPTR está atualmente em desenvolvimento e será disponibilizado em breve.
                </p>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-gray-800 mb-2">Funcionalidades Previstas:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Transferências em tempo real para outros bancos</li>
                <li>• Integração com o Sistema de Pagamentos nacional</li>
                <li>• Verificação automática de limites</li>
                <li>• Confirmação instantânea de transações</li>
                <li>• Histórico detalhado de operações SPTR</li>
              </ul>
            </div>

            <Button disabled className="w-full" size="lg">
              Funcionalidade Indisponível
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TransferenciaSPTR;
